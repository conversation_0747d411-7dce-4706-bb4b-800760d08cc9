# 批量关键词追加API文档

## 概述

批量关键词追加功能允许用户为多个文本块同时添加重要关键词(important_kwd)。新添加的关键词会与现有关键词合并，自动去除重复项。

## API端点

### 1. 批量添加关键词

**端点:** `POST /api/v1/chunk/batch_append_keywords`

**描述:** 为多个chunk批量添加关键词，支持预览和执行两种模式。

#### 请求参数

```json
{
  "doc_id": "string",           // 必需：文档ID
  "chunk_ids": ["string"],      // 必需：要更新的chunk ID列表
  "keywords": ["string"],       // 必需：要添加的关键词列表
  "preview": false              // 可选：是否仅预览不执行更新，默认false
}
```

#### 响应格式

**执行模式 (preview=false):**
```json
{
  "retcode": 0,
  "retmsg": "success",
  "data": {
    "success_count": 10,        // 成功处理的chunk数量
    "failed_count": 0,          // 失败的chunk数量
    "failed_chunks": [],        // 失败的chunk详情
    "total_chunks": 10          // 总chunk数量
  }
}
```

**预览模式 (preview=true):**
```json
{
  "retcode": 0,
  "retmsg": "success", 
  "data": {
    "preview_data": {
      "chunk_id_1": {
        "original_keywords": ["existing1", "existing2"],
        "merged_keywords": ["existing1", "existing2", "new1", "new2"]
      },
      "chunk_id_2": {
        "original_keywords": [],
        "merged_keywords": ["new1", "new2"]
      }
    },
    "total_chunks": 2
  }
}
```

### 2. 预览批量关键词合并

**端点:** `POST /api/v1/chunk/batch_preview_keywords`

**描述:** 专门用于预览批量关键词合并效果的端点。

#### 请求参数

```json
{
  "doc_id": "string",           // 必需：文档ID
  "chunk_ids": ["string"],      // 必需：要更新的chunk ID列表
  "keywords": ["string"]        // 必需：要添加的关键词列表
}
```

#### 响应格式

```json
{
  "retcode": 0,
  "retmsg": "success",
  "data": {
    "preview_data": {
      "chunk_id": {
        "original_keywords": ["existing1"],
        "merged_keywords": ["existing1", "new1", "new2"]
      }
    },
    "statistics": {
      "total_chunks": 2,          // 请求的总chunk数量
      "chunks_found": 2,          // 找到的chunk数量
      "total_new_keywords": 3,    // 将要新增的关键词总数
      "new_keywords": ["new1", "new2"]  // 新增的关键词列表
    }
  }
}
```

## 使用示例

### 示例1：预览关键词合并效果

```bash
curl -X POST "http://localhost:9380/api/v1/chunk/batch_preview_keywords" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "doc_id": "doc_123",
    "chunk_ids": ["chunk_1", "chunk_2", "chunk_3"],
    "keywords": ["重要", "关键", "核心"]
  }'
```

### 示例2：执行批量关键词添加

```bash
curl -X POST "http://localhost:9380/api/v1/chunk/batch_append_keywords" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "doc_id": "doc_123", 
    "chunk_ids": ["chunk_1", "chunk_2", "chunk_3"],
    "keywords": ["重要", "关键", "核心"],
    "preview": false
  }'
```

### 示例3：先预览再执行

```bash
# 第一步：预览效果
curl -X POST "http://localhost:9380/api/v1/chunk/batch_append_keywords" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "doc_id": "doc_123",
    "chunk_ids": ["chunk_1", "chunk_2"],
    "keywords": ["新关键词1", "新关键词2"],
    "preview": true
  }'

# 第二步：确认后执行
curl -X POST "http://localhost:9380/api/v1/chunk/batch_append_keywords" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "doc_id": "doc_123",
    "chunk_ids": ["chunk_1", "chunk_2"],
    "keywords": ["新关键词1", "新关键词2"],
    "preview": false
  }'
```

## 错误处理

### 常见错误码

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 400 | chunk_ids必须是非空列表 | chunk_ids参数为空或格式错误 |
| 400 | keywords必须是非空列表 | keywords参数为空或格式错误 |
| 400 | keywords格式无效 | keywords包含非字符串或空字符串 |
| 400 | chunk数量不能超过1000个 | 单次操作的chunk数量超过限制 |
| 400 | 关键词数量不能超过100个 | 单次添加的关键词数量超过限制 |
| 404 | 文档不存在 | 指定的doc_id不存在 |
| 404 | 租户不存在 | 无法获取文档对应的租户信息 |
| 403 | 权限不足 | 用户无权访问指定文档 |
| 500 | 系统内部错误 | 服务器内部错误 |

### 部分失败处理

当批量操作中部分chunk处理失败时，系统会继续处理其他chunk，并在响应中返回详细的失败信息：

```json
{
  "retcode": 0,
  "retmsg": "success",
  "data": {
    "success_count": 2,
    "failed_count": 1,
    "failed_chunks": [
      {
        "chunk_id": "chunk_3",
        "error": "Chunk不存在"
      }
    ],
    "total_chunks": 3
  }
}
```

## 性能和限制

### 操作限制

- **单次批量操作的chunk数量限制：** 最多1000个
- **单次添加的关键词数量限制：** 最多100个
- **关键词长度限制：** 每个关键词不能为空字符串
- **请求频率限制：** 建议控制在合理范围内，避免对系统造成过大压力

### 性能优化建议

1. **分批处理：** 对于大量chunk，建议分批处理，每批不超过100个chunk
2. **预览确认：** 对于重要操作，建议先使用预览功能确认效果
3. **错误重试：** 对于部分失败的情况，可以针对失败的chunk重新执行操作
4. **合理频率：** 避免过于频繁的批量操作请求

## 最佳实践

### 1. 使用预览功能

在执行批量操作前，建议先使用预览功能确认合并效果：

```python
import requests

# 预览效果
preview_response = requests.post(
    "http://localhost:9380/api/v1/chunk/batch_preview_keywords",
    headers={"Authorization": "Bearer YOUR_TOKEN"},
    json={
        "doc_id": "doc_123",
        "chunk_ids": ["chunk_1", "chunk_2"],
        "keywords": ["重要", "核心"]
    }
)

# 检查预览结果
if preview_response.status_code == 200:
    preview_data = preview_response.json()["data"]
    print(f"将影响 {preview_data['statistics']['chunks_found']} 个chunk")
    print(f"将新增 {preview_data['statistics']['total_new_keywords']} 个关键词")
    
    # 确认后执行
    execute_response = requests.post(
        "http://localhost:9380/api/v1/chunk/batch_append_keywords",
        headers={"Authorization": "Bearer YOUR_TOKEN"},
        json={
            "doc_id": "doc_123",
            "chunk_ids": ["chunk_1", "chunk_2"],
            "keywords": ["重要", "核心"],
            "preview": False
        }
    )
```

### 2. 错误处理和重试

```python
def batch_append_keywords_with_retry(doc_id, chunk_ids, keywords, max_retries=3):
    """带重试机制的批量关键词添加"""
    
    for attempt in range(max_retries):
        response = requests.post(
            "http://localhost:9380/api/v1/chunk/batch_append_keywords",
            headers={"Authorization": "Bearer YOUR_TOKEN"},
            json={
                "doc_id": doc_id,
                "chunk_ids": chunk_ids,
                "keywords": keywords,
                "preview": False
            }
        )
        
        if response.status_code == 200:
            result = response.json()["data"]
            
            if result["failed_count"] == 0:
                print(f"所有 {result['success_count']} 个chunk处理成功")
                return True
            else:
                # 提取失败的chunk_ids进行重试
                failed_chunk_ids = [fc["chunk_id"] for fc in result["failed_chunks"]]
                print(f"第{attempt+1}次尝试：{result['success_count']}个成功，{result['failed_count']}个失败")
                
                if attempt < max_retries - 1:
                    chunk_ids = failed_chunk_ids  # 下次只重试失败的chunk
                    time.sleep(1)  # 等待1秒后重试
                else:
                    print(f"重试{max_retries}次后仍有{len(failed_chunk_ids)}个chunk失败")
                    return False
        else:
            print(f"请求失败：{response.status_code} - {response.text}")
            if attempt < max_retries - 1:
                time.sleep(2)  # 等待2秒后重试
            else:
                return False
    
    return False
```

### 3. 分批处理大量数据

```python
def batch_process_large_dataset(doc_id, all_chunk_ids, keywords, batch_size=100):
    """分批处理大量chunk"""
    
    total_chunks = len(all_chunk_ids)
    processed = 0
    failed_chunks = []
    
    for i in range(0, total_chunks, batch_size):
        batch_chunk_ids = all_chunk_ids[i:i+batch_size]
        
        print(f"处理第 {i//batch_size + 1} 批，共 {len(batch_chunk_ids)} 个chunk")
        
        response = requests.post(
            "http://localhost:9380/api/v1/chunk/batch_append_keywords",
            headers={"Authorization": "Bearer YOUR_TOKEN"},
            json={
                "doc_id": doc_id,
                "chunk_ids": batch_chunk_ids,
                "keywords": keywords,
                "preview": False
            }
        )
        
        if response.status_code == 200:
            result = response.json()["data"]
            processed += result["success_count"]
            failed_chunks.extend(result["failed_chunks"])
            
            print(f"本批处理完成：{result['success_count']}个成功，{result['failed_count']}个失败")
        else:
            print(f"本批处理失败：{response.status_code} - {response.text}")
            failed_chunks.extend([{"chunk_id": cid, "error": "请求失败"} for cid in batch_chunk_ids])
    
    print(f"总计处理完成：{processed}/{total_chunks}个成功，{len(failed_chunks)}个失败")
    return processed, failed_chunks
```

## 故障排除

### 常见问题

1. **权限错误**
   - 确认用户有权限访问指定文档
   - 检查认证token是否有效

2. **chunk不存在**
   - 确认chunk_id是否正确
   - 检查chunk是否已被删除

3. **参数验证失败**
   - 确认所有必需参数都已提供
   - 检查参数格式是否正确

4. **系统性能问题**
   - 减少单次操作的chunk数量
   - 增加请求间隔时间
   - 使用分批处理策略

### 调试技巧

1. **使用预览模式调试**
   ```bash
   # 先预览看看会发生什么
   curl -X POST "http://localhost:9380/api/v1/chunk/batch_append_keywords" \
     -H "Content-Type: application/json" \
     -d '{"doc_id": "test", "chunk_ids": ["chunk1"], "keywords": ["test"], "preview": true}'
   ```

2. **检查日志**
   - 查看应用日志了解详细错误信息
   - 关注批量处理过程中的警告和错误

3. **逐步测试**
   - 先用单个chunk测试
   - 再逐步增加chunk数量
   - 确认每个步骤都正常工作