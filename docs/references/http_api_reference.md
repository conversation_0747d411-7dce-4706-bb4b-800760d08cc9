---
sidebar_position: 4
slug: /http_api_reference
---

# HTTP API

RAGFlow RESTful API 的完整参考文档。在开始之前，请确保您已[准备好用于身份验证的 RAGFlow API 密钥](../guides/models/llm_api_key_setup.md)。

---

## 错误代码

---

| 代码 | 消息                  | 描述           |
| ---- | --------------------- | -------------- |
| 400  | Bad Request           | 无效的请求参数 |
| 401  | Unauthorized          | 未授权访问     |
| 403  | Forbidden             | 访问被拒绝     |
| 404  | Not Found             | 资源未找到     |
| 500  | Internal Server Error | 服务器内部错误 |
| 1001 | Invalid Chunk ID      | 无效的块 ID    |
| 1002 | Chunk Update Failed   | 块更新失败     |

---

## OpenAI 兼容 API

---

### 创建聊天完成

**POST** `/api/v1/chats_openai/{chat_id}/chat/completions`

为给定的聊天对话创建模型响应。

此 API 遵循与 OpenAI API 相同的请求和响应格式。它允许您以类似于使用 [OpenAI API](https://platform.openai.com/docs/api-reference/chat/create) 的方式与模型交互。

##### 请求详情

- Method: POST
- URL: `/api/v1/chats_openai/{chat_id}/chat/completions`
- Headers:
  - `'content-Type: application/json'`
  - `'Authorization: Bearer <YOUR_API_KEY>'`
- Body:
  - `"model"`: `string`
  - `"messages"`: `object list`
  - `"stream"`: `boolean`

##### 请求示例

```bash
curl --request POST \
     --url http://{address}/api/v1/chats_openai/{chat_id}/chat/completions \
     --header 'Content-Type: application/json' \
     --header 'Authorization: Bearer <YOUR_API_KEY>' \
     --data '{
        "model": "model",
        "messages": [{"role": "user", "content": "Say this is a test!"}],
        "stream": true
      }'
```

##### 请求参数

- `model` (*Body parameter*) `string`, *必需*
  用于生成响应的模型。服务器会自动解析此参数，因此您现在可以将其设置为任何值。

- `messages` (*Body parameter*) `list[object]`, *必需*
  用于生成响应的历史聊天消息列表。必须包含至少一条具有 `user` 角色的消息。

- `stream` (*Body parameter*) `boolean`
  是否以流的形式接收响应。如果您希望一次性接收完整响应而不是流式响应，请明确设置为 `false`。

##### 响应结果

流式响应：

```json
{
    "id": "chatcmpl-3a9c3572f29311efa69751e139332ced",
    "choices": [
        {
            "delta": {
                "content": "This is a test. If you have any specific questions or need information, feel",
                "role": "assistant",
                "function_call": null,
                "tool_calls": null
            },
            "finish_reason": null,
            "index": 0,
            "logprobs": null
        }
    ],
    "created": 1740543996,
    "model": "model",
    "object": "chat.completion.chunk",
    "system_fingerprint": "",
    "usage": null
}
// omit duplicated information
{"choices":[{"delta":{"content":" free to ask, and I will do my best to provide an answer based on","role":"assistant"}}]}
{"choices":[{"delta":{"content":" the knowledge I have. If your question is unrelated to the provided knowledge base,","role":"assistant"}}]}
{"choices":[{"delta":{"content":" I will let you know.","role":"assistant"}}]}
// the last chunk
{
    "id": "chatcmpl-3a9c3572f29311efa69751e139332ced",
    "choices": [
        {
            "delta": {
                "content": null,
                "role": "assistant",
                "function_call": null,
                "tool_calls": null
            },
            "finish_reason": "stop",
            "index": 0,
            "logprobs": null
        }
    ],
    "created": 1740543996,
    "model": "model",
    "object": "chat.completion.chunk",
    "system_fingerprint": "",
    "usage": {
        "prompt_tokens": 18,
        "completion_tokens": 225,
        "total_tokens": 243
    }
}
```

非流式响应：

```json
{
    "choices":[
        {
            "finish_reason":"stop",
            "index":0,
            "logprobs":null,
            "message":{
                "content":"This is a test. If you have any specific questions or need information, feel free to ask, and I will do my best to provide an answer based on the knowledge I have. If your question is unrelated to the provided knowledge base, I will let you know.",
                "role":"assistant"
            }
        }
    ],
    "created":1740543499,
    "id":"chatcmpl-3a9c3572f29311efa69751e139332ced",
    "model":"model",
    "object":"chat.completion",
    "usage":{
        "completion_tokens":246,
        "completion_tokens_details":{
            "accepted_prediction_tokens":246,
            "reasoning_tokens":18,
            "rejected_prediction_tokens":0
        },
        "prompt_tokens":18,
        "total_tokens":264
    }
}
```

失败示例：

```json
{
  "code": 102,
  "message": "The last content of this conversation is not from user."
}
```

---

### 创建智能体完成

**POST** `/api/v1/agents_openai/{agent_id}/chat/completions`

为给定的聊天对话创建模型响应。

此 API 遵循与 OpenAI API 相同的请求和响应格式。它允许您以类似于使用 [OpenAI API](https://platform.openai.com/docs/api-reference/chat/create) 的方式与模型交互。

##### 请求详情

- Method: POST
- URL: `/api/v1/agents_openai/{agent_id}/chat/completions`
- Headers:
  - `'content-Type: application/json'`
  - `'Authorization: Bearer <YOUR_API_KEY>'`
- Body:
  - `"model"`: `string`
  - `"messages"`: `object list`
  - `"stream"`: `boolean`

##### 请求示例

```bash
curl --request POST \
     --url http://{address}/api/v1/agents_openai/{agent_id}/chat/completions \
     --header 'Content-Type: application/json' \
     --header 'Authorization: Bearer <YOUR_API_KEY>' \
     --data '{
        "model": "model",
        "messages": [{"role": "user", "content": "Say this is a test!"}],
        "stream": true
      }'
```

##### 请求参数

- `model` (*Body parameter*) `string`, *必需*
  用于生成响应的模型。服务器会自动解析此参数，因此您现在可以将其设置为任何值。

- `messages` (*Body parameter*) `list[object]`, *必需*
  用于生成响应的历史聊天消息列表。必须包含至少一条具有 `user` 角色的消息。

- `stream` (*Body parameter*) `boolean`
  是否以流的形式接收响应。如果您希望一次性接收完整响应而不是流式响应，请明确设置为 `false`。

##### 响应结果

流式响应：

```json
{
    "id": "chatcmpl-3a9c3572f29311efa69751e139332ced",
    "choices": [
        {
            "delta": {
                "content": "This is a test. If you have any specific questions or need information, feel",
                "role": "assistant",
                "function_call": null,
                "tool_calls": null
            },
            "finish_reason": null,
            "index": 0,
            "logprobs": null
        }
    ],
    "created": 1740543996,
    "model": "model",
    "object": "chat.completion.chunk",
    "system_fingerprint": "",
    "usage": null
}
// omit duplicated information
{"choices":[{"delta":{"content":" free to ask, and I will do my best to provide an answer based on","role":"assistant"}}]}
{"choices":[{"delta":{"content":" the knowledge I have. If your question is unrelated to the provided knowledge base,","role":"assistant"}}]}
{"choices":[{"delta":{"content":" I will let you know.","role":"assistant"}}]}
// the last chunk
{
    "id": "chatcmpl-3a9c3572f29311efa69751e139332ced",
    "choices": [
        {
            "delta": {
                "content": null,
                "role": "assistant",
                "function_call": null,
                "tool_calls": null
            },
            "finish_reason": "stop",
            "index": 0,
            "logprobs": null
        }
    ],
    "created": 1740543996,
    "model": "model",
    "object": "chat.completion.chunk",
    "system_fingerprint": "",
    "usage": {
        "prompt_tokens": 18,
        "completion_tokens": 225,
        "total_tokens": 243
    }
}
```

非流式响应：

```json
{
    "choices":[
        {
            "finish_reason":"stop",
            "index":0,
            "logprobs":null,
            "message":{
                "content":"This is a test. If you have any specific questions or need information, feel free to ask, and I will do my best to provide an answer based on the knowledge I have. If your question is unrelated to the provided knowledge base, I will let you know.",
                "role":"assistant"
            }
        }
    ],
    "created":1740543499,
    "id":"chatcmpl-3a9c3572f29311efa69751e139332ced",
    "model":"model",
    "object":"chat.completion",
    "usage":{
        "completion_tokens":246,
        "completion_tokens_details":{
            "accepted_prediction_tokens":246,
            "reasoning_tokens":18,
            "rejected_prediction_tokens":0
        },
        "prompt_tokens":18,
        "total_tokens":264
    }
}
```

失败示例：

```json
{
  "code": 102,
  "message": "The last content of this conversation is not from user."
}
```

## 数据集管理

---

### 创建数据集

**POST** `/api/v1/datasets`

创建数据集。

##### 请求详情

- Method: POST
- URL: `/api/v1/datasets`
- Headers:
  - `'content-Type: application/json'`
  - `'Authorization: Bearer <YOUR_API_KEY>'`
- Body:
  - `"name"`: `string`
  - `"avatar"`: `string`
  - `"description"`: `string`
  - `"embedding_model"`: `string`
  - `"permission"`: `string`
  - `"chunk_method"`: `string`
  - `"parser_config"`: `object`

##### 请求示例

```bash
curl --request POST \
     --url http://{address}/api/v1/datasets \
     --header 'Content-Type: application/json' \
     --header 'Authorization: Bearer <YOUR_API_KEY>' \
     --data '{
      "name": "test_1"
      }'
```

##### 请求参数

- `"name"`: (*Body parameter*), `string`, *必需*
  要创建的数据集的唯一名称。必须符合以下要求：
  - 仅限基本多语言平面 (BMP)
  - 最多 128 个字符
  - 不区分大小写

- `"avatar"`: (*Body parameter*), `string`
  头像的 Base64 编码。
  - 最多 65535 个字符

- `"description"`: (*Body parameter*), `string`
  要创建的数据集的简要描述。
  - 最多 65535 个字符

- `"embedding_model"`: (*Body parameter*), `string`
  要使用的嵌入模型名称。例如：`"BAAI/bge-large-zh-v1.5@BAAI"`
  - 最多 255 个字符
  - 必须遵循 `model_name@model_factory` 格式

- `"permission"`: (*Body parameter*), `string`
  指定谁可以访问要创建的数据集。可用选项：
  - `"me"`：（默认）只有您可以管理数据集。
  - `"team"`：所有团队成员都可以管理数据集。

- `"chunk_method"`: (*Body parameter*), `enum<string>`
  要创建的数据集的分块方法。可用选项：
  - `"naive"`：通用（默认）
  - `"book"`：书籍
  - `"email"`：邮件
  - `"laws"`：法律
  - `"manual"`：手动
  - `"one"`：单一
  - `"paper"`：论文
  - `"picture"`：图片
  - `"presentation"`：演示文稿
  - `"qa"`：问答
  - `"table"`：表格
  - `"tag"`：标签

- `"parser_config"`: (*Body parameter*), `object`
  数据集解析器的配置设置。此 JSON 对象中的属性因所选的 `"chunk_method"` 而异：
  - 如果 `"chunk_method"` 是 `"naive"`，`"parser_config"` 对象包含以下属性：
    - `"auto_keywords"`: `int`
      - 默认为 `0`
      - 最小值：`0`
      - 最大值：`32`
    - `"auto_questions"`: `int`
      - 默认为 `0`
      - 最小值：`0`
      - 最大值：`10`
    - `"chunk_token_num"`: `int`
      - 默认为 `512`
      - 最小值：`1`
      - 最大值：`2048`
    - `"delimiter"`: `string`
      - 默认为 `"\n"`。
    - `"html4excel"`: `bool` 指示是否将 Excel 文档转换为 HTML 格式。
      - 默认为 `false`
    - `"layout_recognize"`: `string`
      - 默认为 `DeepDOC`
    - `"tag_kb_ids"`: `array<string>` 参考 [使用标签集](https://ragflow.io/docs/dev/use_tag_sets)
      - 必须包含数据集 ID 列表，其中每个数据集都使用标签块方法进行解析
    - `"task_page_size"`: `int` 仅适用于 PDF。
      - 默认为 `12`
      - 最小值：`1`
    - `"raptor"`: `object` RAPTOR 特定设置。
      - 默认为：`{"use_raptor": false}`
    - `"graphrag"`: `object` GRAPHRAG 特定设置。
      - 默认为：`{"use_graphrag": false}`
  - 如果 `"chunk_method"` 是 `"qa"`、`"manuel"`、`"paper"`、`"book"`、`"laws"` 或 `"presentation"`，`"parser_config"` 对象包含以下属性：
    - `"raptor"`: `object` RAPTOR 特定设置。
      - 默认为：`{"use_raptor": false}`。
  - 如果 `"chunk_method"` 是 `"table"`、`"picture"`、`"one"` 或 `"email"`，`"parser_config"` 是一个空的 JSON 对象。

##### 响应结果

成功示例：

```json
{
    "code": 0,
    "data": {
        "avatar": null,
        "chunk_count": 0,
        "chunk_method": "naive",
        "create_date": "Mon, 28 Apr 2025 18:40:41 GMT",
        "create_time": 1745836841611,
        "created_by": "3af81804241d11f0a6a79f24fc270c7f",
        "description": null,
        "document_count": 0,
        "embedding_model": "BAAI/bge-large-zh-v1.5@BAAI",
        "id": "3b4de7d4241d11f0a6a79f24fc270c7f",
        "language": "English",
        "name": "RAGFlow example",
        "pagerank": 0,
        "parser_config": {
            "chunk_token_num": 128,
            "delimiter": "\\n!?;。；！？",
            "html4excel": false,
            "layout_recognize": "DeepDOC",
            "raptor": {
                "use_raptor": false
                }
            },
        "permission": "me",
        "similarity_threshold": 0.2,
        "status": "1",
        "tenant_id": "3af81804241d11f0a6a79f24fc270c7f",
        "token_num": 0,
        "update_date": "Mon, 28 Apr 2025 18:40:41 GMT",
        "update_time": 1745836841611,
        "vector_similarity_weight": 0.3,
    },
}
```

失败示例：

```json
{
    "code": 101,
    "message": "Dataset name 'RAGFlow example' already exists"
}
```

---

### 删除数据集

**DELETE** `/api/v1/datasets`

根据 ID 删除数据集。

##### 请求详情

- Method: DELETE
- URL: `/api/v1/datasets`
- Headers:
  - `'content-Type: application/json'`
  - `'Authorization: Bearer <YOUR_API_KEY>'`
- Body:
  - `"ids"`: `list[string]` or `null`

##### 请求示例

```bash
curl --request DELETE \
     --url http://{address}/api/v1/datasets \
     --header 'Content-Type: application/json' \
     --header 'Authorization: Bearer <YOUR_API_KEY>' \
     --data '{
     "ids": ["d94a8dc02c9711f0930f7fbc369eab6d", "e94a8dc02c9711f0930f7fbc369eab6e"]
     }'
```

##### 请求参数

- `"ids"`: (*Body parameter*), `list[string]` or `null`,   *必需*
  指定要删除的数据集：
  - 如果为 `null`，将删除所有数据集。
  - 如果是 ID 数组，只删除指定的数据集。
  - 如果是空数组，不删除任何数据集。

##### 响应结果

成功示例：

```json
{
    "code": 0
}
```

失败示例：

```json
{
    "code": 102,
    "message": "You don't own the dataset."
}
```

---

### 更新数据集

**PUT** `/api/v1/datasets/{dataset_id}`

更新指定数据集的配置。

##### 请求详情

- Method: PUT
- URL: `/api/v1/datasets/{dataset_id}`
- Headers:
  - `'content-Type: application/json'`
  - `'Authorization: Bearer <YOUR_API_KEY>'`
- Body:
  - `"name"`: `string`
  - `"avatar"`: `string`
  - `"description"`: `string`
  - `"embedding_model"`: `string`
  - `"permission"`: `string`
  - `"chunk_method"`: `string`
  - `"pagerank"`: `int`
  - `"parser_config"`: `object`

##### 请求示例

```bash
curl --request PUT \
     --url http://{address}/api/v1/datasets/{dataset_id} \
     --header 'Content-Type: application/json' \
     --header 'Authorization: Bearer <YOUR_API_KEY>' \
     --data '
     {
          "name": "updated_dataset"
     }'
```

##### 请求参数

- `dataset_id`: (*Path parameter*)
  要更新的数据集的 ID。
- `"name"`: (*Body parameter*), `string`
  数据集的修订名称。
  - 仅限基本多语言平面 (BMP)
  - 最多 128 个字符
  - 不区分大小写
- `"avatar"`: (*Body parameter*), `string`
  更新的头像的 base64 编码。
  - 最多 65535 个字符
- `"embedding_model"`: (*Body parameter*), `string`
  更新的嵌入模型名称。
  - 在更新 `"embedding_model"` 之前，确保 `"chunk_count"` 为 `0`。
  - 最多 255 个字符
  - 必须遵循 `model_name@model_factory` 格式
- `"permission"`: (*Body parameter*), `string`
  更新的数据集权限。可用选项：
  - `"me"`：（默认）只有您可以管理数据集。
  - `"team"`：所有团队成员都可以管理数据集。
- `"pagerank"`: (*Body parameter*), `int`
  参考 [设置页面排名](https://ragflow.io/docs/dev/set_page_rank)
  - 默认：`0`
  - 最小值：`0`
  - 最大值：`100`
- `"chunk_method"`: (*Body parameter*), `enum<string>`
  数据集的分块方法。可用选项：
  - `"naive"`：通用（默认）
  - `"book"`：书籍
  - `"email"`：邮件
  - `"laws"`：法律
  - `"manual"`：手动
  - `"one"`：单一
  - `"paper"`：论文
  - `"picture"`：图片
  - `"presentation"`：演示文稿
  - `"qa"`：问答
  - `"table"`：表格
  - `"tag"`：标签
- `"parser_config"`: (*Body parameter*), `object`
  数据集解析器的配置设置。此 JSON 对象中的属性因所选的 `"chunk_method"` 而异：
  - 如果 `"chunk_method"` 是 `"naive"`，`"parser_config"` 对象包含以下属性：
    - `"auto_keywords"`: `int`
      - 默认为 `0`
      - 最小值：`0`
      - 最大值：`32`
    - `"auto_questions"`: `int`
      - 默认为 `0`
      - 最小值：`0`
      - 最大值：`10`
    - `"chunk_token_num"`: `int`
      - 默认为 `512`
      - 最小值：`1`
      - 最大值：`2048`
    - `"delimiter"`: `string`
      - 默认为 `"\n"`。
    - `"html4excel"`: `bool` 指示是否将 Excel 文档转换为 HTML 格式。
      - 默认为 `false`
    - `"layout_recognize"`: `string`
      - 默认为 `DeepDOC`
    - `"tag_kb_ids"`: `array<string>` 参考 [使用标签集](https://ragflow.io/docs/dev/use_tag_sets)
      - 必须包含数据集 ID 列表，其中每个数据集都使用标签块方法进行解析
    - `"task_page_size"`: `int` 仅适用于 PDF。
      - 默认为 `12`
      - 最小值：`1`
    - `"raptor"`: `object` RAPTOR 特定设置。
      - 默认为：`{"use_raptor": false}`
    - `"graphrag"`: `object` GRAPHRAG 特定设置。
      - 默认为：`{"use_graphrag": false}`
  - 如果 `"chunk_method"` 是 `"qa"`、`"manuel"`、`"paper"`、`"book"`、`"laws"` 或 `"presentation"`，`"parser_config"` 对象包含以下属性：
    - `"raptor"`: `object` RAPTOR 特定设置。
      - 默认为：`{"use_raptor": false}`。
  - 如果 `"chunk_method"` 是 `"table"`、`"picture"`、`"one"` 或 `"email"`，`"parser_config"` 是一个空的 JSON 对象。

##### 响应结果

成功示例：

```json
{
    "code": 0
}
```

失败示例：

```json
{
    "code": 102,
    "message": "Can't change tenant_id."
}
```

---

### 列出数据集

**GET** `/api/v1/datasets?page={page}&page_size={page_size}&orderby={orderby}&desc={desc}&name={dataset_name}&id={dataset_id}`

列出数据集。

##### 请求详情

- Method: GET
- URL: `/api/v1/datasets?page={page}&page_size={page_size}&orderby={orderby}&desc={desc}&name={dataset_name}&id={dataset_id}`
- Headers:
  - `'Authorization: Bearer <YOUR_API_KEY>'`

##### 请求示例

```bash
curl --request GET \
     --url http://{address}/api/v1/datasets?page={page}&page_size={page_size}&orderby={orderby}&desc={desc}&name={dataset_name}&id={dataset_id} \
     --header 'Authorization: Bearer <YOUR_API_KEY>'
```

##### 请求参数

- `page`: (*Filter parameter*)
  指定显示数据集的页面。默认为 `1`。
- `page_size`: (*Filter parameter*)
  每页数据集的数量。默认为 `30`。
- `orderby`: (*Filter parameter*)
  数据集排序的字段。可用选项：
  - `create_time`（默认）
  - `update_time`
- `desc`: (*Filter parameter*)
  指示检索到的数据集是否按降序排序。默认为 `true`。
- `name`: (*Filter parameter*)
  要检索的数据集名称。
- `id`: (*Filter parameter*)
  要检索的数据集 ID。

##### 响应结果

成功示例：

```json
{
    "code": 0,
    "data": [
        {
            "avatar": "",
            "chunk_count": 59,
            "create_date": "Sat, 14 Sep 2024 01:12:37 GMT",
            "create_time": 1726276357324,
            "created_by": "69736c5e723611efb51b0242ac120007",
            "description": null,
            "document_count": 1,
            "embedding_model": "BAAI/bge-large-zh-v1.5",
            "id": "6e211ee0723611efa10a0242ac120007",
            "language": "English",
            "name": "mysql",
            "chunk_method": "naive",
            "parser_config": {
                "chunk_token_num": 8192,
                "delimiter": "\\n",
                "entity_types": [
                    "organization",
                    "person",
                    "location",
                    "event",
                    "time"
                ]
            },
            "permission": "me",
            "similarity_threshold": 0.2,
            "status": "1",
            "tenant_id": "69736c5e723611efb51b0242ac120007",
            "token_num": 12744,
            "update_date": "Thu, 10 Oct 2024 04:07:23 GMT",
            "update_time": 1728533243536,
            "vector_similarity_weight": 0.3
        }
    ]
}
```

失败示例：

```json
{
    "code": 102,
    "message": "The dataset doesn't exist"
}
```
 ---

## Get dataset's knowledge graph

**GET** `/api/v1/datasets/{dataset_id}/knowledge_graph`

Retrieves the knowledge graph of a specified dataset.

#### Request

- Method: GET
- URL: `/api/v1/datasets/{dataset_id}/knowledge_graph`
- Headers:
  - `'Authorization: Bearer <YOUR_API_KEY>'`

##### Request example

```bash
curl --request GET \
     --url http://{address}/api/v1/datasets/{dataset_id}/knowledge_graph \
     --header 'Authorization: Bearer <YOUR_API_KEY>'
```

##### Request parameters

- `dataset_id`: (*Path parameter*)  
  The ID of the target dataset.

#### Response

Success:

```json
{
    "code": 0,
    "data": {
        "graph": {
            "directed": false,
            "edges": [
                {
                    "description": "The notice is a document issued to convey risk warnings and operational alerts.<SEP>The notice is a specific instance of a notification document issued under the risk warning framework.",
                    "keywords": ["9", "8"],
                    "source": "notice",
                    "source_id": ["8a46cdfe4b5c11f0a5281a58e595aa1c"],
                    "src_id": "xxx",
                    "target": "xxx",
                    "tgt_id": "xxx",
                    "weight": 17.0
                }
            ],
            "graph": {
                "source_id": ["8a46cdfe4b5c11f0a5281a58e595aa1c", "8a7eb6424b5c11f0a5281a58e595aa1c"]
            },
            "multigraph": false,
            "nodes": [
                {
                    "description": "xxx",
                    "entity_name": "xxx",
                    "entity_type": "ORGANIZATION",
                    "id": "xxx",
                    "pagerank": 0.10804906590624092,
                    "rank": 3,
                    "source_id": ["8a7eb6424b5c11f0a5281a58e595aa1c"]
                }
            ]
        },
        "mind_map": {}
    }
}
```

Failure:

```json
{
    "code": 102,
    "message": "The dataset doesn't exist"
}
```
---

## Delete dataset's knowledge graph

**DELETE** `/api/v1/datasets/{dataset_id}/knowledge_graph`

Removes the knowledge graph of a specified dataset.

#### Request

- Method: DELETE
- URL: `/api/v1/datasets/{dataset_id}/knowledge_graph`
- Headers:
  - `'Authorization: Bearer <YOUR_API_KEY>'`

##### Request example

```bash
curl --request DELETE \
     --url http://{address}/api/v1/datasets/{dataset_id}/knowledge_graph \
     --header 'Authorization: Bearer <YOUR_API_KEY>'
```

##### Request parameters

- `dataset_id`: (*Path parameter*)  
  The ID of the target dataset.

#### Response

Success:

```json
{
    "code": 0,
    "data": true
}
```

Failure:

```json
{
    "code": 102,
    "message": "The dataset doesn't exist"
}
```
---

## 数据集内文件管理

---

### 上传文档

**POST** `/api/v1/datasets/{dataset_id}/documents`

将文档上传到指定数据集。

##### 请求详情

- Method: POST
- URL: `/api/v1/datasets/{dataset_id}/documents`
- Headers:
  - `'Content-Type: multipart/form-data'`
  - `'Authorization: Bearer <YOUR_API_KEY>'`
- Form:
  - `'file=@{FILE_PATH}'`

##### 请求示例

```bash
curl --request POST \
     --url http://{address}/api/v1/datasets/{dataset_id}/documents \
     --header 'Content-Type: multipart/form-data' \
     --header 'Authorization: Bearer <YOUR_API_KEY>' \
     --form 'file=@./test1.txt' \
     --form 'file=@./test2.pdf'
```

##### 请求参数

- `dataset_id`: (*Path parameter*)
  要上传文档的数据集 ID。
- `'file'`: (*Body parameter*)
  要上传的文档。

##### 响应结果

成功示例：

```json
{
    "code": 0,
    "data": [
        {
            "chunk_method": "naive",
            "created_by": "69736c5e723611efb51b0242ac120007",
            "dataset_id": "527fa74891e811ef9c650242ac120006",
            "id": "b330ec2e91ec11efbc510242ac120004",
            "location": "1.txt",
            "name": "1.txt",
            "parser_config": {
                "chunk_token_num": 128,
                "delimiter": "\\n",
                "html4excel": false,
                "layout_recognize": true,
                "raptor": {
                    "use_raptor": false
                }
            },
            "run": "UNSTART",
            "size": 17966,
            "thumbnail": "",
            "type": "doc"
        }
    ]
}
```

失败示例：

```json
{
    "code": 101,
    "message": "No file part!"
}
```

---

### 更新文档

**PUT** `/api/v1/datasets/{dataset_id}/documents/{document_id}`

更新指定文档的配置。

##### 请求详情

- Method: PUT
- URL: `/api/v1/datasets/{dataset_id}/documents/{document_id}`
- Headers:
  - `'content-Type: application/json'`
  - `'Authorization: Bearer <YOUR_API_KEY>'`
- Body:
  - `"name"`:`string`
  - `"meta_fields"`:`object`
  - `"chunk_method"`:`string`
  - `"parser_config"`:`object`

##### 请求示例

```bash
curl --request PUT \
     --url http://{address}/api/v1/datasets/{dataset_id}/info/{document_id} \
     --header 'Authorization: Bearer <YOUR_API_KEY>' \
     --header 'Content-Type: application/json' \
     --data '
     {
          "name": "manual.txt",
          "chunk_method": "manual",
          "parser_config": {"chunk_token_num": 128}
     }'

```

##### 请求参数

- `dataset_id`: (*Path parameter*)
  关联数据集的 ID。
- `document_id`: (*Path parameter*)
  要更新的文档的 ID。
- `"name"`: (*Body parameter*), `string`
- `"meta_fields"`: (*Body parameter*), `dict[str, Any]` 文档的元字段。
- `"chunk_method"`: (*Body parameter*), `string`
  应用于文档的解析方法：
  - `"naive"`：通用
  - `"manual"`：手动
  - `"qa"`：问答
  - `"table"`：表格
  - `"paper"`：论文
  - `"book"`：书籍
  - `"laws"`：法律
  - `"presentation"`：演示文稿
  - `"picture"`：图片
  - `"one"`：单一
  - `"email"`：邮件
- `"parser_config"`: (*Body parameter*), `object`
  数据集解析器的配置设置。此 JSON 对象中的属性因所选的 `"chunk_method"` 而异：
  - 如果 `"chunk_method"` 是 `"naive"`，`"parser_config"` 对象包含以下属性：
    - `"chunk_token_num"`：默认为 `256`。
    - `"layout_recognize"`：默认为 `true`。
    - `"html4excel"`：指示是否将 Excel 文档转换为 HTML 格式。默认为 `false`。
    - `"delimiter"`：默认为 `"\n"`。
    - `"task_page_size"`：默认为 `12`。仅适用于 PDF。
    - `"raptor"`：RAPTOR 特定设置。默认为：`{"use_raptor": false}`。
  - 如果 `"chunk_method"` 是 `"qa"`、`"manuel"`、`"paper"`、`"book"`、`"laws"` 或 `"presentation"`，`"parser_config"` 对象包含以下属性：
    - `"raptor"`：RAPTOR 特定设置。默认为：`{"use_raptor": false}`。
  - 如果 `"chunk_method"` 是 `"table"`、`"picture"`、`"one"` 或 `"email"`，`"parser_config"` 是一个空的 JSON 对象。

##### 响应结果

成功示例：

```json
{
    "code": 0
}
```

失败示例：

```json
{
    "code": 102,
    "message": "The dataset does not have the document."
}
```

---

### 下载文档

**GET** `/api/v1/datasets/{dataset_id}/documents/{document_id}`

从指定数据集下载文档。

##### 请求详情

- Method: GET
- URL: `/api/v1/datasets/{dataset_id}/documents/{document_id}`
- Headers:
  - `'Authorization: Bearer <YOUR_API_KEY>'`
- Output:
  - `'{PATH_TO_THE_FILE}'`

##### 请求示例

```bash
curl --request GET \
     --url http://{address}/api/v1/datasets/{dataset_id}/documents/{document_id} \
     --header 'Authorization: Bearer <YOUR_API_KEY>' \
     --output ./ragflow.txt
```

##### 请求参数

- `dataset_id`: (*Path parameter*)
  关联的数据集 ID。
- `documents_id`: (*Path parameter*)
  要下载的文档的 ID。

##### 响应结果

成功示例：

```json
This is a test to verify the file download feature.
```

失败示例：

```json
{
    "code": 102,
    "message": "You do not own the dataset 7898da028a0511efbf750242ac1220005."
}
```

---

### 列出文档

**GET** `/api/v1/datasets/{dataset_id}/documents?page={page}&page_size={page_size}&orderby={orderby}&desc={desc}&keywords={keywords}&id={document_id}&name={document_name}`

列出指定数据集中的文档。

##### 请求详情

- Method: GET
- URL: `/api/v1/datasets/{dataset_id}/documents?page={page}&page_size={page_size}&orderby={orderby}&desc={desc}&keywords={keywords}&id={document_id}&name={document_name}`
- Headers:
  - `'content-Type: application/json'`
  - `'Authorization: Bearer <YOUR_API_KEY>'`

##### 请求示例

```bash
curl --request GET \
     --url http://{address}/api/v1/datasets/{dataset_id}/documents?page={page}&page_size={page_size}&orderby={orderby}&desc={desc}&keywords={keywords}&id={document_id}&name={document_name} \
     --header 'Authorization: Bearer <YOUR_API_KEY>'
```

##### 请求参数

- `dataset_id`: (*Path parameter*)
  关联的数据集 ID。
- `keywords`: (*Filter parameter*), `string`
  用于匹配文档标题的关键词。
- `page`: (*Filter parameter*), `integer`
  指定显示文档的页面。默认为 `1`。
- `page_size`: (*Filter parameter*), `integer`
  每页文档的最大数量。默认为 `30`。
- `orderby`: (*Filter parameter*), `string`
  文档排序的字段。可用选项：
  - `create_time`（默认）
  - `update_time`
- `desc`: (*Filter parameter*), `boolean`
  指示检索到的文档是否按降序排序。默认为 `true`。
- `id`: (*Filter parameter*), `string`
  要检索的文档 ID。

##### 响应结果

成功示例：

```json
{
    "code": 0,
    "data": {
        "docs": [
            {
                "chunk_count": 0,
                "create_date": "Mon, 14 Oct 2024 09:11:01 GMT",
                "create_time": 1728897061948,
                "created_by": "69736c5e723611efb51b0242ac120007",
                "id": "3bcfbf8a8a0c11ef8aba0242ac120006",
                "knowledgebase_id": "7898da028a0511efbf750242ac120005",
                "location": "Test_2.txt",
                "name": "Test_2.txt",
                "parser_config": {
                    "chunk_token_count": 128,
                    "delimiter": "\n",
                    "layout_recognize": true,
                    "task_page_size": 12
                },
                "chunk_method": "naive",
                "process_begin_at": null,
                "process_duration": 0.0,
                "progress": 0.0,
                "progress_msg": "",
                "run": "0",
                "size": 7,
                "source_type": "local",
                "status": "1",
                "thumbnail": null,
                "token_count": 0,
                "type": "doc",
                "update_date": "Mon, 14 Oct 2024 09:11:01 GMT",
                "update_time": 1728897061948
            }
        ],
        "total": 1
    }
}
```

失败示例：

```json
{
    "code": 102,
    "message": "You don't own the dataset 7898da028a0511efbf750242ac1220005. "
}
```

---

### 删除文档

**DELETE** `/api/v1/datasets/{dataset_id}/documents`

根据 ID 删除文档。

##### 请求详情

- Method: DELETE
- URL: `/api/v1/datasets/{dataset_id}/documents`
- Headers:
  - `'Content-Type: application/json'`
  - `'Authorization: Bearer <YOUR_API_KEY>'`
- Body:
  - `"ids"`: `list[string]`

##### 请求示例

```bash
curl --request DELETE \
     --url http://{address}/api/v1/datasets/{dataset_id}/documents \
     --header 'Content-Type: application/json' \
     --header 'Authorization: Bearer <YOUR_API_KEY>' \
     --data '
     {
          "ids": ["id_1","id_2"]
     }'
```

##### 请求参数

- `dataset_id`: (*Path parameter*)
  关联的数据集 ID。
- `"ids"`: (*Body parameter*), `list[string]`
  要删除的文档的 ID。如果未指定，将删除指定数据集中的所有文档。

##### 响应结果

成功示例：

```json
{
    "code": 0
}.
```

失败示例：

```json
{
    "code": 102,
    "message": "You do not own the dataset 7898da028a0511efbf750242ac1220005."
}
```

---

### 解析文档

**POST** `/api/v1/datasets/{dataset_id}/chunks`

解析指定数据集中的文档。

##### 请求详情

- Method: POST
- URL: `/api/v1/datasets/{dataset_id}/chunks`
- Headers:
  - `'content-Type: application/json'`
  - `'Authorization: Bearer <YOUR_API_KEY>'`
- Body:
  - `"document_ids"`: `list[string]`

##### 请求示例

```bash
curl --request POST \
     --url http://{address}/api/v1/datasets/{dataset_id}/chunks \
     --header 'Content-Type: application/json' \
     --header 'Authorization: Bearer <YOUR_API_KEY>' \
     --data '
     {
          "document_ids": ["97a5f1c2759811efaa500242ac120004","97ad64b6759811ef9fc30242ac120004"]
     }'
```

##### 请求参数

- `dataset_id`: (*Path parameter*)
  数据集 ID。
- `"document_ids"`: (*Body parameter*), `list[string]`, *必需*
  要解析的文档的 ID。

##### 响应结果

成功示例：

```json
{
    "code": 0
}
```

失败示例：

```json
{
    "code": 102,
    "message": "`document_ids` is required"
}
```

---

### 停止解析文档

**DELETE** `/api/v1/datasets/{dataset_id}/chunks`

停止解析指定文档。

##### 请求详情

- Method: DELETE
- URL: `/api/v1/datasets/{dataset_id}/chunks`
- Headers:
  - `'content-Type: application/json'`
  - `'Authorization: Bearer <YOUR_API_KEY>'`
- Body:
  - `"document_ids"`: `list[string]`

##### 请求示例

```bash
curl --request DELETE \
     --url http://{address}/api/v1/datasets/{dataset_id}/chunks \
     --header 'Content-Type: application/json' \
     --header 'Authorization: Bearer <YOUR_API_KEY>' \
     --data '
     {
          "document_ids": ["97a5f1c2759811efaa500242ac120004","97ad64b6759811ef9fc30242ac120004"]
     }'
```

##### 请求参数

- `dataset_id`: (*Path parameter*)
  关联的数据集 ID。
- `"document_ids"`: (*Body parameter*), `list[string]`, *必需*
  要停止解析的文档的 ID。

##### 响应结果

成功示例：

```json
{
    "code": 0
}
```

失败示例：

```json
{
    "code": 102,
    "message": "`document_ids` is required"
}
```

---

## 数据集内块管理

---

### 添加块

**POST** `/api/v1/datasets/{dataset_id}/documents/{document_id}/chunks`

向指定数据集中的指定文档添加块。

##### 请求详情

- Method: POST
- URL: `/api/v1/datasets/{dataset_id}/documents/{document_id}/chunks`
- Headers:
  - `'content-Type: application/json'`
  - `'Authorization: Bearer <YOUR_API_KEY>'`
- Body:
  - `"content"`: `string`
  - `"important_keywords"`: `list[string]`

##### 请求示例

```bash
curl --request POST \
     --url http://{address}/api/v1/datasets/{dataset_id}/documents/{document_id}/chunks \
     --header 'Content-Type: application/json' \
     --header 'Authorization: Bearer <YOUR_API_KEY>' \
     --data '
     {
          "content": "<CHUNK_CONTENT_HERE>"
     }'
```

##### 请求参数

- `dataset_id`: (*Path parameter*)
  关联的数据集 ID。
- `document_ids`: (*Path parameter*)
  关联的文档 ID。
- `"content"`: (*Body parameter*), `string`, *必需*
  块的文本内容。
- `"important_keywords`(*Body parameter*), `list[string]`
  要与块关联的关键词或短语。
- `"questions"`(*Body parameter*), `list[string]`
  如果有给定的问题，嵌入的块将基于这些问题

##### 响应结果

成功示例：

```json
{
    "code": 0,
    "data": {
        "chunk": {
            "content": "who are you",
            "create_time": "2024-12-30 16:59:55",
            "create_timestamp": 1735549195.969164,
            "dataset_id": "72f36e1ebdf411efb7250242ac120006",
            "document_id": "61d68474be0111ef98dd0242ac120006",
            "id": "12ccdc56e59837e5",
            "important_keywords": [],
            "questions": []
        }
    }
}
```

失败示例：

```json
{
    "code": 102,
    "message": "`content` is required"
}
```

---

### 列出块

**GET** `/api/v1/datasets/{dataset_id}/documents/{document_id}/chunks?keywords={keywords}&page={page}&page_size={page_size}&id={id}`

列出指定文档中的块。

##### 请求详情

- Method: GET
- URL: `/api/v1/datasets/{dataset_id}/documents/{document_id}/chunks?keywords={keywords}&page={page}&page_size={page_size}&id={chunk_id}`
- Headers:
  - `'Authorization: Bearer <YOUR_API_KEY>'`

##### 请求示例

```bash
curl --request GET \
     --url http://{address}/api/v1/datasets/{dataset_id}/documents/{document_id}/chunks?keywords={keywords}&page={page}&page_size={page_size}&id={chunk_id} \
     --header 'Authorization: Bearer <YOUR_API_KEY>'
```

##### 请求参数

- `dataset_id`: (*Path parameter*)
  关联的数据集 ID。
- `document_id`: (*Path parameter*)
  关联的文档 ID。
- `keywords`(*Filter parameter*), `string`
  用于匹配块内容的关键词。
- `page`(*Filter parameter*), `integer`
  指定显示块的页面。默认为 `1`。
- `page_size`(*Filter parameter*), `integer`
  每页块的最大数量。默认为 `1024`。
- `id`(*Filter parameter*), `string`
  要检索的块的 ID。

##### 响应结果

成功示例：

```json
{
    "code": 0,
    "data": {
        "chunks": [
            {
                "available": true,
                "content": "This is a test content.",
                "docnm_kwd": "1.txt",
                "document_id": "b330ec2e91ec11efbc510242ac120004",
                "id": "b48c170e90f70af998485c1065490726",
                "image_id": "",
                "important_keywords": "",
                "positions": [
                    ""
                ]
            }
        ],
        "doc": {
            "chunk_count": 1,
            "chunk_method": "naive",
            "create_date": "Thu, 24 Oct 2024 09:45:27 GMT",
            "create_time": 1729763127646,
            "created_by": "69736c5e723611efb51b0242ac120007",
            "dataset_id": "527fa74891e811ef9c650242ac120006",
            "id": "b330ec2e91ec11efbc510242ac120004",
            "location": "1.txt",
            "name": "1.txt",
            "parser_config": {
                "chunk_token_num": 128,
                "delimiter": "\\n",
                "html4excel": false,
                "layout_recognize": true,
                "raptor": {
                    "use_raptor": false
                }
            },
            "process_begin_at": "Thu, 24 Oct 2024 09:56:44 GMT",
            "process_duration": 0.54213,
            "progress": 0.0,
            "progress_msg": "Task dispatched...",
            "run": "2",
            "size": 17966,
            "source_type": "local",
            "status": "1",
            "thumbnail": "",
            "token_count": 8,
            "type": "doc",
            "update_date": "Thu, 24 Oct 2024 11:03:15 GMT",
            "update_time": 1729767795721
        },
        "total": 1
    }
}
```

失败示例：

```json
{
    "code": 102,
    "message": "You don't own the document 5c5999ec7be811ef9cab0242ac12000e5."
}
```

---

### 删除块

**DELETE** `/api/v1/datasets/{dataset_id}/documents/{document_id}/chunks`

根据 ID 删除块。

##### 请求详情

- Method: DELETE
- URL: `/api/v1/datasets/{dataset_id}/documents/{document_id}/chunks`
- Headers:
  - `'content-Type: application/json'`
  - `'Authorization: Bearer <YOUR_API_KEY>'`
- Body:
  - `"chunk_ids"`: `list[string]`

##### 请求示例

```bash
curl --request DELETE \
     --url http://{address}/api/v1/datasets/{dataset_id}/documents/{document_id}/chunks \
     --header 'Content-Type: application/json' \
     --header 'Authorization: Bearer <YOUR_API_KEY>' \
     --data '
     {
          "chunk_ids": ["test_1", "test_2"]
     }'
```

##### 请求参数

- `dataset_id`: (*Path parameter*)
  关联的数据集 ID。
- `document_ids`: (*Path parameter*)
  关联的文档 ID。
- `"chunk_ids"`: (*Body parameter*), `list[string]`
  要删除的块的 ID。如果未指定，将删除指定文档的所有块。

##### 响应结果

成功示例：

```json
{
    "code": 0
}
```

失败示例：

```json
{
    "code": 102,
    "message": "`chunk_ids` is required"
}
```

---

### 更新块

**PUT** `/api/v1/datasets/{dataset_id}/documents/{document_id}/chunks/{chunk_id}`

更新指定块的内容或配置。

##### 请求详情

- Method: PUT
- URL: `/api/v1/datasets/{dataset_id}/documents/{document_id}/chunks/{chunk_id}`
- Headers:
  - `'content-Type: application/json'`
  - `'Authorization: Bearer <YOUR_API_KEY>'`
- Body:
  - `"content"`: `string`
  - `"important_keywords"`: `list[string]`
  - `"available"`: `boolean`

##### 请求示例

```bash
curl --request PUT \
     --url http://{address}/api/v1/datasets/{dataset_id}/documents/{document_id}/chunks/{chunk_id} \
     --header 'Content-Type: application/json' \
     --header 'Authorization: Bearer <YOUR_API_KEY>' \
     --data '
     {
          "content": "ragflow123",
          "important_keywords": []
     }'
```

##### 请求参数

- `dataset_id`: (*Path parameter*)
  关联的数据集 ID。
- `document_ids`: (*Path parameter*)
  关联的文档 ID。
- `chunk_id`: (*Path parameter*)
  要更新的块的 ID。
- `"content"`: (*Body parameter*), `string`
  块的文本内容。
- `"important_keywords"`: (*Body parameter*), `list[string]`
  要与块关联的关键词或短语列表。
- `"available"`: (*Body parameter*) `boolean`
  块在数据集中的可用状态。值选项：
  - `true`：可用（默认）
  - `false`：不可用

##### 响应结果

成功示例：

```json
{
    "code": 0
}
```

失败示例：

```json
{
    "code": 102,
    "message": "Can't find this chunk 29a2d9987e16ba331fb4d7d30d99b71d2"
}
```

---

### 检索块

**POST** `/api/v1/retrieval`

从指定数据集检索块。

##### 请求详情

- Method: POST
- URL: `/api/v1/retrieval`
- Headers:
  - `'content-Type: application/json'`
  - `'Authorization: Bearer <YOUR_API_KEY>'`
- Body:
  - `"question"`: `string`
  - `"dataset_ids"`: `list[string]`
  - `"document_ids"`: `list[string]`
  - `"page"`: `integer`
  - `"page_size"`: `integer`
  - `"similarity_threshold"`: `float`
  - `"vector_similarity_weight"`: `float`
  - `"top_k"`: `integer`
  - `"rerank_id"`: `string`
  - `"keyword"`: `boolean`
  - `"highlight"`: `boolean`

##### 请求示例

```bash
curl --request POST \
     --url http://{address}/api/v1/retrieval \
     --header 'Content-Type: application/json' \
     --header 'Authorization: Bearer <YOUR_API_KEY>' \
     --data '
     {
          "question": "What is advantage of ragflow?",
          "dataset_ids": ["b2a62730759d11ef987d0242ac120004"],
          "document_ids": ["77df9ef4759a11ef8bdd0242ac120004"]
     }'
```

##### 请求参数

- `"question"`: (*Body parameter*), `string`, *必需*
  用户查询或查询关键词。
- `"dataset_ids"`: (*Body parameter*) `list[string]`
  要搜索的数据集的 ID。如果不设置此参数，请确保设置 `"document_ids"`。
- `"document_ids"`: (*Body parameter*), `list[string]`
  要搜索的文档的 ID。确保所有选定的文档使用相同的嵌入模型。否则会发生错误。如果不设置此参数，请确保设置 `"dataset_ids"`。
- `"page"`: (*Body parameter*), `integer`
  指定显示块的页面。默认为 `1`。
- `"page_size"`: (*Body parameter*)
  每页块的最大数量。默认为 `30`。
- `"similarity_threshold"`: (*Body parameter*)
  最小相似度分数。默认为 `0.2`。
- `"vector_similarity_weight"`: (*Body parameter*), `float`
  向量余弦相似度的权重。默认为 `0.3`。如果 x 表示向量余弦相似度的权重，则 (1 - x) 是词项相似度权重。
- `"top_k"`: (*Body parameter*), `integer`
  参与向量余弦计算的块数量。默认为 `1024`。
- `"rerank_id"`: (*Body parameter*), `integer`
  重排序模型的 ID。
- `"keyword"`: (*Body parameter*), `boolean`
  指示是否启用基于关键词的匹配：
  - `true`：启用基于关键词的匹配。
  - `false`：禁用基于关键词的匹配（默认）。
- `"highlight"`: (*Body parameter*), `boolean`
  指定是否在结果中启用匹配词项的高亮显示：
  - `true`：启用匹配词项的高亮显示。
  - `false`：禁用匹配词项的高亮显示（默认）。

##### 响应结果

成功示例：

```json
{
    "code": 0,
    "data": {
        "chunks": [
            {
                "content": "ragflow content",
                "content_ltks": "ragflow content",
                "document_id": "5c5999ec7be811ef9cab0242ac120005",
                "document_keyword": "1.txt",
                "highlight": "<em>ragflow</em> content",
                "id": "d78435d142bd5cf6704da62c778795c5",
                "image_id": "",
                "important_keywords": [
                    ""
                ],
                "kb_id": "c7ee74067a2c11efb21c0242ac120006",
                "positions": [
                    ""
                ],
                "similarity": 0.9669436601210759,
                "term_similarity": 1.0,
                "vector_similarity": 0.8898122004035864
            }
        ],
        "doc_aggs": [
            {
                "count": 1,
                "doc_id": "5c5999ec7be811ef9cab0242ac120005",
                "doc_name": "1.txt"
            }
        ],
        "total": 1
    }
}
```

失败示例：

```json
{
    "code": 102,
    "message": "`datasets` is required."
}
```

---

## 聊天助手管理

---

### 创建聊天助手

**POST** `/api/v1/chats`

创建聊天助手。

##### 请求详情

- Method: POST
- URL: `/api/v1/chats`
- Headers:
  - `'content-Type: application/json'`
  - `'Authorization: Bearer <YOUR_API_KEY>'`
- Body:
  - `"name"`: `string`
  - `"avatar"`: `string`
  - `"dataset_ids"`: `list[string]`
  - `"llm"`: `object`
  - `"prompt"`: `object`

##### 请求示例

```shell
curl --request POST \
     --url http://{address}/api/v1/chats \
     --header 'Content-Type: application/json' \
     --header 'Authorization: Bearer <YOUR_API_KEY>' \
     --data '{
    "dataset_ids": ["0b2cbc8c877f11ef89070242ac120005"],
    "name":"new_chat_1"
}'
```

##### 请求参数

- `"name"`: (*Body parameter*), `string`, *必需*
  聊天助手的名称。
- `"avatar"`: (*Body parameter*), `string`
  头像的 Base64 编码。
- `"dataset_ids"`: (*Body parameter*), `list[string]`
  关联数据集的 ID。
- `"llm"`: (*Body parameter*), `object`
  要创建的聊天助手的 LLM 设置。如果未明确设置，将生成具有以下值的 JSON 对象作为默认值。`llm` JSON 对象包含以下属性：
  - `"model_name"`, `string`
    聊天模型名称。如果未设置，将使用用户的默认聊天模型。
  - `"temperature"`: `float`
    控制模型预测的随机性。较低的温度会产生更保守的响应，而较高的温度会产生更有创意和多样化的响应。默认为 `0.1`。
  - `"top_p"`: `float`
    也称为"核采样"，此参数设置阈值以选择较小的词汇集进行采样。它专注于最可能的词汇，排除不太可能的词汇。默认为 `0.3`。
  - `"presence_penalty"`: `float`
    通过对对话中已出现的词汇进行惩罚，阻止模型重复相同的信息。默认为 `0.4`。
  - `"frequency penalty"`: `float`
    与存在惩罚类似，这减少了模型频繁重复相同词汇的倾向。默认为 `0.7`。
- `"prompt"`: (*Body parameter*), `object`
  LLM 要遵循的指令。如果未明确设置，将生成具有以下值的 JSON 对象作为默认值。`prompt` JSON 对象包含以下属性：
  - `"similarity_threshold"`: `float` RAGFlow 在检索过程中采用加权关键词相似度和加权向量余弦相似度的组合，或加权关键词相似度和加权重排序分数的组合。此参数设置用户查询与块之间相似度的阈值。如果相似度分数低于此阈值，相应的块将被排除在结果之外。默认值为 `0.2`。
  - `"keywords_similarity_weight"`: `float` 此参数设置关键词相似度在与向量余弦相似度或重排序模型相似度的混合相似度分数中的权重。通过调整此权重，您可以控制关键词相似度相对于其他相似度度量的影响。默认值为 `0.7`。
  - `"top_n"`: `int` 此参数指定相似度分数高于 `similarity_threshold` 的顶部块数量，这些块将被提供给 LLM。LLM 将*仅*访问这些"前 N"个块。默认值为 `6`。
  - `"variables"`: `object[]` 此参数列出在**聊天配置**的"系统"字段中使用的变量。注意：
    - `"knowledge"` 是保留变量，代表检索到的块。
    - "系统"中的所有变量都应该用花括号括起来。
    - 默认值为 `[{"key": "knowledge", "optional": true}]`。
  - `"rerank_model"`: `string` 如果未指定，将使用向量余弦相似度；否则，将使用重排序分数。
  - `top_k`: `int` 指根据特定排序标准对列表或集合中的前 k 项进行重新排序或选择的过程。默认为 1024。
  - `"empty_response"`: `string` 如果在数据集中没有检索到用户问题的相关内容，将使用此作为响应。要允许 LLM 在没有找到内容时即兴发挥，请将此留空。
  - `"opener"`: `string` 对用户的开场问候语。默认为 `"Hi! I am your assistant, can I help you?"`。
  - `"show_quote`: `boolean` 指示是否应显示文本来源。默认为 `true`。
  - `"prompt"`: `string` 提示内容。

##### 响应结果

成功示例：

```json
{
    "code": 0,
    "data": {
        "avatar": "",
        "create_date": "Thu, 24 Oct 2024 11:18:29 GMT",
        "create_time": 1729768709023,
        "dataset_ids": [
            "527fa74891e811ef9c650242ac120006"
        ],
        "description": "A helpful Assistant",
        "do_refer": "1",
        "id": "b1f2f15691f911ef81180242ac120003",
        "language": "English",
        "llm": {
            "frequency_penalty": 0.7,
            "model_name": "qwen-plus@Tongyi-Qianwen",
            "presence_penalty": 0.4,
            "temperature": 0.1,
            "top_p": 0.3
        },
        "name": "12234",
        "prompt": {
            "empty_response": "Sorry! No relevant content was found in the knowledge base!",
            "keywords_similarity_weight": 0.3,
            "opener": "Hi! I'm your assistant, what can I do for you?",
            "prompt": "You are an intelligent assistant. Please summarize the content of the knowledge base to answer the question. Please list the data in the knowledge base and answer in detail. When all knowledge base content is irrelevant to the question, your answer must include the sentence \"The answer you are looking for is not found in the knowledge base!\" Answers need to consider chat history.\n ",
            "rerank_model": "",
            "similarity_threshold": 0.2,
            "top_n": 6,
            "variables": [
                {
                    "key": "knowledge",
                    "optional": false
                }
            ]
        },
        "prompt_type": "simple",
        "status": "1",
        "tenant_id": "69736c5e723611efb51b0242ac120007",
        "top_k": 1024,
        "update_date": "Thu, 24 Oct 2024 11:18:29 GMT",
        "update_time": 1729768709023
    }
}
```

失败示例：

```json
{
    "code": 102,
    "message": "Duplicated chat name in creating dataset."
}
```

---

### 更新聊天助手

**PUT** `/api/v1/chats/{chat_id}`

更新指定聊天助手的配置。

##### 请求详情

- Method: PUT
- URL: `/api/v1/chats/{chat_id}`
- Headers:
  - `'content-Type: application/json'`
  - `'Authorization: Bearer <YOUR_API_KEY>'`
- Body:
  - `"name"`: `string`
  - `"avatar"`: `string`
  - `"dataset_ids"`: `list[string]`
  - `"llm"`: `object`
  - `"prompt"`: `object`

##### 请求示例

```bash
curl --request PUT \
     --url http://{address}/api/v1/chats/{chat_id} \
     --header 'Content-Type: application/json' \
     --header 'Authorization: Bearer <YOUR_API_KEY>' \
     --data '
     {
          "name":"Test"
     }'
```

#### Parameters

- `chat_id`: (*Path parameter*)
聊天助手的ID，用于更新。

- `"name"`: (*Body parameter*), `string`, 必需
聊天助手的修改后名称。

- `"avatar"`: (*Body parameter*), `string`
头像的Base64编码。

- `"dataset_ids"`: (*Body parameter*), `list[string]`
相关数据集的ID列表。

- `"llm"`: (*Body parameter*), `object`
为聊天助手创建的LLM设置。如果没有明确设置，将生成以下值的默认字典。一个`llm`对象包含以下属性：

- `"model_name"`: `string`
聊天模型名称。如果没有设置，将使用用户的默认聊天模型。

- `"temperature"`: `float`
控制模型预测的随机性。较低的温度会导致更保守的响应，而较高的温度会产生更具创造性和多样化的响应。默认值为`0.1`。

- `"top_p"`: `float`
也称为“核采样”，此参数设置一个阈值以选择一个较小的单词集进行采样。它侧重于最可能的单词，切断不太可能的单词。默认值为`0.3`。

- `"presence_penalty"`: `float`
这通过惩罚已经在对话中出现的单词来阻止模型重复相同的信息。默认值为`0.2`。

- `"frequency penalty"`: `float`
类似于存在惩罚，这减少了模型频繁重复相同单词的倾向。默认值为`0.7`。

- `"prompt"`: (*Body parameter*), `object`
LLM遵循的说明。一个`prompt`对象包含以下属性：

- `"similarity_threshold"`: `float` RAGFlow采用加权关键词相似性和加权向量余弦相似性的组合，或者在检索期间采用加权关键词相似性和加权重排序分数的组合。此参数设置用户查询和块之间的相似性阈值。如果相似性分数低于此阈值，则相应的块将被排除在结果之外。默认值是`0.2`。

- `"keywords_similarity_weight"`: `float` 此参数设置在混合相似性分数（与向量余弦相似性或重排序模型相似性）中关键词相似性的权重。通过调整此权重，您可以控制关键词相似性相对于其他相似性度量的影响。默认值是`0.7`。

- `"top_n"`: `int` 此参数指定要输入LLM的相似性分数高于`similarity_threshold`的前N个块的数量。LLM将仅访问这些“顶部N”块。默认值是`8`。

- `"variables"`: `object[]` 此参数列出用于Chat配置中“系统”字段的变量。注意：

- `"knowledge"`是一个保留变量，表示检索到的块。

- “系统”中的所有变量都应使用花括号括起来。

- 默认值是`[{"key": "knowledge", "optional": true}]`

- `"rerank_model"`: `string` 如果未指定，则使用向量余弦相似性；否则，使用重排序分数。

- `"empty_response"`: `string` 如果在数据集中没有检索到用户的问题，则将使用此响应。为了允许LLM在没有找到内容时进行即兴创作，请将其留空。

- `"opener"`: `string` 对用户的问候语。默认为`"Hi! I am your assistant, can I help you?"`。

- `"show_quote"`: `boolean` 指示是否应显示文本的来源。默认为`true`。

- `"prompt"`: `string` 提示内容。

##### 响应结果

成功示例：

```json
{
    "code": 0
}
```

失败示例：

```json
{
    "code": 102,
    "message": "Duplicated chat name in updating dataset."
}
```

---

### 删除聊天助手

**DELETE** `/api/v1/chats`

根据 ID 删除聊天助手。

##### 请求详情

- Method: DELETE
- URL: `/api/v1/chats`
- Headers:
  - `'content-Type: application/json'`
  - `'Authorization: Bearer <YOUR_API_KEY>'`
- Body:
  - `"ids"`: `list[string]`

##### 请求示例

```bash
curl --request DELETE \
     --url http://{address}/api/v1/chats \
     --header 'Content-Type: application/json' \
     --header 'Authorization: Bearer <YOUR_API_KEY>' \
     --data '
     {
          "ids": ["test_1", "test_2"]
     }'
```

##### 请求参数

- `"ids"`: (*Body parameter*), `list[string]`
  要删除的聊天助手的 ID。如果未指定，将删除系统中的所有聊天助手。

##### 响应结果

成功示例：

```json
{
    "code": 0
}
```

失败示例：

```json
{
    "code": 102,
    "message": "ids are required"
}
```

---

### 列出聊天助手

**GET** `/api/v1/chats?page={page}&page_size={page_size}&orderby={orderby}&desc={desc}&name={chat_name}&id={chat_id}`

列出聊天助手。

##### 请求详情

- Method: GET
- URL: `/api/v1/chats?page={page}&page_size={page_size}&orderby={orderby}&desc={desc}&name={chat_name}&id={chat_id}`
- Headers:
  - `'Authorization: Bearer <YOUR_API_KEY>'`

##### 请求示例

```bash
curl --request GET \
     --url http://{address}/api/v1/chats?page={page}&page_size={page_size}&orderby={orderby}&desc={desc}&name={chat_name}&id={chat_id} \
     --header 'Authorization: Bearer <YOUR_API_KEY>'
```

##### 请求参数

- `page`: (*Filter parameter*), `integer`
  指定显示聊天助手的页面。默认为 `1`。
- `page_size`: (*Filter parameter*), `integer`
  每页聊天助手的数量。默认为 `30`。
- `orderby`: (*Filter parameter*), `string`
  结果排序的属性。可用选项：
  - `create_time`（默认）
  - `update_time`
- `desc`: (*Filter parameter*), `boolean`
  指示检索到的聊天助手是否按降序排序。默认为 `true`。
- `id`: (*Filter parameter*), `string`
  要检索的聊天助手的 ID。
- `name`: (*Filter parameter*), `string`
  要检索的聊天助手的名称。

##### 响应结果

成功示例：

```json
{
    "code": 0,
    "data": [
        {
            "avatar": "",
            "create_date": "Fri, 18 Oct 2024 06:20:06 GMT",
            "create_time": 1729232406637,
            "description": "A helpful Assistant",
            "do_refer": "1",
            "id": "04d0d8e28d1911efa3630242ac120006",
            "dataset_ids": ["527fa74891e811ef9c650242ac120006"],
            "language": "English",
            "llm": {
                "frequency_penalty": 0.7,
                "model_name": "qwen-plus@Tongyi-Qianwen",
                "presence_penalty": 0.4,
                "temperature": 0.1,
                "top_p": 0.3
            },
            "name": "13243",
            "prompt": {
                "empty_response": "Sorry! No relevant content was found in the knowledge base!",
                "keywords_similarity_weight": 0.3,
                "opener": "Hi! I'm your assistant, what can I do for you?",
                "prompt": "You are an intelligent assistant. Please summarize the content of the knowledge base to answer the question. Please list the data in the knowledge base and answer in detail. When all knowledge base content is irrelevant to the question, your answer must include the sentence \"The answer you are looking for is not found in the knowledge base!\" Answers need to consider chat history.\n",
                "rerank_model": "",
                "similarity_threshold": 0.2,
                "top_n": 6,
                "variables": [
                    {
                        "key": "knowledge",
                        "optional": false
                    }
                ]
            },
            "prompt_type": "simple",
            "status": "1",
            "tenant_id": "69736c5e723611efb51b0242ac120007",
            "top_k": 1024,
            "update_date": "Fri, 18 Oct 2024 06:20:06 GMT",
            "update_time": 1729232406638
        }
    ]
}
```

失败示例：

```json
{
    "code": 102,
    "message": "The chat doesn't exist"
}
```

---

## 会话管理

---

### 创建与聊天助手的会话

**POST** `/api/v1/chats/{chat_id}/sessions`

创建与聊天助手的会话。

##### 请求详情

- Method: POST
- URL: `/api/v1/chats/{chat_id}/sessions`
- Headers:
  - `'content-Type: application/json'`
  - `'Authorization: Bearer <YOUR_API_KEY>'`
- Body:
  - `"name"`: `string`
  - `"user_id"`: `string` (optional)

##### 请求示例

```bash
curl --request POST \
     --url http://{address}/api/v1/chats/{chat_id}/sessions \
     --header 'Content-Type: application/json' \
     --header 'Authorization: Bearer <YOUR_API_KEY>' \
     --data '
     {
          "name": "new session"
     }'
```

##### 请求参数

- `chat_id`: (*Path parameter*)
  关联聊天助手的 ID。
- `"name"`: (*Body parameter*), `string`
  要创建的聊天会话的名称。
- `"user_id"`: (*Body parameter*), `string`
  可选的用户定义 ID。

##### 响应结果

成功示例：

```json
{
    "code": 0,
    "data": {
        "chat_id": "2ca4b22e878011ef88fe0242ac120005",
        "create_date": "Fri, 11 Oct 2024 08:46:14 GMT",
        "create_time": 1728636374571,
        "id": "4606b4ec87ad11efbc4f0242ac120006",
        "messages": [
            {
                "content": "Hi! I am your assistant, can I help you?",
                "role": "assistant"
            }
        ],
        "name": "new session",
        "update_date": "Fri, 11 Oct 2024 08:46:14 GMT",
        "update_time": 1728636374571
    }
}
```

失败示例：

```json
{
    "code": 102,
    "message": "Name cannot be empty."
}
```

---

### 更新聊天助手的会话

**PUT** `/api/v1/chats/{chat_id}/sessions/{session_id}`

更新指定聊天助手的会话。

##### 请求详情

- Method: PUT
- URL: `/api/v1/chats/{chat_id}/sessions/{session_id}`
- Headers:
  - `'content-Type: application/json'`
  - `'Authorization: Bearer <YOUR_API_KEY>'`
- Body:
  - `"name`: `string`
  - `"user_id`: `string` (optional)

##### 请求示例

```bash
curl --request PUT \
     --url http://{address}/api/v1/chats/{chat_id}/sessions/{session_id} \
     --header 'Content-Type: application/json' \
     --header 'Authorization: Bearer <YOUR_API_KEY>' \
     --data '
     {
          "name": "<REVISED_SESSION_NAME_HERE>"
     }'
```

##### 请求参数

- `chat_id`: (*Path parameter*)
  关联聊天助手的 ID。
- `session_id`: (*Path parameter*)
  要更新的会话的 ID。
- `"name"`: (*Body Parameter*), `string`
  会话的修订名称。
- `"user_id"`: (*Body parameter*), `string`
  可选的用户定义 ID。

##### 响应结果

成功示例：

```json
{
    "code": 0
}
```

失败示例：

```json
{
    "code": 102,
    "message": "Name cannot be empty."
}
```

---

### 列出聊天助手的会话

**GET** `/api/v1/chats/{chat_id}/sessions?page={page}&page_size={page_size}&orderby={orderby}&desc={desc}&name={session_name}&id={session_id}`

列出与指定聊天助手关联的会话。

##### 请求详情

- Method: GET
- URL: `/api/v1/chats/{chat_id}/sessions?page={page}&page_size={page_size}&orderby={orderby}&desc={desc}&name={session_name}&id={session_id}&user_id={user_id}`
- Headers:
  - `'Authorization: Bearer <YOUR_API_KEY>'`

##### 请求示例

```bash
curl --request GET \
     --url http://{address}/api/v1/chats/{chat_id}/sessions?page={page}&page_size={page_size}&orderby={orderby}&desc={desc}&name={session_name}&id={session_id} \
     --header 'Authorization: Bearer <YOUR_API_KEY>'
```

##### 请求参数

- `chat_id`: (*Path parameter*)
  关联聊天助手的 ID。
- `page`: (*Filter parameter*), `integer`
  指定显示会话的页面。默认为 `1`。
- `page_size`: (*Filter parameter*), `integer`
  每页会话的数量。默认为 `30`。
- `orderby`: (*Filter parameter*), `string`
  会话排序的字段。可用选项：
  - `create_time`（默认）
  - `update_time`
- `desc`: (*Filter parameter*), `boolean`
  指示检索到的会话是否按降序排序。默认为 `true`。
- `name`: (*Filter parameter*) `string`
  要检索的聊天会话的名称。
- `id`: (*Filter parameter*), `string`
  要检索的聊天会话的 ID。
- `user_id`: (*Filter parameter*), `string`
  创建会话时传入的可选用户定义 ID。

##### 响应结果

成功示例：

```json
{
    "code": 0,
    "data": [
        {
            "chat": "2ca4b22e878011ef88fe0242ac120005",
            "create_date": "Fri, 11 Oct 2024 08:46:43 GMT",
            "create_time": 1728636403974,
            "id": "578d541e87ad11ef96b90242ac120006",
            "messages": [
                {
                    "content": "Hi! I am your assistant, can I help you?",
                    "role": "assistant"
                }
            ],
            "name": "new session",
            "update_date": "Fri, 11 Oct 2024 08:46:43 GMT",
            "update_time": 1728636403974
        }
    ]
}
```

失败示例：

```json
{
    "code": 102,
    "message": "The session doesn't exist"
}
```

---

### 删除聊天助手的会话

**DELETE** `/api/v1/chats/{chat_id}/sessions`

根据 ID 删除聊天助手的会话。

##### 请求详情

- Method: DELETE
- URL: `/api/v1/chats/{chat_id}/sessions`
- Headers:
  - `'content-Type: application/json'`
  - `'Authorization: Bearer <YOUR_API_KEY>'`
- Body:
  - `"ids"`: `list[string]`

##### 请求示例

```bash
curl --request DELETE \
     --url http://{address}/api/v1/chats/{chat_id}/sessions \
     --header 'Content-Type: application/json' \
     --header 'Authorization: Bearer <YOUR_API_KEY>' \
     --data '
     {
          "ids": ["test_1", "test_2"]
     }'
```

##### 请求参数

- `chat_id`: (*Path parameter*)
  关联聊天助手的 ID。
- `"ids"`: (*Body Parameter*), `list[string]`
  要删除的会话的 ID。如果未指定，将删除与指定聊天助手关联的所有会话。

##### 响应结果

成功示例：

```json
{
    "code": 0
}
```

失败示例：

```json
{
    "code": 102,
    "message": "The chat doesn't own the session"
}
```

---

### 与聊天助手对话

**POST** `/api/v1/chats/{chat_id}/completions`

向指定聊天助手提问以开始 AI 驱动的对话。

:::tip NOTE

- 在流式模式下，并非所有响应都包含引用，这取决于系统的判断。

- 在流式模式下，最后一条消息是一个空消息：

  ```json
  data:
  {
    "code": 0,
    "data": true
  }
  ```

:::

##### 请求详情

- Method: POST
- URL: `/api/v1/chats/{chat_id}/completions`
- Headers:
  - `'content-Type: application/json'`
  - `'Authorization: Bearer <YOUR_API_KEY>'`
- Body:
  - `"question"`: `string`
  - `"stream"`: `boolean`
  - `"session_id"`: `string` (optional)
  - `"user_id`: `string` (optional)

##### 请求示例

```bash
curl --request POST \
     --url http://{address}/api/v1/chats/{chat_id}/completions \
     --header 'Content-Type: application/json' \
     --header 'Authorization: Bearer <YOUR_API_KEY>' \
     --data-binary '
     {
     }'
```

```bash
curl --request POST \
     --url http://{address}/api/v1/chats/{chat_id}/completions \
     --header 'Content-Type: application/json' \
     --header 'Authorization: Bearer <YOUR_API_KEY>' \
     --data-binary '
     {
          "question": "Who are you",
          "stream": true,
          "session_id":"9fa7691cb85c11ef9c5f0242ac120005"
     }'
```

##### 请求参数

- `chat_id`: (*Path parameter*)
关联聊天助手的ID。
- `"question"`: (*Body parameter*), `string`, 必需
启动AI对话的问题。
- `"stream"`: (*Body parameter*), `boolean`
指示是否以流式方式输出响应：
  - `true`: 启用流式输出（默认）。
  - `false`: 禁用流式输出。
- `"session_id"`: (*Body parameter*)
会话ID。如果不提供，将生成新的会话。
- `"user_id"`: (*Body parameter*), `string`
可选的用户定义ID。仅在未提供`session_id`时有效。

##### 响应结果

Success without `session_id`:

```json
data:{
    "code": 0,
    "message": "",
    "data": {
        "answer": "Hi! I'm your assistant, what can I do for you?",
        "reference": {},
        "audio_binary": null,
        "id": null,
        "session_id": "b01eed84b85611efa0e90242ac120005"
    }
}
data:{
    "code": 0,
    "message": "",
    "data": true
}
```

Success with `session_id`:

```json
data:{
    "code": 0,
    "data": {
        "answer": "I am an intelligent assistant designed to help answer questions by summarizing content from a",
        "reference": {},
        "audio_binary": null,
        "id": "a84c5dd4-97b4-4624-8c3b-974012c8000d",
        "session_id": "82b0ab2a9c1911ef9d870242ac120006"
    }
}
data:{
    "code": 0,
    "data": {
        "answer": "I am an intelligent assistant designed to help answer questions by summarizing content from a knowledge base. My responses are based on the information available in the knowledge base and",
        "reference": {},
        "audio_binary": null,
        "id": "a84c5dd4-97b4-4624-8c3b-974012c8000d",
        "session_id": "82b0ab2a9c1911ef9d870242ac120006"
    }
}
data:{
    "code": 0,
    "data": {
        "answer": "I am an intelligent assistant designed to help answer questions by summarizing content from a knowledge base. My responses are based on the information available in the knowledge base and any relevant chat history.",
        "reference": {},
        "audio_binary": null,
        "id": "a84c5dd4-97b4-4624-8c3b-974012c8000d",
        "session_id": "82b0ab2a9c1911ef9d870242ac120006"
    }
}
data:{
    "code": 0,
    "data": {
        "answer": "I am an intelligent assistant designed to help answer questions by summarizing content from a knowledge base ##0$$. My responses are based on the information available in the knowledge base and any relevant chat history.",
        "reference": {
            "total": 1,
            "chunks": [
                {
                    "id": "faf26c791128f2d5e821f822671063bd",
                    "content": "xxxxxxxx",
                    "document_id": "dd58f58e888511ef89c90242ac120006",
                    "document_name": "1.txt",
                    "dataset_id": "8e83e57a884611ef9d760242ac120006",
                    "image_id": "",
                    "similarity": 0.7,
                    "vector_similarity": 0.0,
                    "term_similarity": 1.0,
                    "positions": [
                        ""
                    ]
                }
            ],
            "doc_aggs": [
                {
                    "doc_name": "1.txt",
                    "doc_id": "dd58f58e888511ef89c90242ac120006",
                    "count": 1
                }
            ]
        },
        "prompt": "xxxxxxxxxxx",
        "id": "a84c5dd4-97b4-4624-8c3b-974012c8000d",
        "session_id": "82b0ab2a9c1911ef9d870242ac120006"
    }
}
data:{
    "code": 0,
    "data": true
}
```

失败示例：

```json
{
    "code": 102,
    "message": "Please input your question."
}
```

---

### 创建与智能体的会话

**POST** `/api/v1/agents/{agent_id}/sessions`

创建与智能体的会话。

##### 请求详情

- Method: POST
- URL: `/api/v1/agents/{agent_id}/sessions?user_id={user_id}`
- Headers:
  - `'content-Type: application/json' or 'multipart/form-data'`
  - `'Authorization: Bearer <YOUR_API_KEY>'`
- Body:
  - the required parameters:`str`
  - other parameters:
    The parameters specified in the **Begin** component.

##### 请求示例

If the **Begin** component in your agent does not take required parameters:

```bash
curl --request POST \
     --url http://{address}/api/v1/agents/{agent_id}/sessions \
     --header 'Content-Type: application/json' \
     --header 'Authorization: Bearer <YOUR_API_KEY>' \
     --data '{
     }'
```

If the **Begin** component in your agent takes required parameters:

```bash
curl --request POST \
     --url http://{address}/api/v1/agents/{agent_id}/sessions \
     --header 'Content-Type: application/json' \
     --header 'Authorization: Bearer <YOUR_API_KEY>' \
     --data '{
            "lang":"Japanese",
            "file":"Who are you"
     }'
```

If the **Begin** component in your agent takes required file parameters:

```bash
curl --request POST \
     --url http://{address}/api/v1/agents/{agent_id}/sessions?user_id={user_id} \
     --header 'Content-Type: multipart/form-data' \
     --header 'Authorization: Bearer <YOUR_API_KEY>' \
     --form '<FILE_KEY>=@./test1.png'
```

##### 请求参数

- `agent_id`: (*Path parameter*)
  关联智能体的 ID。
- `user_id`: (*Filter parameter*)
  创建会话时上传文件时用于解析文档（特别是图像）的可选用户定义 ID。

##### 响应结果

成功示例：

```json
{
    "code": 0,
    "data": {
        "agent_id": "b4a39922b76611efaa1a0242ac120006",
        "dsl": {
            "answer": [],
            "components": {
                "Answer:GreenReadersDrum": {
                    "downstream": [],
                    "obj": {
                        "component_name": "Answer",
                        "inputs": [],
                        "output": null,
                        "params": {}
                    },
                    "upstream": []
                },
                "begin": {
                    "downstream": [],
                    "obj": {
                        "component_name": "Begin",
                        "inputs": [],
                        "output": {},
                        "params": {}
                    },
                    "upstream": []
                }
            },
            "embed_id": "",
            "graph": {
                "edges": [],
                "nodes": [
                    {
                        "data": {
                            "label": "Begin",
                            "name": "begin"
                        },
                        "dragging": false,
                        "height": 44,
                        "id": "begin",
                        "position": {
                            "x": 53.25688640427177,
                            "y": 198.37155679786412
                        },
                        "positionAbsolute": {
                            "x": 53.25688640427177,
                            "y": 198.37155679786412
                        },
                        "selected": false,
                        "sourcePosition": "left",
                        "targetPosition": "right",
                        "type": "beginNode",
                        "width": 200
                    },
                    {
                        "data": {
                            "form": {},
                            "label": "Answer",
                            "name": "dialog_0"
                        },
                        "dragging": false,
                        "height": 44,
                        "id": "Answer:GreenReadersDrum",
                        "position": {
                            "x": 360.43473114516974,
                            "y": 207.29298425089348
                        },
                        "positionAbsolute": {
                            "x": 360.43473114516974,
                            "y": 207.29298425089348
                        },
                        "selected": false,
                        "sourcePosition": "right",
                        "targetPosition": "left",
                        "type": "logicNode",
                        "width": 200
                    }
                ]
            },
            "history": [],
            "messages": [],
            "path": [
                [
                    "begin"
                ],
                []
            ],
            "reference": []
        },
        "id": "2581031eb7a311efb5200242ac120005",
        "message": [
            {
                "content": "Hi! I'm your smart assistant. What can I do for you?",
                "role": "assistant"
            }
        ],
        "source": "agent",
        "user_id": "69736c5e723611efb51b0242ac120007"
    }
}
```

失败示例：

```json
{
    "code": 102,
    "message": "Agent not found."
}
```

---

### 与智能体对话

**POST** `/api/v1/agents/{agent_id}/completions`

向指定智能体提问以开始 AI 驱动的对话。

:::tip NOTE

- 在流式模式下，并非所有响应都包含引用，这取决于系统的判断。
- 在流式模式下，最后一条消息是一个空消息：

  ```json
  data:
  {
    "code": 0,
    "data": true
  }
  ```

:::

##### 请求详情

- Method: POST
- URL: `/api/v1/agents/{agent_id}/completions`
- Headers:
  - `'content-Type: application/json'`
  - `'Authorization: Bearer <YOUR_API_KEY>'`
- Body:
  - `"question"`: `string`
  - `"stream"`: `boolean`
  - `"session_id"`: `string`
  - `"user_id"`: `string`(optional)
  - `"sync_dsl"`: `boolean` (optional)
  - other parameters: `string`

:::info IMPORTANT
You can include custom parameters in the request body, but first ensure they are defined in the [Begin](../guides/agent/agent_component_reference/begin.mdx) agent component.
:::

##### 请求示例

- If the **Begin** component does not take parameters, the following code will create a session.

```bash
curl --request POST \
     --url http://{address}/api/v1/agents/{agent_id}/completions \
     --header 'Content-Type: application/json' \
     --header 'Authorization: Bearer <YOUR_API_KEY>' \
     --data-binary '
     {
     }'
```

- If the **Begin** component takes parameters, the following code will create a session.

```bash
curl --request POST \
     --url http://{address}/api/v1/agents/{agent_id}/completions \
     --header 'Content-Type: application/json' \
     --header 'Authorization: Bearer <YOUR_API_KEY>' \
     --data-binary '
     {
          "lang":"English",
          "file":"How is the weather tomorrow?"
     }'
```

The following code will execute the completion process

```bash
curl --request POST \
     --url http://{address}/api/v1/agents/{agent_id}/completions \
     --header 'Content-Type: application/json' \
     --header 'Authorization: Bearer <YOUR_API_KEY>' \
     --data-binary '
     {
          "question": "Hello",
          "stream": true,
          "session_id": "cb2f385cb86211efa36e0242ac120005"
     }'
```

##### 请求参数

- `agent_id`: (*Path parameter*), `string`
  关联智能体的 ID。
- `"question"`: (*Body Parameter*), `string`, *必需*
  开始 AI 驱动对话的问题。
- `"stream"`: (*Body Parameter*), `boolean`
  指示是否以流式方式输出响应：
  - `true`：启用流式传输（默认）。
  - `false`：禁用流式传输。
- `"session_id"`: (*Body Parameter*)
  会话的 ID。如果未提供，将生成新会话。
- `"user_id"`: (*Body parameter*), `string`
  可选的用户定义 ID。仅在未提供 `session_id` 时有效。
- `"sync_dsl"`: (*Body parameter*), `boolean`
  当智能体被修改时，是否将更改同步到现有会话，默认为 `false`。
- 其他参数：(*Body Parameter*)
  在**Begin**组件中指定的参数。

##### 响应结果

success without `session_id` provided and with no parameters specified in the **Begin** component:

```json
data:{
    "code": 0,
    "message": "",
    "data": {
        "answer": "Hi! I'm your smart assistant. What can I do for you?",
        "reference": {},
        "id": "31e6091d-88d4-441b-ac65-eae1c055be7b",
        "session_id": "2987ad3eb85f11efb2a70242ac120005"
    }
}
data:{
    "code": 0,
    "message": "",
    "data": true
}
```

Success without `session_id` provided and with parameters specified in the **Begin** component:

```json
data:{
    "code": 0,
    "message": "",
    "data": {
        "session_id": "eacb36a0bdff11ef97120242ac120006",
        "answer": "",
        "reference": [],
        "param": [
            {
                "key": "lang",
                "name": "Target Language",
                "optional": false,
                "type": "line",
                "value": "English"
            },
            {
                "key": "file",
                "name": "Files",
                "optional": false,
                "type": "file",
                "value": "How is the weather tomorrow?"
            },
            {
                "key": "hhyt",
                "name": "hhty",
                "optional": true,
                "type": "line"
            }
        ]
    }
}
data:
```

Success with parameters specified in the **Begin** component:

```json
data:{
    "code": 0,
    "message": "",
    "data": {
        "answer": "How",
        "reference": {},
        "id": "0379ac4c-b26b-4a44-8b77-99cebf313fdf",
        "session_id": "4399c7d0b86311efac5b0242ac120005"
    }
}
data:{
    "code": 0,
    "message": "",
    "data": {
        "answer": "How is",
        "reference": {},
        "id": "0379ac4c-b26b-4a44-8b77-99cebf313fdf",
        "session_id": "4399c7d0b86311efac5b0242ac120005"
    }
}
data:{
    "code": 0,
    "message": "",
    "data": {
        "answer": "How is the",
        "reference": {},
        "id": "0379ac4c-b26b-4a44-8b77-99cebf313fdf",
        "session_id": "4399c7d0b86311efac5b0242ac120005"
    }
}
data:{
    "code": 0,
    "message": "",
    "data": {
        "answer": "How is the weather",
        "reference": {},
        "id": "0379ac4c-b26b-4a44-8b77-99cebf313fdf",
        "session_id": "4399c7d0b86311efac5b0242ac120005"
    }
}
data:{
    "code": 0,
    "message": "",
    "data": {
        "answer": "How is the weather tomorrow",
        "reference": {},
        "id": "0379ac4c-b26b-4a44-8b77-99cebf313fdf",
        "session_id": "4399c7d0b86311efac5b0242ac120005"
    }
}
data:{
    "code": 0,
    "message": "",
    "data": {
        "answer": "How is the weather tomorrow?",
        "reference": {},
        "id": "0379ac4c-b26b-4a44-8b77-99cebf313fdf",
        "session_id": "4399c7d0b86311efac5b0242ac120005"
    }
}
data:{
    "code": 0,
    "message": "",
    "data": {
        "answer": "How is the weather tomorrow?",
        "reference": {},
        "id": "0379ac4c-b26b-4a44-8b77-99cebf313fdf",
        "session_id": "4399c7d0b86311efac5b0242ac120005"
    }
}
data:{
    "code": 0,
    "message": "",
    "data": true
}
```

失败示例：

```json
{
    "code": 102,
    "message": "`question` is required."
}
```

---

### 列出智能体会话

**GET** `/api/v1/agents/{agent_id}/sessions?page={page}&page_size={page_size}&orderby={orderby}&desc={desc}&id={session_id}&user_id={user_id}&dsl={dsl}`

列出与指定智能体关联的会话。

##### 请求详情

- Method: GET
- URL: `/api/v1/agents/{agent_id}/sessions?page={page}&page_size={page_size}&orderby={orderby}&desc={desc}&id={session_id}`
- Headers:
  - `'Authorization: Bearer <YOUR_API_KEY>'`

##### 请求示例

```bash
curl --request GET \
     --url http://{address}/api/v1/agents/{agent_id}/sessions?page={page}&page_size={page_size}&orderby={orderby}&desc={desc}&id={session_id}&user_id={user_id} \
     --header 'Authorization: Bearer <YOUR_API_KEY>'
```

##### 请求参数

- `agent_id`: (*Path parameter*)
  关联智能体的 ID。
- `page`: (*Filter parameter*), `integer`
  指定显示会话的页面。默认为 `1`。
- `page_size`: (*Filter parameter*), `integer`
  每页会话的数量。默认为 `30`。
- `orderby`: (*Filter parameter*), `string`
  会话排序的字段。可用选项：
  - `create_time`（默认）
  - `update_time`
- `desc`: (*Filter parameter*), `boolean`
  指示检索到的会话是否按降序排序。默认为 `true`。
- `id`: (*Filter parameter*), `string`
  要检索的智能体会话的 ID。
- `user_id`: (*Filter parameter*), `string`
  创建会话时传入的可选用户定义 ID。
- `dsl`: (*Filter parameter*), `boolean`
  指示是否在响应中包含会话的 dsl 字段。默认为 `true`。

##### 响应结果

成功示例：

```json
{
    "code": 0,
    "data": [{
        "agent_id": "e9e2b9c2b2f911ef801d0242ac120006",
        "dsl": {
            "answer": [],
            "components": {
                "Answer:OrangeTermsBurn": {
                    "downstream": [],
                    "obj": {
                        "component_name": "Answer",
                        "params": {}
                    },
                    "upstream": []
                },
                "Generate:SocialYearsRemain": {
                    "downstream": [],
                    "obj": {
                        "component_name": "Generate",
                        "params": {
                            "cite": true,
                            "frequency_penalty": 0.7,
                            "llm_id": "gpt-4o___OpenAI-API@OpenAI-API-Compatible",
                            "message_history_window_size": 12,
                            "parameters": [],
                            "presence_penalty": 0.4,
                            "prompt": "Please summarize the following paragraph. Pay attention to the numbers and do not make things up. The paragraph is as follows:\n{input}\nThis is what you need to summarize.",
                            "temperature": 0.1,
                            "top_p": 0.3
                        }
                    },
                    "upstream": []
                },
                "begin": {
                    "downstream": [],
                    "obj": {
                        "component_name": "Begin",
                        "params": {}
                    },
                    "upstream": []
                }
            },
            "graph": {
                "edges": [],
                "nodes": [
                    {
                        "data": {
                            "label": "Begin",
                            "name": "begin"
                        },
                        "height": 44,
                        "id": "begin",
                        "position": {
                            "x": 50,
                            "y": 200
                        },
                        "sourcePosition": "left",
                        "targetPosition": "right",
                        "type": "beginNode",
                        "width": 200
                    },
                    {
                        "data": {
                            "form": {
                                "cite": true,
                                "frequencyPenaltyEnabled": true,
                                "frequency_penalty": 0.7,
                                "llm_id": "gpt-4o___OpenAI-API@OpenAI-API-Compatible",
                                "maxTokensEnabled": true,
                                "message_history_window_size": 12,
                                "parameters": [],
                                "presencePenaltyEnabled": true,
                                "presence_penalty": 0.4,
                                "prompt": "Please summarize the following paragraph. Pay attention to the numbers and do not make things up. The paragraph is as follows:\n{input}\nThis is what you need to summarize.",
                                "temperature": 0.1,
                                "temperatureEnabled": true,
                                "topPEnabled": true,
                                "top_p": 0.3
                            },
                            "label": "Generate",
                            "name": "Generate Answer_0"
                        },
                        "dragging": false,
                        "height": 105,
                        "id": "Generate:SocialYearsRemain",
                        "position": {
                            "x": 561.3457829707513,
                            "y": 178.7211182312641
                        },
                        "positionAbsolute": {
                            "x": 561.3457829707513,
                            "y": 178.7211182312641
                        },
                        "selected": true,
                        "sourcePosition": "right",
                        "targetPosition": "left",
                        "type": "generateNode",
                        "width": 200
                    },
                    {
                        "data": {
                            "form": {},
                            "label": "Answer",
                            "name": "Dialogue_0"
                        },
                        "height": 44,
                        "id": "Answer:OrangeTermsBurn",
                        "position": {
                            "x": 317.2368194777658,
                            "y": 218.30635555445093
                        },
                        "sourcePosition": "right",
                        "targetPosition": "left",
                        "type": "logicNode",
                        "width": 200
                    }
                ]
            },
            "history": [],
            "messages": [],
            "path": [],
            "reference": []
        },
        "id": "792dde22b2fa11ef97550242ac120006",
        "message": [
            {
                "content": "Hi! I'm your smart assistant. What can I do for you?",
                "role": "assistant"
            }
        ],
        "source": "agent",
        "user_id": ""
    }]
}
```

失败示例：

```json
{
    "code": 102,
    "message": "You don't own the agent ccd2f856b12311ef94ca0242ac1200052."
}
```

---

### 删除智能体的会话

**DELETE** `/api/v1/agents/{agent_id}/sessions`

根据 ID 删除智能体的会话。

##### 请求详情

- Method: DELETE
- URL: `/api/v1/agents/{agent_id}/sessions`
- Headers:
  - `'content-Type: application/json'`
  - `'Authorization: Bearer <YOUR_API_KEY>'`
- Body:
  - `"ids"`: `list[string]`

##### 请求示例

```bash
curl --request DELETE \
     --url http://{address}/api/v1/agents/{agent_id}/sessions \
     --header 'Content-Type: application/json' \
     --header 'Authorization: Bearer <YOUR_API_KEY>' \
     --data '
     {
          "ids": ["test_1", "test_2"]
     }'
```

##### 请求参数

- `agent_id`: (*Path parameter*)
  关联智能体的 ID。
- `"ids"`: (*Body Parameter*), `list[string]`
  要删除的会话的 ID。如果未指定，将删除与指定智能体关联的所有会话。

##### 响应结果

成功示例：

```json
{
    "code": 0
}
```

失败示例：

```json
{
    "code": 102,
    "message": "The agent doesn't own the session cbd31e52f73911ef93b232903b842af6"
}
```

---

### 生成相关问题

**POST** `/v1/sessions/related_questions`

从用户的原始查询生成五到十个替代问题字符串，以检索更相关的搜索结果。

此操作需要`Bearer登录令牌`，通常在24小时内过期。您可以在浏览器中的请求头中轻松找到它，如下所示：
![Image](https://raw.githubusercontent.com/infiniflow/ragflow-docs/main/images/login_token.jpg)

:::tip NOTE
聊天模型根据指令自主确定生成问题的数量，通常在五到十个之间。
:::

##### 请求详情

- Method: POST
- URL: `/v1/sessions/related_questions`
- Headers:
  - `'content-Type: application/json'`
  - `'Authorization: Bearer <YOUR_LOGIN_TOKEN>'`
- Body:
  - `"question"`: `string`

##### 请求示例

```bash
curl --request POST \
     --url http://{address}/v1/sessions/related_questions \
     --header 'Content-Type: application/json' \
     --header 'Authorization: Bearer <YOUR_LOGIN_TOKEN>' \
     --data '
     {
          "question": "What are the key advantages of Neovim over Vim?"
     }'
```

##### 请求参数

- `"question"`: (*Body Parameter*), `string`
  原始用户问题。

##### 响应结果

成功示例：

```json
{
    "code": 0,
    "data": [
        "Neovim在功能方面相较于Vim有哪些优势？",
        "Neovim与Vim相比有哪些好处？",
        "Neovim提供了哪些Vim中没有的优势？",
        "Neovim在哪些方面在功能上超越了Vim？",
        "与Vim相比，Neovim有哪些最显著的改进？",
        "Neovim相较于Vim有哪些独特的优势？",
        "从用户体验的角度来看，Neovim在哪些方面与Vim不同，更具优势？",
        "从Vim切换到Neovim的主要原因有哪些？",
        "Neovim的哪些特性被认为比Vim更先进？"
    ],
    "message": "success"
}
```

失败示例：

```json
{
    "code": 401,
    "data": null,
    "message": "<Unauthorized '401: Unauthorized'>"
}
```

---

## 智能体管理

---

### 列出智能体

**GET** `/api/v1/agents?page={page}&page_size={page_size}&orderby={orderby}&desc={desc}&name={agent_name}&id={agent_id}`

列出智能体。

##### 请求详情

- Method: GET
- URL: `/api/v1/agents?page={page}&page_size={page_size}&orderby={orderby}&desc={desc}&name={agent_name}&id={agent_id}`
- Headers:
  - `'Authorization: Bearer <YOUR_API_KEY>'`

##### 请求示例

```bash
curl --request GET \
     --url http://{address}/api/v1/agents?page={page}&page_size={page_size}&orderby={orderby}&desc={desc}&name={agent_name}&id={agent_id} \
     --header 'Authorization: Bearer <YOUR_API_KEY>'
```

##### 请求参数

- `page`: (*Filter parameter*), `integer`
  指定显示智能体的页面。默认为 `1`。
- `page_size`: (*Filter parameter*), `integer`
  每页智能体的数量。默认为 `30`。
- `orderby`: (*Filter parameter*), `string`
  结果排序的属性。可用选项：
  - `create_time`（默认）
  - `update_time`
- `desc`: (*Filter parameter*), `boolean`
  指示检索到的智能体是否按降序排序。默认为 `true`。
- `id`: (*Filter parameter*), `string`
  要检索的智能体的 ID。
- `name`: (*Filter parameter*), `string`
  要检索的智能体的名称。

##### 响应结果

成功示例：

```json
{
    "code": 0,
    "data": [
        {
            "avatar": null,
            "canvas_type": null,
            "create_date": "Thu, 05 Dec 2024 19:10:36 GMT",
            "create_time": 1733397036424,
            "description": null,
            "dsl": {
                "answer": [],
                "components": {
                    "begin": {
                        "downstream": [],
                        "obj": {
                            "component_name": "Begin",
                            "params": {}
                        },
                        "upstream": []
                    }
                },
                "graph": {
                    "edges": [],
                    "nodes": [
                        {
                            "data": {
                                "label": "Begin",
                                "name": "begin"
                            },
                            "height": 44,
                            "id": "begin",
                            "position": {
                                "x": 50,
                                "y": 200
                            },
                            "sourcePosition": "left",
                            "targetPosition": "right",
                            "type": "beginNode",
                            "width": 200
                        }
                    ]
                },
                "history": [],
                "messages": [],
                "path": [],
                "reference": []
            },
            "id": "8d9ca0e2b2f911ef9ca20242ac120006",
            "title": "123465",
            "update_date": "Thu, 05 Dec 2024 19:10:56 GMT",
            "update_time": 1733397056801,
            "user_id": "69736c5e723611efb51b0242ac120007"
        }
    ]
}
```

失败示例：

```json
{
    "code": 102,
    "message": "The agent doesn't exist."
}
```

---

### 创建智能体

**POST** `/api/v1/agents`

创建智能体。

##### 请求详情

- Method: POST
- URL: `/api/v1/agents`
- Headers:
  - `'Content-Type: application/json`
  - `'Authorization: Bearer <YOUR_API_KEY>'`
- Body:
  - `"title"`: `string`
  - `"description"`: `string`
  - `"dsl"`: `object`

##### 请求示例

```bash
curl --request POST \
     --url http://{address}/api/v1/agents \
     --header 'Content-Type: application/json' \
     --header 'Authorization: Bearer <YOUR_API_KEY>' \
     --data '{
         "title": "Test Agent",
         "description": "A test agent",
         "dsl": {
           // ... Canvas DSL here ...
         }
     }'
```

##### 请求参数

- `title`: (*Body parameter*), `string`, *必需*
  智能体的标题。
- `description`: (*Body parameter*), `string`
  智能体的描述。默认为 `None`。
- `dsl`: (*Body parameter*), `object`, *必需*
  智能体的画布 DSL 对象。

##### 响应结果

成功示例：

```json
{
    "code": 0,
    "data": true,
    "message": "success"
}
```

失败示例：

```json
{
    "code": 102,
    "message": "Agent with title test already exists."
}
```

---

### 更新智能体

**PUT** `/api/v1/agents/{agent_id}`

根据 ID 更新智能体。

##### 请求详情

- Method: PUT
- URL: `/api/v1/agents/{agent_id}`
- Headers:
  - `'Content-Type: application/json`
  - `'Authorization: Bearer <YOUR_API_KEY>'`
- Body:
  - `"title"`: `string`
  - `"description"`: `string`
  - `"dsl"`: `object`

##### 请求示例

```bash
curl --request PUT \
     --url http://{address}/api/v1/agents/58af890a2a8911f0a71a11b922ed82d6 \
     --header 'Content-Type: application/json' \
     --header 'Authorization: Bearer <YOUR_API_KEY>' \
     --data '{
         "title": "Test Agent",
         "description": "A test agent",
         "dsl": {
           // ... Canvas DSL here ...
         }
     }'
```

##### 请求参数

- `agent_id`: (*Path parameter*), `string`
  要更新的智能体的 ID。
- `title`: (*Body parameter*), `string`
  智能体的标题。
- `description`: (*Body parameter*), `string`
  智能体的描述。
- `dsl`: (*Body parameter*), `object`
  智能体的画布 DSL 对象。

仅指定请求体中您想要更改的参数。如果参数不存在或为`None`，则不会更新。

##### 响应结果

成功示例：

```json
{
    "code": 0,
    "data": true,
    "message": "success"
}
```

失败示例：

```json
{
    "code": 103,
    "message": "Only owner of canvas authorized for this operation."
}
```

---

### 删除智能体

**DELETE** `/api/v1/agents/{agent_id}`

根据 ID 删除智能体。

##### 请求详情

- Method: DELETE
- URL: `/api/v1/agents/{agent_id}`
- Headers:
  - `'Content-Type: application/json`
  - `'Authorization: Bearer <YOUR_API_KEY>'`

##### 请求示例

```bash
curl --request DELETE \
     --url http://{address}/api/v1/agents/58af890a2a8911f0a71a11b922ed82d6 \
     --header 'Content-Type: application/json' \
     --header 'Authorization: Bearer <YOUR_API_KEY>' \
     --data '{}'
```

##### 请求参数

- `agent_id`: (*Path parameter*), `string`
  要删除的智能体的 ID。

##### 响应结果

成功示例：

```json
{
    "code": 0,
    "data": true,
    "message": "success"
}
```

失败示例：

```json
{
    "code": 103,
    "message": "Only owner of canvas authorized for this operation."
}
```

---
