# RAGFlow 数据库表结构说明文档

## 概述

本文档详细描述了 RAGFlow 系统的数据库表结构，包含用户管理、租户管理、知识库管理、对话管理、文档处理等核心功能模块的数据表设计。

## 数据表列表

### 1. 用户管理模块

#### 1.1 user - 用户表
存储系统用户的基本信息和认证相关数据。

| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 索引 | 说明 |
|--------|------|------|----------|--------|------|------|
| id | VARCHAR | 32 | NOT NULL | - | PRIMARY KEY | 用户唯一标识 |
| access_token | VARCHAR | 255 | NULL | - | INDEX | 访问令牌 |
| nickname | VARCHAR | 100 | NOT NULL | - | INDEX | 用户昵称 |
| password | VARCHAR | 255 | NULL | - | INDEX | 用户密码 |
| email | VARCHAR | 255 | NOT NULL | - | INDEX | 用户邮箱 |
| avatar | TEXT | - | NULL | - | - | 用户头像(base64字符串) |
| language | VARCHAR | 32 | NULL | Chinese | INDEX | 语言设置(English/Chinese) |
| color_schema | VARCHAR | 32 | NULL | Bright | INDEX | 颜色主题(Bright/Dark) |
| timezone | VARCHAR | 64 | NULL | UTC+8\tAsia/Shanghai | INDEX | 时区设置 |
| last_login_time | DATETIME | - | NULL | - | INDEX | 最后登录时间 |
| is_authenticated | VARCHAR | 1 | NOT NULL | 1 | INDEX | 是否已认证 |
| is_active | VARCHAR | 1 | NOT NULL | 1 | INDEX | 是否激活 |
| is_anonymous | VARCHAR | 1 | NOT NULL | 0 | INDEX | 是否匿名用户 |
| login_channel | VARCHAR | - | NULL | - | INDEX | 登录渠道 |
| status | VARCHAR | 1 | NULL | 1 | INDEX | 用户状态(0:废弃, 1:有效) |
| is_superuser | BOOLEAN | - | NULL | FALSE | INDEX | 是否超级用户 |
| create_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | - | 创建时间 |
| update_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | - | 更新时间 |

#### 1.2 tenant - 租户表
存储租户(组织)的基本信息和默认配置。

| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 索引 | 说明 |
|--------|------|------|----------|--------|------|------|
| id | VARCHAR | 32 | NOT NULL | - | PRIMARY KEY | 租户唯一标识 |
| name | VARCHAR | 100 | NULL | - | INDEX | 租户名称 |
| public_key | VARCHAR | 255 | NULL | - | INDEX | 公钥 |
| llm_id | VARCHAR | 128 | NOT NULL | - | INDEX | 默认大语言模型ID |
| embd_id | VARCHAR | 128 | NOT NULL | - | INDEX | 默认嵌入模型ID |
| asr_id | VARCHAR | 128 | NOT NULL | - | INDEX | 默认语音识别模型ID |
| img2txt_id | VARCHAR | 128 | NOT NULL | - | INDEX | 默认图像转文本模型ID |
| rerank_id | VARCHAR | 128 | NOT NULL | - | INDEX | 默认重排序模型ID |
| tts_id | VARCHAR | 256 | NULL | - | INDEX | 默认文本转语音模型ID |
| parser_ids | VARCHAR | 256 | NOT NULL | - | INDEX | 文档解析器ID列表 |
| credit | INT | - | NOT NULL | 512 | INDEX | 积分余额 |
| status | VARCHAR | 1 | NULL | 1 | INDEX | 租户状态(0:废弃, 1:有效) |
| create_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | - | 创建时间 |
| update_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | - | 更新时间 |

#### 1.3 user_tenant - 用户租户关联表
存储用户与租户的关联关系和角色信息。

| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 索引 | 说明 |
|--------|------|------|----------|--------|------|------|
| id | VARCHAR | 32 | NOT NULL | - | PRIMARY KEY | 关联关系唯一标识 |
| user_id | VARCHAR | 32 | NOT NULL | - | INDEX | 用户ID |
| tenant_id | VARCHAR | 32 | NOT NULL | - | INDEX | 租户ID |
| role | VARCHAR | 32 | NOT NULL | - | INDEX | 用户在租户中的角色 |
| invited_by | VARCHAR | 32 | NOT NULL | - | INDEX | 邀请人ID |
| status | VARCHAR | 1 | NULL | 1 | INDEX | 关联状态(0:废弃, 1:有效) |
| create_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | - | 创建时间 |
| update_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | - | 更新时间 |

#### 1.4 invitation_code - 邀请码表
存储系统邀请码信息。

| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 索引 | 说明 |
|--------|------|------|----------|--------|------|------|
| id | VARCHAR | 32 | NOT NULL | - | PRIMARY KEY | 邀请码唯一标识 |
| code | VARCHAR | 32 | NOT NULL | - | INDEX | 邀请码 |
| visit_time | DATETIME | - | NULL | - | INDEX | 访问时间 |
| user_id | VARCHAR | 32 | NULL | - | INDEX | 使用邀请码的用户ID |
| tenant_id | VARCHAR | 32 | NULL | - | INDEX | 关联的租户ID |
| status | VARCHAR | 1 | NULL | 1 | INDEX | 邀请码状态(0:废弃, 1:有效) |
| create_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | - | 创建时间 |
| update_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | - | 更新时间 |

### 2. 模型管理模块

#### 2.1 llm_factories - 大语言模型厂商表
存储支持的大语言模型厂商信息。

| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 索引 | 说明 |
|--------|------|------|----------|--------|------|------|
| name | VARCHAR | 128 | NOT NULL | - | PRIMARY KEY | 厂商名称 |
| logo | TEXT | - | NULL | - | - | 厂商Logo(base64字符串) |
| tags | VARCHAR | 255 | NOT NULL | - | INDEX | 支持的模型类型标签 |
| status | VARCHAR | 1 | NULL | 1 | INDEX | 厂商状态(0:废弃, 1:有效) |
| create_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | - | 创建时间 |
| update_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | - | 更新时间 |

#### 2.2 llm - 大语言模型表
存储具体的大语言模型信息。

| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 索引 | 说明 |
|--------|------|------|----------|--------|------|------|
| llm_name | VARCHAR | 128 | NOT NULL | - | INDEX | 模型名称 |
| model_type | VARCHAR | 128 | NOT NULL | - | INDEX | 模型类型(LLM/Text Embedding/Image2Text/ASR) |
| fid | VARCHAR | 128 | NOT NULL | - | INDEX | 厂商ID |
| max_tokens | INT | - | NOT NULL | 0 | - | 最大token数 |
| tags | VARCHAR | 255 | NOT NULL | - | INDEX | 模型标签 |
| is_tools | BOOLEAN | - | NOT NULL | FALSE | - | 是否支持工具调用 |
| status | VARCHAR | 1 | NULL | 1 | INDEX | 模型状态(0:废弃, 1:有效) |
| create_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | - | 创建时间 |
| update_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | - | 更新时间 |

**复合主键**: (fid, llm_name)

#### 2.3 tenant_llm - 租户模型配置表
存储租户的模型配置信息。

| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 索引 | 说明 |
|--------|------|------|----------|--------|------|------|
| tenant_id | VARCHAR | 32 | NOT NULL | - | INDEX | 租户ID |
| llm_factory | VARCHAR | 128 | NOT NULL | - | INDEX | 模型厂商名称 |
| model_type | VARCHAR | 128 | NULL | - | INDEX | 模型类型 |
| llm_name | VARCHAR | 128 | NULL | - | INDEX | 模型名称 |
| api_key | VARCHAR | 2048 | NULL | - | INDEX | API密钥 |
| api_base | VARCHAR | 255 | NULL | - | - | API基础URL |
| max_tokens | INT | - | NOT NULL | 8192 | INDEX | 最大token数 |
| used_tokens | INT | - | NOT NULL | 0 | INDEX | 已使用token数 |
| create_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | - | 创建时间 |
| update_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | - | 更新时间 |

**复合主键**: (tenant_id, llm_factory, llm_name)

#### 2.4 tenant_langfuse - 租户Langfuse配置表
存储租户的Langfuse监控配置。

| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 索引 | 说明 |
|--------|------|------|----------|--------|------|------|
| tenant_id | VARCHAR | 32 | NOT NULL | - | PRIMARY KEY | 租户ID |
| secret_key | VARCHAR | 2048 | NOT NULL | - | INDEX | Langfuse密钥 |
| public_key | VARCHAR | 2048 | NOT NULL | - | INDEX | Langfuse公钥 |
| host | VARCHAR | 128 | NOT NULL | - | INDEX | Langfuse主机地址 |
| create_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | - | 创建时间 |
| update_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | - | 更新时间 |

### 3. 知识库管理模块

#### 3.1 knowledgebase - 知识库表
存储知识库的基本信息和配置。

| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 索引 | 说明 |
|--------|------|------|----------|--------|------|------|
| id | VARCHAR | 32 | NOT NULL | - | PRIMARY KEY | 知识库唯一标识 |
| avatar | TEXT | - | NULL | - | - | 知识库头像(base64字符串) |
| tenant_id | VARCHAR | 32 | NOT NULL | - | INDEX | 租户ID |
| name | VARCHAR | 128 | NOT NULL | - | INDEX | 知识库名称 |
| language | VARCHAR | 32 | NULL | Chinese | INDEX | 语言设置 |
| description | TEXT | - | NULL | - | - | 知识库描述 |
| embd_id | VARCHAR | 128 | NOT NULL | - | INDEX | 嵌入模型ID |
| permission | VARCHAR | 16 | NOT NULL | me | INDEX | 权限设置(me/team) |
| created_by | VARCHAR | 32 | NOT NULL | - | INDEX | 创建者ID |
| doc_num | INT | - | NOT NULL | 0 | INDEX | 文档数量 |
| token_num | INT | - | NOT NULL | 0 | INDEX | token总数 |
| chunk_num | INT | - | NOT NULL | 0 | INDEX | 文档块数量 |
| similarity_threshold | FLOAT | - | NOT NULL | 0.2 | INDEX | 相似度阈值 |
| vector_similarity_weight | FLOAT | - | NOT NULL | 0.3 | INDEX | 向量相似度权重 |
| parser_id | VARCHAR | 32 | NOT NULL | naive | INDEX | 解析器ID |
| parser_config | JSON | - | NOT NULL | {"pages": [[1, 1000000]]} | - | 解析器配置 |
| pagerank | INT | - | NOT NULL | 0 | - | 页面排名 |
| status | VARCHAR | 1 | NULL | 1 | INDEX | 知识库状态(0:废弃, 1:有效) |
| create_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | - | 创建时间 |
| update_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | - | 更新时间 |

#### 3.2 document - 文档表
存储知识库中的文档信息。

| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 索引 | 说明 |
|--------|------|------|----------|--------|------|------|
| id | VARCHAR | 32 | NOT NULL | - | PRIMARY KEY | 文档唯一标识 |
| thumbnail | TEXT | - | NULL | - | - | 文档缩略图(base64字符串) |
| kb_id | VARCHAR | 256 | NOT NULL | - | INDEX | 知识库ID |
| parser_id | VARCHAR | 32 | NOT NULL | - | INDEX | 解析器ID |
| parser_config | JSON | - | NOT NULL | {"pages": [[1, 1000000]]} | - | 解析器配置 |
| source_type | VARCHAR | 128 | NOT NULL | local | INDEX | 文档来源类型 |
| type | VARCHAR | 32 | NOT NULL | - | INDEX | 文件扩展名 |
| created_by | VARCHAR | 32 | NOT NULL | - | INDEX | 创建者ID |
| name | VARCHAR | 255 | NULL | - | INDEX | 文件名 |
| location | VARCHAR | 255 | NULL | - | INDEX | 存储位置 |
| size | INT | - | NOT NULL | 0 | INDEX | 文件大小 |
| token_num | INT | - | NOT NULL | 0 | INDEX | token数量 |
| chunk_num | INT | - | NOT NULL | 0 | INDEX | 文档块数量 |
| progress | FLOAT | - | NOT NULL | 0 | INDEX | 处理进度 |
| progress_msg | TEXT | - | NULL | - | - | 处理消息 |
| process_begin_at | DATETIME | - | NULL | - | INDEX | 处理开始时间 |
| process_duration | FLOAT | - | NOT NULL | 0 | - | 处理耗时 |
| meta_fields | JSON | - | NULL | {} | - | 元数据字段 |
| suffix | VARCHAR | 32 | NOT NULL | - | INDEX | 真实文件扩展名 |
| run | VARCHAR | 1 | NULL | 0 | INDEX | 运行状态(0:停止, 1:运行, 2:取消) |
| status | VARCHAR | 1 | NULL | 1 | INDEX | 文档状态(0:废弃, 1:有效) |
| create_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | - | 创建时间 |
| update_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | - | 更新时间 |

### 4. 文件管理模块

#### 4.1 file - 文件表
存储系统中的文件和文件夹信息。

| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 索引 | 说明 |
|--------|------|------|----------|--------|------|------|
| id | VARCHAR | 32 | NOT NULL | - | PRIMARY KEY | 文件唯一标识 |
| parent_id | VARCHAR | 32 | NOT NULL | - | INDEX | 父文件夹ID |
| tenant_id | VARCHAR | 32 | NOT NULL | - | INDEX | 租户ID |
| created_by | VARCHAR | 32 | NOT NULL | - | INDEX | 创建者ID |
| name | VARCHAR | 255 | NOT NULL | - | INDEX | 文件或文件夹名称 |
| location | VARCHAR | 255 | NULL | - | INDEX | 存储位置 |
| size | INT | - | NOT NULL | 0 | INDEX | 文件大小 |
| type | VARCHAR | 32 | NOT NULL | - | INDEX | 文件类型 |
| source_type | VARCHAR | 128 | NOT NULL | - | INDEX | 文件来源类型 |
| create_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | - | 创建时间 |
| update_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | - | 更新时间 |

#### 4.2 file2document - 文件文档关联表
存储文件与文档的关联关系。

| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 索引 | 说明 |
|--------|------|------|----------|--------|------|------|
| id | VARCHAR | 32 | NOT NULL | - | PRIMARY KEY | 关联关系唯一标识 |
| file_id | VARCHAR | 32 | NULL | - | INDEX | 文件ID |
| document_id | VARCHAR | 32 | NULL | - | INDEX | 文档ID |
| create_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | - | 创建时间 |
| update_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | - | 更新时间 |

#### 4.3 task - 任务表
存储文档处理任务信息。

| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 索引 | 说明 |
|--------|------|------|----------|--------|------|------|
| id | VARCHAR | 32 | NOT NULL | - | PRIMARY KEY | 任务唯一标识 |
| doc_id | VARCHAR | 32 | NOT NULL | - | INDEX | 文档ID |
| from_page | INT | - | NOT NULL | 0 | - | 起始页码 |
| to_page | INT | - | NOT NULL | 100000000 | - | 结束页码 |
| task_type | VARCHAR | 32 | NOT NULL | - | - | 任务类型 |
| priority | INT | - | NOT NULL | 0 | - | 任务优先级 |
| begin_at | DATETIME | - | NULL | - | INDEX | 任务开始时间 |
| process_duration | FLOAT | - | NOT NULL | 0 | - | 处理耗时 |
| progress | FLOAT | - | NOT NULL | 0 | INDEX | 任务进度 |
| progress_msg | TEXT | - | NULL | - | - | 进度消息 |
| retry_count | INT | - | NOT NULL | 0 | - | 重试次数 |
| digest | TEXT | - | NULL | - | - | 任务摘要 |
| chunk_ids | LONGTEXT | - | NULL | - | - | 文档块ID列表 |
| create_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | - | 创建时间 |
| update_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | - | 更新时间 |

### 5. 对话管理模块

#### 5.1 dialog - 对话应用表
存储对话应用的配置信息。

| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 索引 | 说明 |
|--------|------|------|----------|--------|------|------|
| id | VARCHAR | 32 | NOT NULL | - | PRIMARY KEY | 对话应用唯一标识 |
| tenant_id | VARCHAR | 32 | NOT NULL | - | INDEX | 租户ID |
| name | VARCHAR | 255 | NULL | - | INDEX | 对话应用名称 |
| description | TEXT | - | NULL | - | - | 对话应用描述 |
| icon | TEXT | - | NULL | - | - | 应用图标(base64字符串) |
| language | VARCHAR | 32 | NULL | Chinese | INDEX | 语言设置 |
| llm_id | VARCHAR | 128 | NOT NULL | - | - | 大语言模型ID |
| llm_setting | JSON | - | NOT NULL | 默认LLM设置 | - | 大语言模型参数配置 |
| prompt_type | VARCHAR | 16 | NOT NULL | simple | INDEX | 提示词类型(simple/advanced) |
| prompt_config | JSON | - | NOT NULL | 默认提示词配置 | - | 提示词配置 |
| similarity_threshold | FLOAT | - | NOT NULL | 0.2 | - | 相似度阈值 |
| vector_similarity_weight | FLOAT | - | NOT NULL | 0.3 | - | 向量相似度权重 |
| top_n | INT | - | NOT NULL | 6 | - | 返回结果数量 |
| top_k | INT | - | NOT NULL | 1024 | - | 检索候选数量 |
| do_refer | VARCHAR | 1 | NOT NULL | 1 | - | 是否插入引用索引 |
| rerank_id | VARCHAR | 128 | NOT NULL | - | - | 重排序模型ID |
| kb_ids | JSON | - | NOT NULL | [] | - | 关联的知识库ID列表 |
| status | VARCHAR | 1 | NULL | 1 | INDEX | 对话应用状态(0:废弃, 1:有效) |
| create_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | - | 创建时间 |
| update_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | - | 更新时间 |

#### 5.2 conversation - 对话记录表
存储用户与对话应用的对话记录。

| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 索引 | 说明 |
|--------|------|------|----------|--------|------|------|
| id | VARCHAR | 32 | NOT NULL | - | PRIMARY KEY | 对话记录唯一标识 |
| dialog_id | VARCHAR | 32 | NOT NULL | - | INDEX | 对话应用ID |
| name | VARCHAR | 255 | NULL | - | INDEX | 对话名称 |
| message | JSON | - | NULL | - | - | 对话消息内容 |
| reference | JSON | - | NULL | [] | - | 引用信息 |
| user_id | VARCHAR | 255 | NULL | - | INDEX | 用户ID |
| create_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | - | 创建时间 |
| update_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | - | 更新时间 |

### 6. API管理模块

#### 6.1 api_token - API令牌表
存储API访问令牌信息。

| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 索引 | 说明 |
|--------|------|------|----------|--------|------|------|
| tenant_id | VARCHAR | 32 | NOT NULL | - | INDEX | 租户ID |
| token | VARCHAR | 255 | NOT NULL | - | INDEX | API令牌 |
| dialog_id | VARCHAR | 32 | NULL | - | INDEX | 对话应用ID |
| source | VARCHAR | 16 | NULL | - | INDEX | 令牌来源(none/agent/dialog) |
| beta | VARCHAR | 255 | NULL | - | INDEX | Beta标识 |
| create_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | - | 创建时间 |
| update_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | - | 更新时间 |

**复合主键**: (tenant_id, token)

#### 6.2 api_4_conversation - API对话记录表
存储通过API进行的对话记录和统计信息。

| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 索引 | 说明 |
|--------|------|------|----------|--------|------|------|
| id | VARCHAR | 32 | NOT NULL | - | PRIMARY KEY | API对话记录唯一标识 |
| dialog_id | VARCHAR | 32 | NOT NULL | - | INDEX | 对话应用ID |
| user_id | VARCHAR | 255 | NOT NULL | - | INDEX | 用户ID |
| message | JSON | - | NULL | - | - | 对话消息内容 |
| reference | JSON | - | NULL | [] | - | 引用信息 |
| tokens | INT | - | NOT NULL | 0 | - | 消耗的token数量 |
| source | VARCHAR | 16 | NULL | - | INDEX | 对话来源(none/agent/dialog) |
| dsl | JSON | - | NULL | {} | - | DSL配置 |
| duration | FLOAT | - | NOT NULL | 0 | INDEX | 对话耗时 |
| round | INT | - | NOT NULL | 0 | INDEX | 对话轮次 |
| thumb_up | INT | - | NOT NULL | 0 | INDEX | 点赞数 |
| create_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | - | 创建时间 |
| update_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | - | 更新时间 |

### 7. 画布管理模块

#### 7.1 user_canvas - 用户画布表
存储用户创建的画布信息。

| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 索引 | 说明 |
|--------|------|------|----------|--------|------|------|
| id | VARCHAR | 32 | NOT NULL | - | PRIMARY KEY | 用户画布唯一标识 |
| avatar | TEXT | - | NULL | - | - | 画布头像(base64字符串) |
| user_id | VARCHAR | 255 | NOT NULL | - | INDEX | 用户ID |
| title | VARCHAR | 255 | NULL | - | - | 画布标题 |
| permission | VARCHAR | 16 | NOT NULL | me | INDEX | 权限设置(me/team) |
| description | TEXT | - | NULL | - | - | 画布描述 |
| canvas_type | VARCHAR | 32 | NULL | - | INDEX | 画布类型 |
| dsl | JSON | - | NULL | {} | - | 画布DSL配置 |
| create_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | - | 创建时间 |
| update_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | - | 更新时间 |

#### 7.2 canvas_template - 画布模板表
存储系统提供的画布模板。

| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 索引 | 说明 |
|--------|------|------|----------|--------|------|------|
| id | VARCHAR | 32 | NOT NULL | - | PRIMARY KEY | 画布模板唯一标识 |
| avatar | TEXT | - | NULL | - | - | 模板头像(base64字符串) |
| title | VARCHAR | 255 | NULL | - | - | 模板标题 |
| description | TEXT | - | NULL | - | - | 模板描述 |
| canvas_type | VARCHAR | 32 | NULL | - | INDEX | 画布类型 |
| dsl | JSON | - | NULL | {} | - | 模板DSL配置 |
| create_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | - | 创建时间 |
| update_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | - | 更新时间 |

#### 7.3 user_canvas_version - 用户画布版本表
存储用户画布的版本历史。

| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 索引 | 说明 |
|--------|------|------|----------|--------|------|------|
| id | VARCHAR | 32 | NOT NULL | - | PRIMARY KEY | 画布版本唯一标识 |
| user_canvas_id | VARCHAR | 255 | NOT NULL | - | INDEX | 用户画布ID |
| title | VARCHAR | 255 | NULL | - | - | 版本标题 |
| description | TEXT | - | NULL | - | - | 版本描述 |
| dsl | JSON | - | NULL | {} | - | 版本DSL配置 |
| create_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | - | 创建时间 |
| update_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | - | 更新时间 |

### 8. MCP服务器管理模块

#### 8.1 mcp_server - MCP服务器表
存储MCP(Model Context Protocol)服务器配置信息。

| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 索引 | 说明 |
|--------|------|------|----------|--------|------|------|
| id | VARCHAR | 32 | NOT NULL | - | PRIMARY KEY | MCP服务器唯一标识 |
| name | VARCHAR | 255 | NOT NULL | - | - | MCP服务器名称 |
| tenant_id | VARCHAR | 32 | NOT NULL | - | INDEX | 租户ID |
| url | VARCHAR | 2048 | NOT NULL | - | - | MCP服务器URL |
| server_type | VARCHAR | 32 | NOT NULL | - | - | MCP服务器类型 |
| description | TEXT | - | NULL | - | - | MCP服务器描述 |
| variables | JSON | - | NULL | {} | - | MCP服务器变量配置 |
| headers | JSON | - | NULL | {} | - | MCP服务器请求头配置 |
| create_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | - | 创建时间 |
| update_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | - | 更新时间 |

### 9. 搜索管理模块

#### 9.1 search - 搜索配置表
存储搜索应用的配置信息。

| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 索引 | 说明 |
|--------|------|------|----------|--------|------|------|
| id | VARCHAR | 32 | NOT NULL | - | PRIMARY KEY | 搜索配置唯一标识 |
| avatar | TEXT | - | NULL | - | - | 搜索应用头像(base64字符串) |
| tenant_id | VARCHAR | 32 | NOT NULL | - | INDEX | 租户ID |
| name | VARCHAR | 128 | NOT NULL | - | INDEX | 搜索应用名称 |
| description | TEXT | - | NULL | - | - | 搜索应用描述 |
| created_by | VARCHAR | 32 | NOT NULL | - | INDEX | 创建者ID |
| search_config | JSON | - | NOT NULL | 默认搜索配置 | - | 搜索配置参数 |
| status | VARCHAR | 1 | NULL | 1 | INDEX | 搜索应用状态(0:废弃, 1:有效) |
| create_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | - | 创建时间 |
| update_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | - | 更新时间 |

## 数据库设计说明

### 设计原则
1. **模块化设计**: 按功能模块划分表结构，便于维护和扩展
2. **标准化字段**: 所有表都包含create_time和update_time字段用于审计
3. **状态管理**: 使用status字段进行软删除，保证数据完整性
4. **索引优化**: 在查询频繁的字段上建立索引，提高查询性能
5. **JSON存储**: 使用JSON字段存储复杂配置，提供灵活性

### 关键关系
- 用户通过user_tenant表与租户建立多对多关系
- 知识库属于特定租户，文档属于特定知识库
- 对话应用可以关联多个知识库进行检索
- API令牌与租户和对话应用关联，用于访问控制

### 扩展性考虑
- 使用VARCHAR(32)作为主键，支持UUID等长标识符
- JSON字段用于存储配置信息，便于功能扩展
- 预留了足够的字段长度，支持未来需求变化