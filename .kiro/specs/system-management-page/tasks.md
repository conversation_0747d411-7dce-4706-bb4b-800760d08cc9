# 实施计划

- [x] 1. 创建后端权限验证和API基础结构
  - 实现超级用户权限验证装饰器
  - 创建系统管理API蓝图和基础路由结构
  - 实现错误处理装饰器和异常类
  - _需求: 1.1, 1.2, 1.3, 1.4_

- [x] 2. 扩展用户服务层功能
  - 在UserService中添加get_users_for_admin方法实现用户列表查询
  - 实现update_user_info方法支持用户信息更新
  - 实现update_user_status方法支持用户状态管理
  - 添加用户搜索和筛选功能
  - _需求: 6.1, 6.2, 6.3, 6.4, 6.5_

- [x] 3. 实现用户管理API端点
  - 创建GET /api/v1/system/users端点获取用户列表
  - 创建PUT /api/v1/system/users/{user_id}端点更新用户信息
  - 创建PUT /api/v1/system/users/{user_id}/status端点更新用户状态
  - 实现分页、搜索和筛选参数处理
  - _需求: 6.1, 6.2, 6.3, 6.4, 6.5_

- [x] 4. 扩展知识库服务层功能
  - 在KnowledgebaseService中添加get_knowledgebases_for_admin方法
  - 实现get_knowledgebase_detail方法获取知识库详情
  - 实现update_knowledgebase_status方法支持知识库状态管理
  - 添加知识库搜索、筛选和创建者过滤功能
  - _需求: 7.1, 7.2, 7.3, 7.4, 7.5_

- [x] 5. 实现知识库管理API端点
  - 创建GET /api/v1/system/knowledgebases端点获取知识库列表
  - 创建GET /api/v1/system/knowledgebases/{kb_id}端点获取知识库详情
  - 创建PUT /api/v1/system/knowledgebases/{kb_id}/status端点更新知识库状态
  - 实现分页、搜索和筛选参数处理
  - _需求: 7.1, 7.2, 7.3, 7.4, 7.5_

- [x] 6. 创建前端项目结构和类型定义
  - 创建web/src/pages/system-management目录结构
  - 定义TypeScript接口和类型（User, Knowledgebase, API响应等）
  - 创建常量定义文件
  - 设置样式文件结构
  - _需求: 4.1, 4.2, 4.3_

- [x] 7. 实现权限检查Hook和包装器
  - 创建use-permission-check.ts Hook验证超级用户权限
  - 实现SuperuserAuth包装器组件
  - 添加权限验证逻辑和重定向处理
  - _需求: 1.1, 1.2, 1.4_

- [x] 8. 实现系统管理主页面组件
  - 创建SystemManagement主组件
  - 实现页面布局（左侧边栏 + 右侧内容区域）
  - 添加标签页切换状态管理
  - 实现加载状态和错误处理
  - _需求: 2.1, 2.2, 2.3, 2.4, 2.5, 5.1, 5.2_

- [x] 9. 实现左侧边栏导航组件
  - 创建Sidebar组件
  - 实现导航菜单项（用户管理、知识库管理）
  - 添加菜单项点击处理和高亮状态
  - 实现响应式布局适配
  - _需求: 2.1, 2.2, 2.3, 2.4, 2.5_

- [x] 10. 实现用户管理数据获取Hook
  - 创建use-user-management.ts Hook
  - 实现用户列表数据获取和缓存
  - 添加搜索、筛选和分页功能
  - 实现用户信息更新和状态切换功能
  - _需求: 6.1, 6.2, 6.3, 6.4, 6.5_

- [x] 11. 实现用户管理表格组件
  - 创建UserTable组件显示用户列表
  - 实现表格列定义（ID、昵称、邮箱、状态、超级用户标识）
  - 添加分页、搜索和筛选UI控件
  - 实现用户状态切换按钮和确认对话框
  - _需求: 6.1, 6.2, 6.3, 6.4, 6.5_

- [x] 12. 实现用户编辑模态框组件
  - 创建UserModal组件
  - 实现用户信息编辑表单（昵称、邮箱）
  - 添加表单验证和提交处理
  - 实现成功/失败反馈
  - _需求: 6.4_

- [x] 13. 实现用户管理主组件
  - 创建UserManagement主组件
  - 集成用户表格和编辑模态框
  - 实现组件间状态管理和数据流
  - 添加错误处理和加载状态
  - _需求: 6.1, 6.2, 6.3, 6.4, 6.5, 5.3, 5.4, 5.5_

- [x] 14. 实现知识库管理数据获取Hook
  - 创建use-knowledgebase-management.ts Hook
  - 实现知识库列表数据获取和缓存
  - 添加搜索、筛选和分页功能
  - 实现知识库状态切换功能
  - _需求: 7.1, 7.2, 7.3, 7.4, 7.5_

- [x] 15. 实现知识库管理表格组件
  - 创建KnowledgebaseTable组件显示知识库列表
  - 实现表格列定义（ID、名称、描述、创建者、状态、文档数量）
  - 添加分页、搜索和筛选UI控件
  - 实现知识库状态切换按钮和详情查看功能
  - _需求: 7.1, 7.2, 7.3, 7.4, 7.5_

- [x] 16. 实现知识库详情模态框组件
  - 创建KnowledgebaseModal组件
  - 实现知识库详细信息展示
  - 添加文档统计信息显示
  - 实现关闭和操作按钮
  - _需求: 7.4_

- [x] 17. 实现知识库管理主组件
  - 创建KnowledgebaseManagement主组件
  - 集成知识库表格和详情模态框
  - 实现组件间状态管理和数据流
  - 添加错误处理和加载状态
  - _需求: 7.1, 7.2, 7.3, 7.4, 7.5, 5.3, 5.4, 5.5_

- [x] 18. 配置前端路由和导航
  - 在现有路由配置中添加系统管理路由
  - 实现路由权限保护
  - 在主导航中添加系统管理标签（仅超级用户可见）
  - 配置子路由和默认重定向
  - _需求: 1.1, 1.2, 1.3, 4.3_

- [x] 19. 注册后端API路由
  - 在Flask应用中注册系统管理API蓝图
  - 配置API路由前缀和中间件
  - 确保与现有API结构的兼容性
  - _需求: 4.2, 4.4_

- [x] 20. 实现样式和响应式设计
  - 创建系统管理页面的Less样式文件
  - 实现响应式布局适配
  - 确保与现有设计系统的一致性
  - 添加加载动画和过渡效果
  - _需求: 4.5, 5.1_

- [x] 21. 添加错误处理和用户反馈
  - 实现全局错误处理Hook
  - 添加网络错误和权限错误的用户友好提示
  - 实现操作成功/失败的反馈消息
  - 添加确认对话框防止误操作
  - _需求: 5.1, 5.2, 5.3, 5.4, 5.5_

- [x] 22. 编写单元测试
  - 为用户管理和知识库管理Hook编写单元测试
  - 为主要组件编写React Testing Library测试
  - 为API端点编写后端单元测试
  - 为服务层方法编写测试用例
  - _需求: 4.1, 4.2_

- [x] 23. 集成测试和端到端测试
  - 编写系统管理页面的集成测试
  - 实现权限验证的端到端测试
  - 测试用户管理和知识库管理的完整工作流程
  - 验证错误处理和边界情况
  - _需求: 1.1, 1.2, 1.3, 1.4, 5.1, 5.2, 5.3, 5.4, 5.5_