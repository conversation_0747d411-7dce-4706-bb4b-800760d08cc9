# 设计文档

## 概述

批量关键词追加功能将为RAGFlow系统添加一个新的API端点，允许用户为多个文本块同时添加重要关键词。该功能将复用现有的chunk更新机制，确保数据一致性和系统稳定性。

## 架构

### 系统架构图

```mermaid
graph TD
    A[前端UI] --> B[批量关键词API]
    B --> C[权限验证]
    C --> D[参数验证]
    D --> E[获取现有Chunk数据]
    E --> F[关键词合并逻辑]
    F --> G[批量更新处理]
    G --> H[向量重新计算]
    H --> I[数据库更新]
    I --> J[返回结果]
```

### 数据流

1. 用户在前端选择多个chunk并输入新关键词
2. 前端发送批量更新请求到后端API
3. 后端验证用户权限和参数有效性
4. 系统获取每个chunk的现有important_kwd数据
5. 执行关键词合并和去重逻辑
6. 批量更新chunk数据和相关索引
7. 返回操作结果给前端

## 组件和接口

### API接口设计

#### 批量添加关键词接口

**端点:** `POST /api/v1/chunk/batch_append_keywords`

**请求参数:**
```json
{
  "doc_id": "string",           // 文档ID，用于权限验证
  "chunk_ids": ["string"],      // 要更新的chunk ID列表
  "keywords": ["string"],       // 要添加的关键词列表
  "preview": false              // 可选，是否仅预览不执行更新
}
```

**响应格式:**
```json
{
  "retcode": 0,
  "retmsg": "success",
  "data": {
    "success_count": 10,        // 成功处理的chunk数量
    "failed_count": 0,          // 失败的chunk数量
    "failed_chunks": [],        // 失败的chunk详情
    "preview_data": {           // 仅在preview=true时返回
      "chunk_id": {
        "original_keywords": ["keyword1"],
        "merged_keywords": ["keyword1", "keyword2"]
      }
    }
  }
}
```

### 核心组件

#### 1. 关键词合并器 (KeywordMerger)

**职责:** 处理关键词的合并和去重逻辑

**方法:**
- `merge_keywords(existing_keywords: List[str], new_keywords: List[str]) -> List[str]`
- `validate_keywords(keywords: List[str]) -> bool`

#### 2. 批量处理器 (BatchProcessor)

**职责:** 协调批量操作的执行流程

**方法:**
- `process_batch(chunk_ids: List[str], keywords: List[str], doc_id: str) -> BatchResult`
- `preview_batch(chunk_ids: List[str], keywords: List[str], doc_id: str) -> PreviewResult`

#### 3. Chunk数据访问器 (ChunkDataAccessor)

**职责:** 封装chunk数据的读取和更新操作

**方法:**
- `get_chunks_data(chunk_ids: List[str], tenant_id: str, kb_id: str) -> Dict`
- `update_chunk_keywords(chunk_id: str, keywords: List[str], tenant_id: str, kb_id: str) -> bool`

## 数据模型

### Chunk数据结构扩展

现有的chunk数据结构已包含所需字段：

```python
{
    "id": "chunk_id",
    "important_kwd": ["existing", "keywords"],  # 现有关键词列表
    "important_tks": "tokenized keywords",      # 分词后的关键词
    "content_with_weight": "chunk content",
    # ... 其他字段
}
```

### 批量操作结果模型

```python
@dataclass
class BatchResult:
    success_count: int
    failed_count: int
    failed_chunks: List[Dict[str, str]]  # {"chunk_id": "error_message"}
    
@dataclass
class PreviewResult:
    preview_data: Dict[str, Dict[str, List[str]]]  # chunk_id -> keyword comparison
```

## 错误处理

### 错误类型和处理策略

1. **权限错误**
   - 用户无权访问指定文档
   - 返回403错误，停止处理

2. **参数验证错误**
   - 无效的chunk_ids或keywords格式
   - 返回400错误，包含详细错误信息

3. **数据不存在错误**
   - 文档或chunk不存在
   - 跳过该chunk，继续处理其他chunk

4. **数据库更新错误**
   - 索引更新失败
   - 记录错误，继续处理其他chunk

5. **系统错误**
   - 向量计算失败等
   - 返回500错误，包含错误详情

### 错误恢复机制

- 采用"尽力而为"策略：单个chunk失败不影响其他chunk的处理
- 提供详细的错误报告，便于用户了解失败原因
- 支持重试机制，用户可以针对失败的chunk重新执行操作

## 测试策略

### 单元测试

1. **关键词合并逻辑测试**
   - 测试正常合并场景
   - 测试去重功能
   - 测试空列表和特殊字符处理

2. **参数验证测试**
   - 测试各种无效参数组合
   - 测试边界条件

3. **权限验证测试**
   - 测试有权限和无权限场景
   - 测试跨租户访问控制

### 集成测试

1. **端到端流程测试**
   - 完整的批量更新流程
   - 数据一致性验证

2. **错误场景测试**
   - 部分chunk失败的处理
   - 网络异常和超时处理

3. **性能测试**
   - 大批量数据处理性能
   - 并发请求处理能力

### 测试数据准备

- 创建测试用的文档和chunk数据
- 准备各种关键词组合的测试用例
- 模拟不同的错误场景

## 性能考虑

### 优化策略

1. **批量数据库操作**
   - 使用批量更新减少数据库交互次数
   - 优化索引更新操作

2. **内存使用优化**
   - 分批处理大量chunk，避免内存溢出
   - 及时释放不需要的数据

3. **并发处理**
   - 支持异步处理提高响应速度
   - 合理控制并发数量避免系统过载

### 限制和约束

- 单次批量操作的chunk数量限制（建议不超过1000个）
- 关键词数量和长度限制
- 请求频率限制防止滥用

## 前端UI设计

### 组件架构

```mermaid
graph TD
    A[ChunkToolBar] --> B[批量操作下拉菜单]
    B --> C[批量添加关键词选项]
    C --> D[BatchKeywordModal]
    D --> E[关键词输入组件]
    D --> F[预览组件]
    D --> G[进度反馈组件]
    
    H[ChunkList] --> I[Chunk选择状态]
    I --> D
    
    J[API Service] --> K[批量关键词API调用]
    K --> D
```

### 核心UI组件

#### 1. 批量操作菜单扩展

在现有的ChunkToolBar组件中，扩展批量操作下拉菜单：

**位置:** `chunk-toolbar/index.tsx`
**功能:** 在现有菜单项中添加"批量添加关键词"选项

**菜单项结构:**
```typescript
{
  key: 'batch-keywords',
  label: (
    <Space onClick={handleBatchKeywords}>
      <TagsOutlined />
      <b>{t('batchAddKeywords')}</b>
    </Space>
  ),
  disabled: selectedChunks.length === 0
}
```

#### 2. 批量关键词模态框 (BatchKeywordModal)

**文件路径:** `components/batch-keyword-modal/index.tsx`

**主要功能:**
- 显示选中的chunk数量
- 提供关键词输入界面
- 显示预览结果
- 执行批量操作并显示进度

**组件结构:**
```typescript
interface BatchKeywordModalProps {
  visible: boolean;
  onCancel: () => void;
  selectedChunkIds: string[];
  documentId: string;
  onSuccess: () => void;
}
```

#### 3. 关键词输入组件 (KeywordInput)

**功能特性:**
- 支持标签式输入（Tag Input）
- 支持回车和逗号分隔
- 自动去重提示
- 关键词验证

**技术实现:**
```typescript
// 使用Antd的Select组件的tags模式
<Select
  mode="tags"
  style={{ width: '100%' }}
  placeholder={t('enterKeywords')}
  value={keywords}
  onChange={setKeywords}
  tokenSeparators={[',', ' ']}
  maxTagCount="responsive"
/>
```

#### 4. 预览组件 (PreviewPanel)

**显示内容:**
- 每个chunk的原有关键词
- 合并后的关键词（高亮新增部分）
- 统计信息（总chunk数、新增关键词数）

**数据结构:**
```typescript
interface PreviewData {
  chunkId: string;
  chunkTitle: string; // chunk内容的前50个字符
  originalKeywords: string[];
  mergedKeywords: string[];
  newKeywords: string[]; // 用于高亮显示
}
```

#### 5. 进度反馈组件 (ProgressFeedback)

**功能:**
- 显示处理进度条
- 实时更新处理状态
- 显示成功/失败统计
- 错误详情展示

### 状态管理

#### 1. 选中状态管理

扩展现有的chunk选择逻辑：

```typescript
// 在chunk列表组件中添加
const [selectedChunkIds, setSelectedChunkIds] = useState<string[]>([]);

// 批量选择逻辑
const handleSelectAll = (checked: boolean) => {
  if (checked) {
    setSelectedChunkIds(chunks.map(chunk => chunk.chunk_id));
  } else {
    setSelectedChunkIds([]);
  }
};
```

#### 2. 模态框状态

```typescript
const [batchKeywordModalVisible, setBatchKeywordModalVisible] = useState(false);
const [keywords, setKeywords] = useState<string[]>([]);
const [previewData, setPreviewData] = useState<PreviewData[]>([]);
const [isProcessing, setIsProcessing] = useState(false);
```

### API集成

#### 1. 服务层扩展

在 `knowledge-service.ts` 中添加批量关键词API：

```typescript
const methods = {
  // ... 现有方法
  batchAppendKeywords: {
    url: '/api/v1/chunk/batch_append_keywords',
    method: 'post',
  },
  batchPreviewKeywords: {
    url: '/api/v1/chunk/batch_preview_keywords', 
    method: 'post',
  },
};
```

#### 2. Hook实现

创建专用的批量关键词Hook：

```typescript
// hooks/batch-keyword-hooks.ts
export const useBatchKeywords = () => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  
  const previewMutation = useMutation({
    mutationFn: async (params: BatchKeywordParams) => {
      const { data } = await kbService.batchPreviewKeywords({
        ...params,
        preview: true
      });
      return data;
    },
  });
  
  const executeMutation = useMutation({
    mutationFn: async (params: BatchKeywordParams) => {
      const { data } = await kbService.batchAppendKeywords(params);
      if (data.code === 0) {
        message.success(t('batchKeywordSuccess'));
        queryClient.invalidateQueries({ queryKey: ['fetchChunkList'] });
      }
      return data;
    },
  });
  
  return {
    preview: previewMutation.mutateAsync,
    execute: executeMutation.mutateAsync,
    isPreviewLoading: previewMutation.isPending,
    isExecuteLoading: executeMutation.isPending,
  };
};
```

### 用户交互流程

#### 1. 操作入口流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant TB as ChunkToolBar
    participant CL as ChunkList
    participant M as BatchKeywordModal
    
    U->>CL: 选择多个chunk
    CL->>TB: 更新选中状态
    U->>TB: 点击批量操作按钮
    TB->>U: 显示下拉菜单
    U->>TB: 点击"批量添加关键词"
    TB->>M: 打开模态框
    M->>U: 显示关键词输入界面
```

#### 2. 关键词处理流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant M as Modal
    participant API as 后端API
    participant DB as 数据库
    
    U->>M: 输入关键词
    M->>API: 调用预览接口
    API->>DB: 获取chunk数据
    DB->>API: 返回现有关键词
    API->>M: 返回预览数据
    M->>U: 显示预览结果
    U->>M: 确认执行
    M->>API: 调用批量更新接口
    API->>DB: 批量更新chunk
    DB->>API: 返回更新结果
    API->>M: 返回操作结果
    M->>U: 显示成功反馈
```

### 响应式设计

#### 移动端适配

- 模态框在小屏幕上全屏显示
- 关键词输入组件适配触摸操作
- 预览列表支持滚动和折叠

#### 无障碍支持

- 添加适当的ARIA标签
- 支持键盘导航
- 屏幕阅读器友好

### 国际化支持

需要添加的翻译键：

```typescript
// zh-CN
{
  "batchAddKeywords": "批量添加关键词",
  "enterKeywords": "请输入关键词，支持回车或逗号分隔",
  "keywordPreview": "关键词预览",
  "originalKeywords": "原有关键词",
  "mergedKeywords": "合并后关键词",
  "newKeywords": "新增关键词",
  "processProgress": "处理进度",
  "batchKeywordSuccess": "批量添加关键词成功",
  "selectedChunks": "已选择 {count} 个文本块"
}
```

## 安全考虑

### 访问控制

- 验证用户对文档的访问权限
- 确保用户只能操作自己有权限的chunk

### 数据验证

- 严格验证输入参数格式和内容
- 防止SQL注入和XSS攻击
- 限制关键词内容，防止恶意输入

### 审计日志

- 记录所有批量操作的详细日志
- 包含操作用户、时间、影响的数据等信息
- 便于问题追踪和安全审计