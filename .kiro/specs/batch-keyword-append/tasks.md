# 实现计划

- [x] 1. 创建核心工具类和数据模型
  - 实现关键词合并和验证的工具函数
  - 创建批量操作结果的数据类
  - 编写单元测试验证合并逻辑的正确性
  - _需求: 1.3, 1.4, 1.5_

- [x] 2. 实现批量关键词追加的核心逻辑
  - 创建获取多个chunk现有数据的函数
  - 实现关键词合并和去重的处理逻辑
  - 添加参数验证和错误处理机制
  - _需求: 1.1, 1.2, 1.3, 1.4, 1.5, 3.4_

- [x] 3. 创建批量更新API端点
  - 在chunk_app.py中添加batch_append_keywords路由
  - 实现请求参数验证和权限检查
  - 集成核心逻辑并处理批量更新流程
  - _需求: 1.1, 1.2, 3.1, 3.2_

- [x] 4. 实现预览功能
  - 添加预览模式的处理逻辑
  - 返回合并前后关键词对比数据
  - 确保预览模式不执行实际数据更新
  - _需求: 4.1, 4.2, 4.3, 4.4_

- [x] 5. 添加错误处理和日志记录
  - 实现详细的错误分类和处理
  - 添加操作日志记录功能
  - 确保部分失败不影响其他chunk处理
  - _需求: 2.2, 2.3, 3.3_

- [x] 6. 编写API集成测试
  - 创建测试用的文档和chunk数据
  - 测试正常批量更新流程
  - 测试各种错误场景的处理
  - _需求: 1.1, 1.2, 1.3, 1.4, 1.5, 2.1, 2.2, 2.3, 2.4_

- [x] 7. 添加性能优化和限制
  - 实现批量操作的数量限制
  - 优化数据库批量更新操作
  - 添加请求频率限制机制
  - _需求: 3.3_

- [x] 8. 完善文档和示例
  - 编写API使用文档和示例代码
  - 添加错误码说明和故障排除指南
  - 创建功能演示的测试脚本
  - _需求: 2.1, 2.4_

- [x] 9. 扩展ChunkToolBar批量操作菜单
  - 在现有批量操作下拉菜单中添加"批量添加关键词"选项
  - 实现菜单项的启用/禁用逻辑（基于选中chunk数量）
  - 添加相应的图标和国际化文本
  - _需求: 5.1, 5.2, 5.3_

- [x] 10. 创建批量关键词模态框组件
  - 创建BatchKeywordModal组件的基础结构
  - 实现模态框的打开/关闭逻辑
  - 显示选中chunk的数量信息
  - 集成到ChunkToolBar的批量操作流程中
  - _需求: 5.4, 6.1_

- [x] 11. 实现关键词输入组件
  - 使用Antd Select组件的tags模式实现关键词输入
  - 支持回车键和逗号分隔符添加关键词
  - 实现关键词去重和验证逻辑
  - 添加输入提示和错误状态显示
  - _需求: 6.1, 6.2, 6.3_

- [x] 12. 开发预览功能组件
  - 创建PreviewPanel组件显示关键词合并预览
  - 实现原有关键词和新增关键词的对比显示
  - 高亮显示新增的关键词
  - 添加统计信息展示（总chunk数、新增关键词数）
  - _需求: 4.1, 4.2, 6.4_

- [ ] 13. 集成批量关键词API服务
  - 在knowledge-service.ts中添加批量关键词API方法
  - 创建useBatchKeywords Hook封装API调用逻辑
  - 实现预览和执行两个API的调用
  - 添加错误处理和loading状态管理
  - _需求: 1.1, 1.2, 4.3, 4.4_

- [x] 14. 实现进度反馈和结果展示
  - 创建ProgressFeedback组件显示批量操作进度
  - 实现成功/失败统计信息的展示
  - 添加错误详情的显示和重试功能
  - 操作完成后自动刷新chunk列表
  - _需求: 2.1, 2.2, 2.3, 2.4, 7.1, 7.2, 7.3, 7.4_

- [x] 15. 完善chunk选择状态管理
  - 扩展现有的chunk选择逻辑支持批量关键词操作
  - 确保选中状态在批量操作过程中保持一致
  - 实现全选/取消全选功能的正确集成
  - 添加选中chunk数量的实时显示
  - _需求: 5.1, 5.2, 5.4_

- [x] 16. 添加国际化支持
  - 为所有新增的UI文本添加国际化键值
  - 实现中英文翻译文件的更新
  - 确保所有用户提示信息支持多语言
  - 测试不同语言环境下的UI显示效果
  - _需求: 6.1, 6.2, 7.1, 7.2_

- [x] 17. 实现响应式设计和无障碍支持
  - 确保批量关键词模态框在移动端正确显示
  - 添加适当的ARIA标签支持屏幕阅读器
  - 实现键盘导航支持
  - 测试不同屏幕尺寸下的用户体验
  - _需求: 6.1, 6.4_

- [x] 18. 编写前端组件单元测试
  - 为BatchKeywordModal组件编写单元测试
  - 测试关键词输入和验证逻辑
  - 测试预览功能的正确性
  - 测试API集成和错误处理
  - _需求: 1.3, 1.4, 2.2, 4.1, 4.2_

- [ ] 19. 进行端到端集成测试
  - 测试完整的批量关键词添加流程
  - 验证前后端数据同步的正确性
  - 测试各种边界情况和错误场景
  - 确保与现有chunk管理功能的兼容性
  - _需求: 1.1, 1.2, 1.3, 1.4, 1.5, 2.1, 2.2, 2.3, 2.4_