import { useFetchUserInfo } from '@/hooks/user-setting-hooks';
import { Avatar } from 'antd';
import React from 'react';
import { history } from 'umi';

import styles from '../../index.less';

const App: React.FC = () => {
  const { data: userInfo } = useFetchUserInfo();

  const toSetting = () => {
    history.push('/user-setting');
  };

  return (
    <Avatar
      size={32}
      onClick={toSetting}
      className={styles.clickAvailable}
      src={
        userInfo.avatar ??
        'data:image/png;base64,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'
      }
    />
  );
};

export default App;
