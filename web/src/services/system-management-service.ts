import request from '@/utils/request';
import api from '@/utils/api';
import {
    IUserListResponse,
    IUpdateUserRequest,
    IUpdateUserStatusRequest,
    IKnowledgebaseListResponse,
    IKnowledgebaseDetailResponse,
    IUpdateKnowledgebaseStatusRequest,
    IUserSearchParams,
    IKnowledgebaseSearchParams,
    ISystemManagementApiResponse,
} from '@/pages/system-management/types';

// 系统管理API服务类
class SystemManagementService {
    // 用户管理相关API

    /**
     * 获取用户列表
     */
    static async getUserList(params: IUserSearchParams): Promise<IUserListResponse> {
        const response = await request.get<ISystemManagementApiResponse<IUserListResponse>>(
            api.getSystemUsers,
            { params }
        );
        // response.data包含完整的API响应（包括code, data, message）
        const apiResponse = response.data;
        // 返回API响应中的data字段，其中包含users数组
        return apiResponse?.data || { users: [], total: 0, page: 1, size: 20, total_pages: 0 };
    }

    /**
     * 更新用户信息
     */
    static async updateUser(userId: string, data: IUpdateUserRequest): Promise<void> {
        const response = await request(api.updateSystemUser(userId), {
            method: 'PUT',
            data,
        });


        // 检查API响应结构，确保业务逻辑成功
        const apiResponse = response.data;
        // 确保正确检查业务逻辑错误
        if (apiResponse && typeof apiResponse.code === 'number' && apiResponse.code !== 0) {
            // 如果业务逻辑失败，抛出错误
            const error = new Error(apiResponse.message || '更新用户信息失败');
            (error as any).code = apiResponse.code;
            (error as any).apiResponse = apiResponse;
            throw error;
        }
    }

    /**
     * 更新用户状态
     */
    static async updateUserStatus(userId: string, data: IUpdateUserStatusRequest): Promise<void> {
        const response = await request(api.updateSystemUserStatus(userId), {
            method: 'PUT',
            data,
        });

        // 检查API响应结构，确保业务逻辑成功
        const apiResponse = response.data;
        if (apiResponse && apiResponse.code !== 0) {
            // 如果业务逻辑失败，抛出错误
            const error = new Error(apiResponse.message || '更新用户状态失败');
            (error as any).code = apiResponse.code;
            throw error;
        }
    }

    /**
     * 获取用户租户详情
     */
    static async getUserTenantDetails(userId: string, params: { page: number; size: number }): Promise<any> {
        const response = await request.get<ISystemManagementApiResponse<any>>(
            api.getUserTenantDetails(userId),
            { params }
        );

        const apiResponse = response.data;
        return apiResponse?.data || { tenants: [], total: 0, page: 1, size: 20, total_pages: 0 };
    }

    /**
     * 获取用户团队详情
     */
    static async getUserTeamDetails(userId: string, params: { page: number; size: number }): Promise<any> {
        const response = await request.get<ISystemManagementApiResponse<any>>(
            api.getUserTeamDetails(userId),
            { params }
        );

        const apiResponse = response.data;
        return apiResponse?.data || { teams: [], total: 0, page: 1, size: 20, total_pages: 0 };
    }

    // 知识库管理相关API

    /**
     * 获取知识库列表
     */
    static async getKnowledgebaseList(params: IKnowledgebaseSearchParams): Promise<IKnowledgebaseListResponse> {
        const response = await request.get<ISystemManagementApiResponse<IKnowledgebaseListResponse>>(
            api.getSystemKnowledgebases,
            { params }
        );

        const apiResponse = response.data;

        return apiResponse?.data || { knowledgebases: [], total: 0, page: 1, size: 20, total_pages: 0 };
    }

    /**
     * 更新知识库状态
     */
    static async updateKnowledgebaseStatus(kbId: string, data: IUpdateKnowledgebaseStatusRequest): Promise<void> {
        const response = await request(api.updateSystemKnowledgebaseStatus(kbId), {
            method: 'PUT',
            data,
        });

        // 检查API响应结构，确保业务逻辑成功
        const apiResponse = response.data;
        if (apiResponse && apiResponse.code !== 0) {
            // 如果业务逻辑失败，抛出错误
            const error = new Error(apiResponse.message || '更新知识库状态失败');
            (error as any).code = apiResponse.code;
            throw error;
        }
    }
}

export default SystemManagementService;