import kbService from '@/services/knowledge-service';
import { useMutation, useQueryClient } from '@tanstack/react-query';

interface BatchKeywordParams {
  doc_id: string;
  chunk_ids: string[];
  keywords: string[];
  preview: boolean;
}

interface BatchKeywordData {
  failed_chunks: Array<{
    chunk_id: string;
    error_message: string;
  }>;
  failed_count: number;
  success_count: number;
  total_chunks: number;
  preview_data?: Record<string, any>;
}

interface BatchKeywordResponse {
  code: number;
  data: BatchKeywordData;
  message: string;
}

export const useBatchKeywords = () => {
  const queryClient = useQueryClient();

  const previewMutation = useMutation<
    BatchKeywordResponse,
    Error,
    BatchKeywordParams
  >({
    mutationFn: async (params: BatchKeywordParams) => {
      const response = await kbService.batchAppendKeywords({
        ...params,
        preview: true,
      });
      // 返回 response.data 而不是整个 response
      return response.data;
    },
  });

  const executeMutation = useMutation<
    BatchKeywordResponse,
    Error,
    BatchKeywordParams
  >({
    mutationFn: async (params: BatchKeywordParams) => {
      const response = await kbService.batchAppendKeywords({
        ...params,
        preview: false,
      });
      // 返回 response.data 而不是整个 response
      return response.data;
    },
    onSuccess: (data) => {
      // 只有在成功时才刷新查询，失败时由组件处理
      if (data.code === 0) {
        // 刷新chunk列表
        queryClient.invalidateQueries({
          queryKey: ['fetchChunkList'],
        });

        // 刷新相关的查询
        queryClient.invalidateQueries({
          queryKey: ['chunk'],
        });
      }
    },
  });

  return {
    preview: previewMutation.mutateAsync,
    execute: executeMutation.mutateAsync,
    isPreviewLoading: previewMutation.isPending,
    isExecuteLoading: executeMutation.isPending,
  };
};
