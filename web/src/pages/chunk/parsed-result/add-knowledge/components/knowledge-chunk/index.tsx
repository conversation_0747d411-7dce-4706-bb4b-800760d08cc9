import {
  useBatchOperations,
  useChunkSelection,
} from '@/hooks/chunk-selection-hooks';
import {
  useFetchNextChunkList,
  useSwitchChunk,
} from '@/hooks/use-chunk-request';
import { useQueryClient } from '@tanstack/react-query';
import classNames from 'classnames';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import ChunkCard from './components/chunk-card';
import CreatingModal from './components/chunk-creating-modal';
import DocumentPreview from './components/document-preview';
import {
  useChangeChunkTextMode,
  useDeleteChunkByIds,
  useGetChunkHighlights,
  useHandleChunkCardClick,
  useUpdateChunk,
} from './hooks';

import BatchKeywordModal from './components/batch-keyword-modal';
import ChunkResultBar from './components/chunk-result-bar';
import CheckboxSets from './components/chunk-result-bar/checkbox-sets';
import DocumentHeader from './components/document-preview/document-header';

import { PageHeader } from '@/components/page-header';
import {
  RAGFlowPagination,
  RAGFlowPaginationType,
} from '@/components/ui/ragflow-pagination';
import { Spin } from '@/components/ui/spin';
import {
  QueryStringMap,
  useNavigatePage,
} from '@/hooks/logic-hooks/navigate-hooks';
import styles from './index.less';

const Chunk = () => {
  const [batchKeywordModalVisible, setBatchKeywordModalVisible] =
    useState(false);
  const { removeChunk } = useDeleteChunkByIds();
  const queryClient = useQueryClient();
  const {
    data: { documentInfo, data = [], total },
    pagination,
    loading,
    searchString,
    handleInputChange,
    available,
    handleSetAvailable,
  } = useFetchNextChunkList();
  const { handleChunkCardClick, selectedChunkId } = useHandleChunkCardClick();
  const isPdf = documentInfo?.type === 'pdf';

  // 使用增强的chunk选择状态管理
  const {
    selectedChunkIds,
    selectionStats,
    selectAllChunks,
    toggleChunkSelection,
    clearSelection,
    validateSelection,
    showSelectionWarning,
  } = useChunkSelection(data);

  // 批量操作状态管理
  const { batchOperationState, startBatchOperation, endBatchOperation } =
    useBatchOperations(selectedChunkIds);

  const { t } = useTranslation();
  const { changeChunkTextMode, textMode } = useChangeChunkTextMode();
  const { switchChunk } = useSwitchChunk();
  const [chunkList, setChunkList] = useState(data);
  const {
    chunkUpdatingLoading,
    onChunkUpdatingOk,
    showChunkUpdatingModal,
    hideChunkUpdatingModal,
    chunkId,
    chunkUpdatingVisible,
    documentId,
  } = useUpdateChunk();
  const { navigateToDataset, getQueryString } = useNavigatePage();
  useEffect(() => {
    setChunkList(data);
  }, [data]);
  const onPaginationChange: RAGFlowPaginationType['onChange'] = (
    page,
    size,
  ) => {
    clearSelection();
    pagination.onChange?.(page, size);
  };

  const handleBatchKeywords = useCallback(() => {
    if (validateSelection(1)) {
      if (startBatchOperation('batch-keywords')) {
        setBatchKeywordModalVisible(true);
      }
    }
  }, [validateSelection, startBatchOperation]);

  const handleBatchKeywordModalCancel = useCallback(() => {
    setBatchKeywordModalVisible(false);
    endBatchOperation();
  }, [endBatchOperation]);

  const handleBatchKeywordSuccess = useCallback(() => {
    setBatchKeywordModalVisible(false);
    clearSelection();
    endBatchOperation();
    // 刷新chunk列表通过React Query自动处理
    queryClient.invalidateQueries({ queryKey: ['fetchChunkList'] });
  }, [clearSelection, endBatchOperation, queryClient]);

  const handleRemoveChunk = useCallback(async () => {
    if (validateSelection(1)) {
      if (startBatchOperation('remove')) {
        try {
          const resCode: number = await removeChunk(
            selectedChunkIds,
            documentId,
          );
          if (resCode === 0) {
            clearSelection();
          }
        } finally {
          endBatchOperation();
        }
      }
    }
  }, [
    validateSelection,
    startBatchOperation,
    selectedChunkIds,
    documentId,
    removeChunk,
    clearSelection,
    endBatchOperation,
  ]);

  const handleSwitchChunk = useCallback(
    async (available?: number, chunkIds?: string[]) => {
      let ids = chunkIds;
      if (!chunkIds) {
        ids = selectedChunkIds;
        if (!validateSelection(1)) {
          return;
        }
      }

      if (startBatchOperation(`switch-${available}`)) {
        try {
          const resCode: number = await switchChunk({
            chunk_ids: ids,
            available_int: available,
            doc_id: documentId,
          });
          if (ids?.length && resCode === 0) {
            chunkList.forEach((x: any) => {
              if (ids!.indexOf(x['chunk_id']) > -1) {
                x['available_int'] = available;
              }
            });
            setChunkList(chunkList);
          }
        } finally {
          endBatchOperation();
        }
      }
    },
    [
      switchChunk,
      documentId,
      selectedChunkIds,
      validateSelection,
      startBatchOperation,
      endBatchOperation,
      chunkList,
    ],
  );

  const { highlights, setWidthAndHeight } =
    useGetChunkHighlights(selectedChunkId);

  const fileType = useMemo(() => {
    switch (documentInfo?.type) {
      case 'doc':
        return documentInfo?.name.split('.').pop() || 'doc';
      case 'visual':
      case 'docx':
      case 'txt':
      case 'md':
      case 'pdf':
        return documentInfo?.type;
    }
    return 'unknown';
  }, [documentInfo]);

  return (
    <>
      <PageHeader
        title="Back"
        back={navigateToDataset(getQueryString(QueryStringMap.id) as string)}
      ></PageHeader>
      <div className={styles.chunkPage}>
        <div className="flex flex-1 gap-8">
          <div className="w-2/5">
            <div className="h-[100px] flex flex-col justify-end pb-[5px]">
              <DocumentHeader {...documentInfo} />
            </div>
            <section className={styles.documentPreview}>
              <DocumentPreview
                className={styles.documentPreview}
                fileType={fileType}
                highlights={highlights}
                setWidthAndHeight={setWidthAndHeight}
              ></DocumentPreview>
            </section>
          </div>
          <div
            className={classNames(
              { [styles.pagePdfWrapper]: isPdf },
              'flex flex-col w-3/5',
            )}
          >
            <Spin spinning={loading} className={styles.spin} size="large">
              <div className="h-[100px] flex flex-col justify-end pb-[5px]">
                <div>
                  <h2 className="text-[24px]">{t('chunk.chunkResult')}</h2>
                  <div className="text-[14px] text-[#979AAB]">
                    {t('chunk.chunkResultTip')}
                  </div>
                </div>
              </div>
              <div className=" rounded-[16px] bg-[#FFF]/10 pl-[20px] pb-[20px] pt-[20px] box-border	mb-2">
                <ChunkResultBar
                  handleInputChange={handleInputChange}
                  searchString={searchString}
                  changeChunkTextMode={changeChunkTextMode}
                  createChunk={showChunkUpdatingModal}
                  available={available}
                  selectAllChunk={selectAllChunk}
                  handleSetAvailable={handleSetAvailable}
                />
                <div className="pt-[5px] pb-[5px]">
                  <CheckboxSets
                    selectAllChunk={selectAllChunks}
                    switchChunk={handleSwitchChunk}
                    removeChunk={handleRemoveChunk}
                    checked={selectionStats.isAllSelected}
                    selectedChunkIds={selectedChunkIds}
                    onBatchKeywords={handleBatchKeywords}
                  />
                </div>
                <div className={styles.pageContent}>
                  <div
                    className={classNames(
                      styles.chunkContainer,
                      {
                        [styles.chunkOtherContainer]: !isPdf,
                      },
                      'flex flex-col gap-4',
                    )}
                  >
                    {chunkList.map((item) => (
                      <ChunkCard
                        item={item}
                        key={item.chunk_id}
                        editChunk={showChunkUpdatingModal}
                        checked={selectedChunkIds.some(
                          (x) => x === item.chunk_id,
                        )}
                        handleCheckboxClick={toggleChunkSelection}
                        switchChunk={handleSwitchChunk}
                        clickChunkCard={handleChunkCardClick}
                        selected={item.chunk_id === selectedChunkId}
                        textMode={textMode}
                      ></ChunkCard>
                    ))}
                  </div>
                </div>
                <div className={styles.pageFooter}>
                  <RAGFlowPagination
                    pageSize={pagination.pageSize}
                    current={pagination.current}
                    total={total}
                    onChange={(page, pageSize) => {
                      onPaginationChange(page, pageSize);
                    }}
                  ></RAGFlowPagination>
                </div>
              </div>
            </Spin>
          </div>
        </div>
      </div>
      {chunkUpdatingVisible && (
        <CreatingModal
          doc_id={documentId}
          chunkId={chunkId}
          hideModal={hideChunkUpdatingModal}
          visible={chunkUpdatingVisible}
          loading={chunkUpdatingLoading}
          onOk={onChunkUpdatingOk}
          parserId={documentInfo.parser_id}
        />
      )}
      {batchKeywordModalVisible && (
        <BatchKeywordModal
          visible={batchKeywordModalVisible}
          onCancel={handleBatchKeywordModalCancel}
          selectedChunkIds={selectedChunkIds}
          documentId={documentId}
          onSuccess={handleBatchKeywordSuccess}
        />
      )}
    </>
  );
};

export default Chunk;
