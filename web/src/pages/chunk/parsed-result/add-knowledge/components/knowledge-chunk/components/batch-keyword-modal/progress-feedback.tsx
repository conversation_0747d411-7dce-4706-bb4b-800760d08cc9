import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  LoadingOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  Col,
  List,
  Progress,
  Row,
  Space,
  Statistic,
  Tag,
  Typography,
} from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';

const { Text, Title } = Typography;

// 进度反馈数据接口
export interface ProgressFeedbackData {
  status: 'processing' | 'success' | 'partial' | 'error';
  totalCount: number;
  processedCount: number;
  successCount: number;
  failedCount: number;
  failedChunks: Array<{
    chunk_id: string;
    error_message: string;
  }>;
  startTime: number;
  endTime?: number;
}

interface ProgressFeedbackProps {
  data: ProgressFeedbackData;
  onRetry?: (failedChunkIds: string[]) => void;
  onClose?: () => void;
  showRetry?: boolean;
}

const ProgressFeedback: React.FC<ProgressFeedbackProps> = ({
  data,
  onRetry,
  onClose,
  showRetry = false,
}) => {
  const { t } = useTranslation();

  // 计算进度百分比
  const progressPercent =
    data.totalCount > 0
      ? Math.round((data.processedCount / data.totalCount) * 100)
      : 0;

  // 计算耗时
  const duration = data.endTime
    ? Math.round((data.endTime - data.startTime) / 1000)
    : Math.round((Date.now() - data.startTime) / 1000);

  // 获取状态图标和颜色
  const getStatusConfig = () => {
    switch (data.status) {
      case 'processing':
        return {
          icon: <LoadingOutlined spin />,
          color: '#1890ff',
          title: '正在处理...',
          description: '批量关键词添加正在进行中，请稍候',
        };
      case 'success':
        return {
          icon: <CheckCircleOutlined />,
          color: '#52c41a',
          title: '处理完成',
          description: '所有文本块都已成功添加关键词',
        };
      case 'partial':
        return {
          icon: <ExclamationCircleOutlined />,
          color: '#faad14',
          title: '部分成功',
          description: '部分文本块处理成功，部分失败',
        };
      case 'error':
        return {
          icon: <CloseCircleOutlined />,
          color: '#ff4d4f',
          title: '处理失败',
          description: '批量操作失败，请查看错误详情',
        };
      default:
        return {
          icon: <LoadingOutlined />,
          color: '#1890ff',
          title: '处理中',
          description: '正在处理...',
        };
    }
  };

  const statusConfig = getStatusConfig();

  // 处理重试
  const handleRetry = () => {
    if (onRetry && data.failedChunks.length > 0) {
      const failedChunkIds = data.failedChunks.map((chunk) => chunk.chunk_id);
      onRetry(failedChunkIds);
    }
  };

  return (
    <div style={{ width: '100%' }}>
      {/* 状态概览 */}
      <Card
        title={
          <Space>
            <span style={{ color: statusConfig.color }}>
              {statusConfig.icon}
            </span>
            <Title level={5} style={{ margin: 0, color: statusConfig.color }}>
              {statusConfig.title}
            </Title>
          </Space>
        }
        style={{ marginBottom: 16 }}
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          <Text type="secondary">{statusConfig.description}</Text>

          {/* 进度条 */}
          <Progress
            percent={progressPercent}
            status={
              data.status === 'error'
                ? 'exception'
                : data.status === 'success'
                  ? 'success'
                  : data.status === 'partial'
                    ? 'active'
                    : 'active'
            }
            strokeColor={statusConfig.color}
            format={(percent) =>
              `${data.processedCount}/${data.totalCount} (${percent}%)`
            }
          />

          {/* 统计信息 */}
          <Row gutter={16} style={{ marginTop: 16 }}>
            <Col span={6}>
              <Statistic
                title="总数量"
                value={data.totalCount}
                valueStyle={{ fontSize: '16px' }}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="成功"
                value={data.successCount}
                valueStyle={{ fontSize: '16px', color: '#52c41a' }}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="失败"
                value={data.failedCount}
                valueStyle={{ fontSize: '16px', color: '#ff4d4f' }}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="耗时"
                value={duration}
                suffix="秒"
                valueStyle={{ fontSize: '16px' }}
              />
            </Col>
          </Row>
        </Space>
      </Card>

      {/* 成功率指示 */}
      {data.status !== 'processing' && (
        <Card style={{ marginBottom: 16 }}>
          <Space direction="vertical" style={{ width: '100%' }}>
            <Text strong>成功率</Text>
            <Progress
              type="circle"
              percent={
                data.totalCount > 0
                  ? Math.round((data.successCount / data.totalCount) * 100)
                  : 0
              }
              width={80}
              strokeColor={
                data.successCount === data.totalCount
                  ? '#52c41a'
                  : data.successCount > 0
                    ? '#faad14'
                    : '#ff4d4f'
              }
            />
          </Space>
        </Card>
      )}

      {/* 错误详情 */}
      {data.failedChunks.length > 0 && (
        <Card
          title={
            <Space>
              <CloseCircleOutlined style={{ color: '#ff4d4f' }} />
              <Text strong>错误详情 ({data.failedChunks.length})</Text>
            </Space>
          }
          style={{ marginBottom: 16 }}
        >
          <List
            size="small"
            dataSource={data.failedChunks}
            renderItem={(chunk, index) => (
              <List.Item>
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Space>
                    <Tag color="red">#{index + 1}</Tag>
                    <Text strong>Chunk ID: {chunk.chunk_id}</Text>
                  </Space>
                  <Text type="danger" style={{ fontSize: '12px' }}>
                    错误: {chunk.error_message}
                  </Text>
                </Space>
              </List.Item>
            )}
            style={{ maxHeight: 200, overflowY: 'auto' }}
          />

          {/* 重试按钮 */}
          {showRetry && data.status !== 'processing' && (
            <div style={{ marginTop: 16, textAlign: 'center' }}>
              <Button
                type="primary"
                danger
                icon={<ReloadOutlined />}
                onClick={handleRetry}
                disabled={data.failedChunks.length === 0}
              >
                重试失败的文本块 ({data.failedChunks.length})
              </Button>
            </div>
          )}
        </Card>
      )}

      {/* 操作建议 */}
      {data.status === 'success' && (
        <Alert
          message="操作成功完成"
          description="所有关键词已成功添加到选中的文本块中。文本块列表将自动刷新以显示最新内容。"
          type="success"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      {data.status === 'partial' && (
        <Alert
          message="部分操作成功"
          description="部分文本块已成功添加关键词，但有一些失败了。您可以查看错误详情并重试失败的文本块。"
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      {data.status === 'error' && (
        <Alert
          message="操作失败"
          description="批量关键词添加操作失败。请检查错误详情，确认网络连接和权限设置后重试。"
          type="error"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      {/* 操作按钮 */}
      {data.status !== 'processing' && (
        <div style={{ textAlign: 'center', marginTop: 16 }}>
          <Space>
            {showRetry && data.failedChunks.length > 0 && (
              <Button icon={<ReloadOutlined />} onClick={handleRetry}>
                重试失败项
              </Button>
            )}
            <Button type="primary" onClick={onClose}>
              {data.status === 'success' ? '完成' : '关闭'}
            </Button>
          </Space>
        </div>
      )}

      {/* 处理中的提示 */}
      {data.status === 'processing' && (
        <div style={{ textAlign: 'center', marginTop: 16 }}>
          <Space direction="vertical">
            <LoadingOutlined style={{ fontSize: 24, color: '#1890ff' }} />
            <Text type="secondary">
              正在处理第 {data.processedCount + 1} / {data.totalCount}{' '}
              个文本块...
            </Text>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              请不要关闭此窗口
            </Text>
          </Space>
        </div>
      )}
    </div>
  );
};

export default ProgressFeedback;
