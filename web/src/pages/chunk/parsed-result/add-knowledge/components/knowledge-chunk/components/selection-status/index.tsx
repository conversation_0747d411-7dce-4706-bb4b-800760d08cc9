import { CheckCircleOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { Badge, Progress, Space, Tooltip, Typography } from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';

const { Text } = Typography;

interface SelectionStatusProps {
  totalCount: number;
  selectedCount: number;
  isAllSelected: boolean;
  isPartialSelected: boolean;
  isNoneSelected: boolean;
  selectionPercentage: number;
  compact?: boolean;
}

const SelectionStatus: React.FC<SelectionStatusProps> = ({
  totalCount,
  selectedCount,
  isAllSelected,
  isPartialSelected,
  isNoneSelected,
  selectionPercentage,
  compact = false,
}) => {
  const { t } = useTranslation();

  if (compact) {
    return (
      <Space size="small">
        <Badge
          count={selectedCount}
          showZero
          style={{
            backgroundColor: selectedCount > 0 ? '#52c41a' : '#d9d9d9',
            color: selectedCount > 0 ? '#fff' : '#666',
          }}
        />
        <Text type="secondary" style={{ fontSize: '12px' }}>
          / {totalCount}
        </Text>
      </Space>
    );
  }

  const getStatusIcon = () => {
    if (isAllSelected) {
      return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
    } else if (isPartialSelected) {
      return <InfoCircleOutlined style={{ color: '#1890ff' }} />;
    }
    return null;
  };

  const getStatusText = () => {
    if (isNoneSelected) {
      return t('chunk.selection.noneSelected');
    } else if (isAllSelected) {
      return t('chunk.selection.allSelected');
    } else {
      return t('chunk.selection.partialSelected', {
        selected: selectedCount,
        total: totalCount,
      });
    }
  };

  const getStatusColor = () => {
    if (isAllSelected) return '#52c41a';
    if (isPartialSelected) return '#1890ff';
    return '#d9d9d9';
  };

  return (
    <div style={{ padding: '8px 0' }}>
      <Space direction="vertical" style={{ width: '100%' }}>
        <Space align="center">
          {getStatusIcon()}
          <Text strong style={{ color: getStatusColor() }}>
            {getStatusText()}
          </Text>
          <Tooltip
            title={`选择进度: ${selectedCount}/${totalCount} (${selectionPercentage}%)`}
          >
            <InfoCircleOutlined style={{ color: '#999', fontSize: '12px' }} />
          </Tooltip>
        </Space>

        <Progress
          percent={selectionPercentage}
          size="small"
          strokeColor={getStatusColor()}
          showInfo={false}
          style={{ marginBottom: 4 }}
        />

        <Space split={<span style={{ color: '#d9d9d9' }}>|</span>}>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            已选择:{' '}
            <Text strong style={{ color: '#52c41a' }}>
              {selectedCount}
            </Text>
          </Text>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            未选择: <Text strong>{totalCount - selectedCount}</Text>
          </Text>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            总计: <Text strong>{totalCount}</Text>
          </Text>
        </Space>
      </Space>
    </div>
  );
};

export default SelectionStatus;
