// 批量关键词模态框响应式样式

.batch-keyword-modal {
  // 基础样式
  .ant-modal-content {
    border-radius: 8px;
    overflow: hidden;
  }

  .ant-modal-header {
    padding: 16px 24px;
    border-bottom: 1px solid #f0f0f0;
  }

  .ant-modal-body {
    padding: 24px;
  }

  .ant-modal-footer {
    padding: 12px 24px;
    border-top: 1px solid #f0f0f0;
  }

  // 桌面端样式 (>= 1024px)
  @media (min-width: 1024px) {
    .ant-modal {
      max-width: 800px;
      width: 90vw;
    }

    .keyword-input-section {
      padding: 16px 0;
    }

    .preview-section {
      max-height: 500px;
      overflow-y: auto;
    }

    .progress-section {
      max-height: 500px;
      overflow-y: auto;
    }
  }

  // 平板端样式 (768px - 1023px)
  @media (min-width: 768px) and (max-width: 1023px) {
    .ant-modal {
      max-width: 700px;
      width: 95vw;
      margin: 20px auto;
    }

    .ant-modal-body {
      padding: 20px;
    }

    .keyword-input-section {
      padding: 12px 0;
    }

    .preview-section {
      max-height: 400px;
      overflow-y: auto;
    }

    .progress-section {
      max-height: 400px;
      overflow-y: auto;
    }

    // 调整统计卡片布局
    .statistics-row {
      .ant-col {
        margin-bottom: 12px;
      }
    }
  }

  // 移动端样式 (< 768px)
  @media (max-width: 767px) {
    .ant-modal {
      max-width: none;
      width: 100vw;
      height: 100vh;
      margin: 0;
      top: 0;
      padding-bottom: 0;
    }

    .ant-modal-content {
      height: 100vh;
      border-radius: 0;
      display: flex;
      flex-direction: column;
    }

    .ant-modal-header {
      padding: 12px 16px;
      flex-shrink: 0;
    }

    .ant-modal-body {
      padding: 16px;
      flex: 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;
    }

    .ant-modal-footer {
      padding: 12px 16px;
      flex-shrink: 0;
    }

    // 标签页样式调整
    .ant-tabs {
      height: 100%;
      display: flex;
      flex-direction: column;

      .ant-tabs-nav {
        flex-shrink: 0;
        margin-bottom: 8px;
      }

      .ant-tabs-content-holder {
        flex: 1;
        overflow: hidden;
      }

      .ant-tabs-tabpane {
        height: 100%;
        overflow-y: auto;
      }
    }

    // 关键词输入区域
    .keyword-input-section {
      padding: 8px 0;
    }

    // 预览区域
    .preview-section {
      height: 100%;
      overflow-y: auto;
      -webkit-overflow-scrolling: touch;
    }

    // 进度区域
    .progress-section {
      height: 100%;
      overflow-y: auto;
      -webkit-overflow-scrolling: touch;
    }

    // 统计信息卡片移动端布局
    .statistics-row {
      .ant-col {
        width: 50% !important;
        margin-bottom: 8px;
      }
    }

    // 预览卡片移动端样式
    .preview-card {
      margin-bottom: 12px;

      .ant-card-head {
        padding: 8px 12px;
        min-height: auto;
      }

      .ant-card-body {
        padding: 12px;
      }
    }

    // 关键词标签移动端样式
    .keyword-tags {
      .ant-tag {
        margin: 2px;
        font-size: 11px;
        padding: 2px 6px;
        line-height: 1.2;
      }
    }

    // 按钮组移动端样式
    .ant-modal-footer {
      .ant-btn {
        flex: 1;
        margin: 0 4px;

        &:first-child {
          margin-left: 0;
        }

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }

  // 超小屏幕样式 (< 480px)
  @media (max-width: 479px) {
    .ant-modal-header {
      padding: 8px 12px;

      .ant-modal-title {
        font-size: 16px;
      }
    }

    .ant-modal-body {
      padding: 12px;
    }

    .ant-modal-footer {
      padding: 8px 12px;

      .ant-btn {
        font-size: 14px;
        height: 36px;
      }
    }

    // 统计信息超小屏幕布局
    .statistics-row {
      .ant-col {
        width: 100% !important;
        margin-bottom: 8px;
      }
    }

    // 关键词输入组件
    .ant-select {
      .ant-select-selector {
        min-height: 40px;
        padding: 4px 8px;
      }
    }
  }
}

// 无障碍支持样式
.batch-keyword-modal {
  // 焦点样式
  .focusable-element:focus {
    outline: 2px solid #1890ff;
    outline-offset: 2px;
  }

  // 高对比度模式支持
  @media (prefers-contrast: high) {
    .ant-modal-content {
      border: 2px solid #000;
    }

    .keyword-tag {
      border: 1px solid #000;
    }

    .ant-btn {
      border: 2px solid #000;
    }
  }

  // 减少动画模式支持
  @media (prefers-reduced-motion: reduce) {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }

  // 深色模式支持
  @media (prefers-color-scheme: dark) {
    .ant-modal-content {
      background-color: #1f1f1f;
      color: #ffffff;
    }

    .ant-modal-header {
      background-color: #1f1f1f;
      border-bottom-color: #434343;
    }

    .ant-modal-footer {
      background-color: #1f1f1f;
      border-top-color: #434343;
    }
  }
}

// 打印样式
@media print {
  .batch-keyword-modal {
    .ant-modal {
      position: static !important;
      width: 100% !important;
      height: auto !important;
      margin: 0 !important;
      box-shadow: none !important;
    }

    .ant-modal-mask {
      display: none !important;
    }

    .ant-modal-footer {
      display: none !important;
    }

    .preview-section {
      max-height: none !important;
      overflow: visible !important;
    }
  }
}
