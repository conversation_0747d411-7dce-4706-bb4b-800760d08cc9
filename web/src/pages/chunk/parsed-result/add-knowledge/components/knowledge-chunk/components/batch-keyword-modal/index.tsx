import { useTranslate } from '@/hooks/common-hooks';
import { TagsOutlined } from '@ant-design/icons';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  Divider,
  Modal,
  Select,
  Space,
  Tag,
  Typography,
  message,
} from 'antd';
import { useCallback, useEffect, useState } from 'react';
import { useBatchKeywords } from './hooks/useBatchKeywords';

const { Text, Title } = Typography;

interface BatchKeywordModalProps {
  visible: boolean;
  onCancel: () => void;
  selectedChunkIds: string[];
  documentId: string;
  onSuccess: () => void;
}

interface PreviewData {
  [chunkId: string]: {
    original_keywords: string[];
    merged_keywords: string[];
  };
}

const BatchKeywordModal: React.FC<BatchKeywordModalProps> = ({
  visible,
  onCancel,
  selectedChunkIds,
  documentId,
  onSuccess,
}) => {
  const { t } = useTranslate('chunk');
  const [keywords, setKeywords] = useState<string[]>([]);
  const [previewData, setPreviewData] = useState<PreviewData>({});
  const [showPreview, setShowPreview] = useState(false);
  const [isExecuting, setIsExecuting] = useState(false);

  const { preview, execute, isPreviewLoading, isExecuteLoading } =
    useBatchKeywords();

  // 重置状态
  const resetState = useCallback(() => {
    setKeywords([]);
    setPreviewData({});
    setShowPreview(false);
    setIsExecuting(false);
  }, []);

  // 当模态框关闭时重置状态
  useEffect(() => {
    if (!visible) {
      resetState();
    }
  }, [visible, resetState]);

  // 处理关键词输入
  const handleKeywordsChange = useCallback((value: string[]) => {
    // 去重并过滤空值
    const uniqueKeywords = Array.from(new Set(value.filter((k) => k.trim())));
    setKeywords(uniqueKeywords);
    setShowPreview(false); // 输入变化时隐藏预览
  }, []);

  // 预览功能
  const handlePreview = useCallback(async () => {
    if (keywords.length === 0) {
      message.warning(t('pleaseEnterKeywords'));
      return;
    }

    try {
      const result = await preview({
        doc_id: documentId,
        chunk_ids: selectedChunkIds,
        keywords,
        preview: true,
      });

      if (result.code === 0) {
        setPreviewData(result.data.preview_data || {});
        setShowPreview(true);
      } else {
        message.error(result.message || t('previewFailed'));
      }
    } catch (error) {
      console.error('Preview error:', error);
      message.error(t('previewFailed'));
    }
  }, [keywords, documentId, selectedChunkIds, preview, t]);

  // 执行批量添加
  const handleExecute = useCallback(async () => {
    if (keywords.length === 0) {
      message.warning(t('pleaseEnterKeywords'));
      return;
    }

    setIsExecuting(true);
    try {
      const result = await execute({
        doc_id: documentId,
        chunk_ids: selectedChunkIds,
        keywords,
        preview: false,
      });

      if (result.code === 0) {
        const { success_count, failed_count } = result.data;
        message.success(
          t('batchKeywordSuccess', {
            success: success_count,
            failed: failed_count,
          }),
        );
        onSuccess();
      } else {
        message.error(result.message || t('batchKeywordFailed'));
      }
    } catch (error) {
      console.error('Execute error:', error);
      message.error(t('batchKeywordFailed'));
    } finally {
      setIsExecuting(false);
    }
  }, [keywords, documentId, selectedChunkIds, execute, onSuccess, t]);

  // 渲染预览内容
  const renderPreview = () => {
    if (!showPreview || Object.keys(previewData).length === 0) {
      return null;
    }

    return (
      <div style={{ marginTop: 16 }}>
        <Divider orientation="left">{t('keywordPreview')}</Divider>
        <div style={{ maxHeight: 300, overflowY: 'auto' }}>
          {Object.entries(previewData).map(([chunkId, data]) => {
            const newKeywords = data.merged_keywords.filter(
              (k) => !data.original_keywords.includes(k),
            );

            return (
              <Card key={chunkId} size="small" style={{ marginBottom: 8 }}>
                <div>
                  <Text strong>{t('chunkId')}: </Text>
                  <Text code>{chunkId.slice(-8)}</Text>
                </div>
                <div style={{ marginTop: 8 }}>
                  <Text>{t('originalKeywords')}: </Text>
                  {data.original_keywords.map((keyword) => (
                    <Tag key={keyword} color="default">
                      {keyword}
                    </Tag>
                  ))}
                </div>
                <div style={{ marginTop: 8 }}>
                  <Text>{t('newKeywords')}: </Text>
                  {newKeywords.map((keyword) => (
                    <Tag key={keyword} color="green">
                      {keyword}
                    </Tag>
                  ))}
                </div>
              </Card>
            );
          })}
        </div>
      </div>
    );
  };

  return (
    <Modal
      title={
        <Space>
          <TagsOutlined />
          {t('batchAddKeywords')}
        </Space>
      }
      open={visible}
      onCancel={onCancel}
      width={600}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          {t('cancel')}
        </Button>,
        <Button
          key="preview"
          onClick={handlePreview}
          loading={isPreviewLoading}
          disabled={keywords.length === 0}
        >
          {t('preview')}
        </Button>,
        <Button
          key="execute"
          type="primary"
          onClick={handleExecute}
          loading={isExecuteLoading || isExecuting}
          disabled={keywords.length === 0}
        >
          {t('execute')}
        </Button>,
      ]}
    >
      <div>
        <Alert
          message={t('selectedChunks', { count: selectedChunkIds.length })}
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />

        <div style={{ marginBottom: 16 }}>
          <Text strong>{t('enterKeywords')}</Text>
          <Select
            mode="tags"
            style={{ width: '100%', marginTop: 8 }}
            placeholder={t('keywordInputPlaceholder')}
            value={keywords}
            onChange={handleKeywordsChange}
            tokenSeparators={[',', ' ']}
            maxTagCount="responsive"
            size="large"
          />
          <Text type="secondary" style={{ fontSize: 12 }}>
            {t('keywordInputTip')}
          </Text>
        </div>

        {renderPreview()}
      </div>
    </Modal>
  );
};

export default BatchKeywordModal;
