import {
  EyeOutlined,
  InfoCircleOutlined,
  TagsOutlined,
} from '@ant-design/icons';
import {
  Card,
  Col,
  Collapse,
  Divider,
  Empty,
  Progress,
  Row,
  Space,
  Statistic,
  Tag,
  Typography,
} from 'antd';
import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { calculateStatistics } from './keyword-utils';

const { Text, Title } = Typography;
const { Panel } = Collapse;

// 预览数据接口
export interface PreviewData {
  chunkId: string;
  chunkTitle: string; // chunk内容的前50个字符
  originalKeywords: string[];
  mergedKeywords: string[];
  newKeywords: string[]; // 用于高亮显示
}

interface PreviewPanelProps {
  previewData: PreviewData[];
  loading?: boolean;
  newKeywords: string[]; // 用户输入的新关键词
}

const PreviewPanel: React.FC<PreviewPanelProps> = ({
  previewData,
  loading = false,
  newKeywords,
}) => {
  const { t } = useTranslation();

  // 计算统计信息
  const statistics = useMemo(() => {
    return calculateStatistics(previewData, newKeywords);
  }, [previewData, newKeywords]);

  // 渲染关键词标签
  const renderKeywordTag = (keyword: string, isNew: boolean = false) => (
    <Tag
      key={keyword}
      color={isNew ? 'blue' : 'default'}
      style={{
        margin: '2px',
        fontSize: '12px',
        ...(isNew && {
          backgroundColor: '#e6f7ff',
          borderColor: '#91d5ff',
          color: '#1890ff',
          fontWeight: 'bold',
        }),
      }}
    >
      {isNew && <TagsOutlined style={{ marginRight: 2, fontSize: '10px' }} />}
      {keyword}
    </Tag>
  );

  // 渲染单个chunk的预览
  const renderChunkPreview = (item: PreviewData) => {
    const hasNewKeywords = item.newKeywords.length > 0;

    return (
      <div key={item.chunkId} style={{ marginBottom: 16 }}>
        <Card
          size="small"
          title={
            <Space>
              <Text strong style={{ fontSize: '13px' }}>
                Chunk: {item.chunkTitle}
              </Text>
              {hasNewKeywords && (
                <Tag color="green" size="small">
                  +{item.newKeywords.length} 新增
                </Tag>
              )}
            </Space>
          }
          style={{
            border: hasNewKeywords ? '1px solid #52c41a' : '1px solid #d9d9d9',
          }}
        >
          <Space direction="vertical" style={{ width: '100%' }}>
            {/* 原有关键词 */}
            <div>
              <Text
                type="secondary"
                style={{ fontSize: '12px', marginBottom: 4, display: 'block' }}
              >
                原有关键词 ({item.originalKeywords.length}):
              </Text>
              <div style={{ minHeight: 24 }}>
                {item.originalKeywords.length > 0 ? (
                  item.originalKeywords.map((keyword) =>
                    renderKeywordTag(keyword, false),
                  )
                ) : (
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    无
                  </Text>
                )}
              </div>
            </div>

            {/* 新增关键词 */}
            {hasNewKeywords && (
              <div>
                <Text
                  type="secondary"
                  style={{
                    fontSize: '12px',
                    marginBottom: 4,
                    display: 'block',
                  }}
                >
                  新增关键词 ({item.newKeywords.length}):
                </Text>
                <div>
                  {item.newKeywords.map((keyword) =>
                    renderKeywordTag(keyword, true),
                  )}
                </div>
              </div>
            )}

            <Divider style={{ margin: '8px 0' }} />

            {/* 合并后关键词 */}
            <div>
              <Text
                strong
                style={{ fontSize: '12px', marginBottom: 4, display: 'block' }}
              >
                合并后关键词 ({item.mergedKeywords.length}):
              </Text>
              <div
                style={{
                  backgroundColor: '#f5f5f5',
                  padding: 8,
                  borderRadius: 4,
                }}
              >
                {item.mergedKeywords.map((keyword) => {
                  const isNew = item.newKeywords.includes(keyword);
                  return renderKeywordTag(keyword, isNew);
                })}
              </div>
            </div>
          </Space>
        </Card>
      </div>
    );
  };

  if (loading) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: 20 }}>
          <Text>正在生成预览...</Text>
        </div>
      </Card>
    );
  }

  if (previewData.length === 0) {
    return (
      <Card>
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description="暂无预览数据"
        />
      </Card>
    );
  }

  return (
    <div style={{ width: '100%' }}>
      {/* 统计信息 */}
      <Card
        title={
          <Space>
            <EyeOutlined />
            <Title level={5} style={{ margin: 0 }}>
              关键词预览
            </Title>
          </Space>
        }
        style={{ marginBottom: 16 }}
      >
        <Row gutter={16}>
          <Col span={6}>
            <Statistic
              title="总文本块数"
              value={statistics.totalChunks}
              valueStyle={{ fontSize: '18px' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="受影响文本块"
              value={statistics.affectedChunks}
              valueStyle={{ fontSize: '18px', color: '#52c41a' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="输入关键词数"
              value={statistics.totalInputKeywords}
              valueStyle={{ fontSize: '18px', color: '#1890ff' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="实际新增数"
              value={statistics.uniqueNewKeywords}
              valueStyle={{ fontSize: '18px', color: '#722ed1' }}
            />
          </Col>
        </Row>

        {/* 影响程度进度条 */}
        <div style={{ marginTop: 16 }}>
          <Text strong style={{ marginBottom: 8, display: 'block' }}>
            影响程度:
          </Text>
          <Progress
            percent={statistics.affectedPercentage}
            strokeColor={{
              '0%': '#108ee9',
              '100%': '#87d068',
            }}
            format={(percent) =>
              `${statistics.affectedChunks}/${statistics.totalChunks} (${percent}%)`
            }
          />
        </div>

        {/* 输入的关键词预览 */}
        <Divider />
        <div>
          <Text strong style={{ marginBottom: 8, display: 'block' }}>
            输入的关键词:
          </Text>
          <div
            style={{ backgroundColor: '#f0f2f5', padding: 8, borderRadius: 4 }}
          >
            {newKeywords.map((keyword) => renderKeywordTag(keyword, true))}
          </div>
        </div>
      </Card>

      {/* 详细预览 */}
      <Card title={t('chunk.detailedPreview')}>
        <Collapse
          defaultActiveKey={previewData.slice(0, 3).map((item) => item.chunkId)}
          size="small"
        >
          {previewData.map((item) => (
            <Panel
              key={item.chunkId}
              header={
                <Space>
                  <Text>{item.chunkTitle}</Text>
                  {item.newKeywords.length > 0 && (
                    <Tag color="green" size="small">
                      +{item.newKeywords.length}
                    </Tag>
                  )}
                </Space>
              }
            >
              {renderChunkPreview(item)}
            </Panel>
          ))}
        </Collapse>
      </Card>

      {/* 操作提示 */}
      <Card
        style={{
          marginTop: 16,
          backgroundColor: '#f6ffed',
          border: '1px solid #b7eb8f',
        }}
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          <Space>
            <InfoCircleOutlined style={{ color: '#52c41a' }} />
            <Text strong style={{ color: '#52c41a' }}>
              {t('chunk.previewDescription')}:
            </Text>
          </Space>
          <ul style={{ margin: 0, paddingLeft: 20 }}>
            <li>
              <Text style={{ fontSize: '12px' }}>
                {t('chunk.previewInstructions.blueTag')}
              </Text>
            </li>
            <li>
              <Text style={{ fontSize: '12px' }}>
                {t('chunk.previewInstructions.grayTag')}
              </Text>
            </li>
            <li>
              <Text style={{ fontSize: '12px' }}>
                {t('chunk.previewInstructions.autoDedupe')}
              </Text>
            </li>
            <li>
              <Text style={{ fontSize: '12px' }}>
                {t('chunk.previewInstructions.confirmAction')}
              </Text>
            </li>
          </ul>
        </Space>
      </Card>
    </div>
  );
};

export default PreviewPanel;
