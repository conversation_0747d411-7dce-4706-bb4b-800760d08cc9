import { TagsOutlined } from '@ant-design/icons';
import { Alert, Select, Space, Typography } from 'antd';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

const { Text } = Typography;

interface KeywordInputProps {
  keywords: string[];
  onChange: (keywords: string[]) => void;
  placeholder?: string;
  disabled?: boolean;
  maxCount?: number;
  onValidationChange?: (isValid: boolean) => void;
}

const KeywordInput: React.FC<KeywordInputProps> = ({
  keywords,
  onChange,
  placeholder,
  disabled = false,
  maxCount = 50,
  onValidationChange,
}) => {
  const { t } = useTranslation();
  const [duplicateWarning, setDuplicateWarning] = useState<string>('');
  const [validationError, setValidationError] = useState<string>('');

  // 验证关键词有效性
  const validateKeywords = useCallback(
    (keywordList: string[]) => {
      if (keywordList.length === 0) {
        setValidationError('');
        return true;
      }

      // 检查是否有无效关键词
      const invalidKeywords = keywordList.filter((keyword) => {
        const trimmed = keyword.trim();
        return !trimmed || trimmed.length > 100 || /[<>\"'&]/.test(trimmed);
      });

      if (invalidKeywords.length > 0) {
        setValidationError(t('chunk.invalidKeywordFormat'));
        return false;
      }

      setValidationError('');
      return true;
    },
    [t],
  );

  // 通知父组件验证状态
  useEffect(() => {
    const isValid =
      keywords.length > 0 && !validationError && !duplicateWarning;
    onValidationChange?.(isValid);
  }, [keywords.length, validationError, duplicateWarning, onValidationChange]);

  const handleChange = useCallback(
    (value: string[]) => {
      // 过滤空字符串和去重
      const filteredKeywords = value
        .map((keyword) => keyword.trim())
        .filter((keyword) => keyword.length > 0);

      const uniqueKeywords = Array.from(new Set(filteredKeywords));

      // 检查是否有重复
      if (filteredKeywords.length !== uniqueKeywords.length) {
        setDuplicateWarning(t('chunk.duplicateKeywordWarning'));
        setTimeout(() => setDuplicateWarning(''), 3000);
      } else {
        setDuplicateWarning('');
      }

      // 限制关键词数量
      const limitedKeywords = uniqueKeywords.slice(0, maxCount);

      // 验证关键词
      validateKeywords(limitedKeywords);

      onChange(limitedKeywords);
    },
    [onChange, maxCount, t, validateKeywords],
  );

  const filterOption = useCallback(() => true, []); // 允许所有输入

  // 处理键盘事件，支持回车和逗号分隔
  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent) => {
      if (e.key === 'Enter' || e.key === ',') {
        e.preventDefault();
        // 触发输入框的值提交
        const target = e.target as HTMLInputElement;
        if (target.value.trim()) {
          const newKeywords = [...keywords, target.value.trim()];
          handleChange(newKeywords);
          target.value = '';
        }
      }
    },
    [keywords, handleChange],
  );

  const tagRender = useCallback(
    (props: any) => {
      const { label, closable, onClose } = props;
      return (
        <span
          style={{
            display: 'inline-flex',
            alignItems: 'center',
            padding: '2px 8px',
            margin: '2px',
            backgroundColor: '#f0f0f0',
            border: '1px solid #d9d9d9',
            borderRadius: '6px',
            fontSize: '12px',
          }}
        >
          <TagsOutlined style={{ marginRight: 4, fontSize: '10px' }} />
          {label}
          {closable && !disabled && (
            <span
              onClick={onClose}
              style={{
                marginLeft: 4,
                cursor: 'pointer',
                fontSize: '10px',
                color: '#999',
              }}
            >
              ×
            </span>
          )}
        </span>
      );
    },
    [disabled],
  );

  const selectProps = useMemo(
    () => ({
      mode: 'tags' as const,
      style: { width: '100%' },
      placeholder: placeholder || t('chunk.enterKeywords'),
      value: keywords,
      onChange: handleChange,
      tokenSeparators: [',', ' ', '，', '　'], // 支持中英文逗号和空格
      maxTagCount: 'responsive' as const,
      disabled,
      filterOption,
      tagRender,
      maxLength: 100, // 限制单个标签长度
      showSearch: true,
      allowClear: true,
      onKeyDown: handleKeyDown,
      status: validationError ? 'error' : undefined,
    }),
    [
      placeholder,
      t,
      keywords,
      handleChange,
      disabled,
      filterOption,
      tagRender,
      handleKeyDown,
      validationError,
    ],
  );

  return (
    <div style={{ width: '100%' }}>
      <div style={{ marginBottom: 8 }}>
        <Space align="center">
          <Text strong>
            <TagsOutlined style={{ marginRight: 4 }} aria-hidden="true" />
            <label htmlFor="keyword-input" id="keyword-input-label">
              {t('chunk.keywords')}
            </label>
          </Text>
          <Text
            type="secondary"
            style={{ fontSize: '12px' }}
            aria-label={`已输入${keywords.length}个关键词，最多${maxCount}个`}
          >
            ({keywords.length}/{maxCount})
          </Text>
          {keywords.length >= maxCount && (
            <Text
              type="warning"
              style={{ fontSize: '12px' }}
              role="alert"
              aria-live="polite"
            >
              已达到最大数量
            </Text>
          )}
        </Space>
      </div>

      <Select
        {...selectProps}
        id="keyword-input"
        aria-labelledby="keyword-input-label"
        aria-describedby="keyword-input-description"
        role="combobox"
        aria-expanded="false"
        aria-autocomplete="list"
      />

      {/* 显示重复警告 */}
      {duplicateWarning && (
        <Alert
          message={duplicateWarning}
          type="warning"
          showIcon
          style={{ marginTop: 8 }}
          closable
          onClose={() => setDuplicateWarning('')}
        />
      )}

      {/* 显示验证错误 */}
      {validationError && (
        <Alert
          message={validationError}
          type="error"
          showIcon
          style={{ marginTop: 8 }}
          closable
          onClose={() => setValidationError('')}
        />
      )}

      <div style={{ marginTop: 8 }}>
        <Text
          type="secondary"
          style={{ fontSize: '12px' }}
          id="keyword-input-description"
        >
          {t('chunk.keywordInputTip')}
        </Text>
      </div>

      {/* 显示当前关键词列表 */}
      {keywords.length > 0 && (
        <div
          style={{
            marginTop: 12,
            padding: 8,
            backgroundColor: '#f5f5f5',
            borderRadius: 4,
          }}
        >
          <Text
            type="secondary"
            style={{ fontSize: '12px', marginBottom: 4, display: 'block' }}
          >
            当前关键词：
          </Text>
          <div style={{ display: 'flex', flexWrap: 'wrap', gap: 4 }}>
            {keywords.map((keyword, index) => (
              <span
                key={index}
                style={{
                  padding: '2px 6px',
                  backgroundColor: '#e6f7ff',
                  border: '1px solid #91d5ff',
                  borderRadius: 3,
                  fontSize: '11px',
                  color: '#1890ff',
                }}
              >
                {keyword}
              </span>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default KeywordInput;
