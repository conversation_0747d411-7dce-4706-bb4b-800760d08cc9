/**
 * 关键词处理工具函数
 */

import { PreviewData } from './preview-panel';

// 关键词合并函数
export const mergeKeywords = (
  originalKeywords: string[],
  newKeywords: string[],
): string[] => {
  // 创建一个Set来自动去重
  const keywordSet = new Set<string>();

  // 添加原有关键词
  originalKeywords.forEach((keyword) => {
    const trimmed = keyword.trim();
    if (trimmed) {
      keywordSet.add(trimmed);
    }
  });

  // 添加新关键词
  newKeywords.forEach((keyword) => {
    const trimmed = keyword.trim();
    if (trimmed) {
      keywordSet.add(trimmed);
    }
  });

  // 转换回数组并排序
  return Array.from(keywordSet).sort();
};

// 计算新增的关键词
export const getNewKeywords = (
  originalKeywords: string[],
  allKeywords: string[],
): string[] => {
  const originalSet = new Set(originalKeywords.map((k) => k.trim()));
  return allKeywords.filter((keyword) => !originalSet.has(keyword.trim()));
};

// 验证关键词格式
export const validateKeyword = (keyword: string): boolean => {
  const trimmed = keyword.trim();
  if (!trimmed) return false;
  if (trimmed.length > 100) return false;
  if (/[<>\"'&]/.test(trimmed)) return false;
  return true;
};

// 批量验证关键词
export const validateKeywords = (
  keywords: string[],
): { valid: string[]; invalid: string[] } => {
  const valid: string[] = [];
  const invalid: string[] = [];

  keywords.forEach((keyword) => {
    if (validateKeyword(keyword)) {
      valid.push(keyword.trim());
    } else {
      invalid.push(keyword);
    }
  });

  return { valid, invalid };
};

// 生成预览数据
export const generatePreviewData = (
  chunkData: Array<{
    chunkId: string;
    chunkTitle: string;
    originalKeywords: string[];
  }>,
  newKeywords: string[],
): PreviewData[] => {
  return chunkData.map((chunk) => {
    const mergedKeywords = mergeKeywords(chunk.originalKeywords, newKeywords);
    const actualNewKeywords = getNewKeywords(
      chunk.originalKeywords,
      mergedKeywords,
    );

    return {
      chunkId: chunk.chunkId,
      chunkTitle: chunk.chunkTitle,
      originalKeywords: chunk.originalKeywords,
      mergedKeywords,
      newKeywords: actualNewKeywords,
    };
  });
};

// 计算统计信息
export const calculateStatistics = (
  previewData: PreviewData[],
  inputKeywords: string[],
) => {
  const totalChunks = previewData.length;
  const affectedChunks = previewData.filter(
    (item) => item.newKeywords.length > 0,
  ).length;
  const totalInputKeywords = inputKeywords.length;

  // 计算实际新增的唯一关键词数量
  const allNewKeywordsSet = new Set<string>();
  previewData.forEach((item) => {
    item.newKeywords.forEach((keyword) => allNewKeywordsSet.add(keyword));
  });
  const uniqueNewKeywords = allNewKeywordsSet.size;

  return {
    totalChunks,
    affectedChunks,
    totalInputKeywords,
    uniqueNewKeywords,
    affectedPercentage:
      totalChunks > 0 ? Math.round((affectedChunks / totalChunks) * 100) : 0,
  };
};

// 格式化chunk标题（截取前N个字符）
export const formatChunkTitle = (
  content: string,
  maxLength: number = 50,
): string => {
  if (!content) return '未知内容';

  const trimmed = content.trim();
  if (trimmed.length <= maxLength) {
    return trimmed;
  }

  return trimmed.substring(0, maxLength) + '...';
};

// 关键词去重和清理
export const cleanAndDedupeKeywords = (keywords: string[]): string[] => {
  const cleanedSet = new Set<string>();

  keywords.forEach((keyword) => {
    const trimmed = keyword.trim();
    if (trimmed && validateKeyword(trimmed)) {
      cleanedSet.add(trimmed);
    }
  });

  return Array.from(cleanedSet).sort();
};

// 检查关键词是否已存在
export const isKeywordExists = (
  keyword: string,
  existingKeywords: string[],
): boolean => {
  const trimmedKeyword = keyword.trim().toLowerCase();
  return existingKeywords.some(
    (existing) => existing.trim().toLowerCase() === trimmedKeyword,
  );
};

// 批量关键词操作的参数接口
export interface BatchKeywordParams {
  doc_id: string;
  chunk_ids: string[];
  keywords: string[];
  preview?: boolean;
}

// 批量操作结果接口
export interface BatchKeywordResult {
  success_count: number;
  failed_count: number;
  failed_chunks: Array<{
    chunk_id: string;
    error_message: string;
  }>;
  preview_data?: Record<
    string,
    {
      original_keywords: string[];
      merged_keywords: string[];
    }
  >;
}

// 转换API响应为预览数据
export const convertApiResponseToPreviewData = (
  apiResponse: BatchKeywordResult,
  chunkIds: string[],
  inputKeywords: string[],
): PreviewData[] => {
  if (!apiResponse.preview_data) {
    return [];
  }

  return chunkIds.map((chunkId) => {
    const chunkData = apiResponse.preview_data![chunkId];
    if (!chunkData) {
      return {
        chunkId,
        chunkTitle: formatChunkTitle(`Chunk ${chunkId}`),
        originalKeywords: [],
        mergedKeywords: inputKeywords,
        newKeywords: inputKeywords,
      };
    }

    const newKeywords = getNewKeywords(
      chunkData.original_keywords,
      chunkData.merged_keywords,
    );

    return {
      chunkId,
      chunkTitle: formatChunkTitle(`Chunk ${chunkId}`),
      originalKeywords: chunkData.original_keywords,
      mergedKeywords: chunkData.merged_keywords,
      newKeywords,
    };
  });
};
