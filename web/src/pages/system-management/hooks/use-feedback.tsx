import { useCallback } from 'react';
import { message, notification, Modal } from 'antd';
import {
    CheckCircleOutlined,
    InfoCircleOutlined,
    WarningOutlined,
    ExclamationCircleOutlined,
    LoadingOutlined
} from '@ant-design/icons';

// 反馈类型枚举
export enum FeedbackType {
    SUCCESS = 'success',
    INFO = 'info',
    WARNING = 'warning',
    ERROR = 'error',
    LOADING = 'loading',
}

// 反馈配置接口
interface FeedbackConfig {
    type?: 'message' | 'notification' | 'modal';
    duration?: number;
    placement?: 'topLeft' | 'topRight' | 'bottomLeft' | 'bottomRight';
    showIcon?: boolean;
    closable?: boolean;
    onClose?: () => void;
}

// 确认对话框配置
interface ConfirmConfig {
    title: string;
    content: string;
    okText?: string;
    cancelText?: string;
    okType?: 'primary' | 'danger' | 'default';
    danger?: boolean;
    onOk?: () => void | Promise<void>;
    onCancel?: () => void;
}

/**
 * 用户反馈Hook
 * 提供统一的成功、信息、警告等反馈机制
 */
export const useFeedback = () => {
    /**
     * 获取反馈图标
     */
    const getFeedbackIcon = useCallback((type: FeedbackType) => {
        switch (type) {
            case FeedbackType.SUCCESS:
                return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
            case FeedbackType.INFO:
                return <InfoCircleOutlined style={{ color: '#1890ff' }} />;
            case FeedbackType.WARNING:
                return <WarningOutlined style={{ color: '#faad14' }} />;
            case FeedbackType.ERROR:
                return <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />;
            case FeedbackType.LOADING:
                return <LoadingOutlined style={{ color: '#1890ff' }} />;
            default:
                return <InfoCircleOutlined style={{ color: '#8c8c8c' }} />;
        }
    }, []);

    /**
     * 显示消息提示
     */
    const showMessage = useCallback((
        content: string,
        type: FeedbackType = FeedbackType.INFO,
        config: FeedbackConfig = {}
    ) => {
        const { duration = 3, showIcon = true } = config;

        const messageConfig = {
            content,
            duration,
            icon: showIcon ? getFeedbackIcon(type) : undefined,
        };

        switch (type) {
            case FeedbackType.SUCCESS:
                message.success(messageConfig);
                break;
            case FeedbackType.WARNING:
                message.warning(messageConfig);
                break;
            case FeedbackType.ERROR:
                message.error(messageConfig);
                break;
            case FeedbackType.LOADING:
                message.loading(messageConfig);
                break;
            default:
                message.info(messageConfig);
                break;
        }
    }, [getFeedbackIcon]);

    /**
     * 显示通知
     */
    const showNotification = useCallback((
        title: string,
        description: string,
        type: FeedbackType = FeedbackType.INFO,
        config: FeedbackConfig = {}
    ) => {
        const {
            duration = 4.5,
            placement = 'topRight',
            showIcon = true,
            closable = true,
            onClose,
        } = config;

        const notificationConfig = {
            message: title,
            description,
            duration,
            placement,
            icon: showIcon ? getFeedbackIcon(type) : undefined,
            closable,
            onClose,
        };

        switch (type) {
            case FeedbackType.SUCCESS:
                notification.success(notificationConfig);
                break;
            case FeedbackType.WARNING:
                notification.warning(notificationConfig);
                break;
            case FeedbackType.ERROR:
                notification.error(notificationConfig);
                break;
            default:
                notification.info(notificationConfig);
                break;
        }
    }, [getFeedbackIcon]);

    /**
     * 显示确认对话框
     */
    const showConfirm = useCallback((config: ConfirmConfig) => {
        const {
            title,
            content,
            okText = '确定',
            cancelText = '取消',
            okType = 'primary',
            danger = false,
            onOk,
            onCancel,
        } = config;

        Modal.confirm({
            title,
            content,
            okText,
            cancelText,
            okType: danger ? 'danger' : okType,
            icon: danger ? <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} /> : undefined,
            onOk: async () => {
                if (onOk) {
                    try {
                        await onOk();
                    } catch (error) {
                        console.error('Confirm dialog onOk error:', error);
                    }
                }
            },
            onCancel,
        });
    }, []);

   /**
     * 显示删除确对话框
   */
    const showDeleteConfirm = useCallback((
        itemName: string,
        onConfirm: () => void | Promise<void>,
        onCancel?: () => void
    ) => {
        showConfirm({
            title: '确认删除',
            content: `确定要删除 "${itemName}" 吗？此操作不可撤销。`,
            okText: '删除',
            cancelText: '取消',
            danger: true,
            onOk: onConfirm,
            onCancel,
        });
    }, [showConfirm]);

    /**
     * 显示状态变更确认对话框
     */
    const showStatusChangeConfirm = useCallback((
        action: string,
        itemName: string,
        onConfirm: () => void | Promise<void>,
        onCancel?: () => void
    ) => {
        showConfirm({
            title: `确认${action}`,
            content: `确定要${action} "${itemName}" 吗？`,
            okText: action,
            cancelText: '取消',
            onOk: onConfirm,
            onCancel,
        });
    }, [showConfirm]);

    /**
     * 显示批量操作确认对话框
     */
    const showBatchConfirm = useCallback((
        action: string,
        count: number,
        onConfirm: () => void | Promise<void>,
        onCancel?: () => void
    ) => {
        showConfirm({
            title: `确认批量${action}`,
            content: `确定要${action} ${count} 个项目吗？`,
            okText: action,
            cancelText: '取消',
            danger: action.includes('删除') || action.includes('禁用'),
            onOk: onConfirm,
            onCancel,
        });
    }, [showConfirm]);

    /**
     * 成功反馈
     */
    const success = useCallback((
        content: string,
        config?: FeedbackConfig
    ) => {
        const { type = 'message' } = config || {};

        if (type === 'notification') {
            showNotification('操作成功', content, FeedbackType.SUCCESS, config);
        } else {
            showMessage(content, FeedbackType.SUCCESS, config);
        }
    }, [showMessage, showNotification]);

    /**
     * 信息反馈
     */
    const info = useCallback((
        content: string,
        config?: FeedbackConfig
    ) => {
        const { type = 'message' } = config || {};

        if (type === 'notification') {
            showNotification('提示', content, FeedbackType.INFO, config);
        } else {
            showMessage(content, FeedbackType.INFO, config);
        }
    }, [showMessage, showNotification]);

    /**
     * 警告反馈
     */
    const warning = useCallback((
        content: string,
        config?: FeedbackConfig
    ) => {
        const { type = 'message' } = config || {};

        if (type === 'notification') {
            showNotification('警告', content, FeedbackType.WARNING, config);
        } else {
            showMessage(content, FeedbackType.WARNING, config);
        }
    }, [showMessage, showNotification]);

    /**
     * 错误反馈
     */
    const error = useCallback((
        content: string,
        config?: FeedbackConfig
    ) => {
        const { type = 'message' } = config || {};

        if (type === 'notification') {
            showNotification('错误', content, FeedbackType.ERROR, config);
        } else {
            showMessage(content, FeedbackType.ERROR, config);
        }
    }, [showMessage, showNotification]);

    /**
     * 加载反馈
     */
    const loading = useCallback((
        content: string = '处理中...',
        config?: FeedbackConfig
    ) => {
        showMessage(content, FeedbackType.LOADING, { duration: 0, ...config });
    }, [showMessage]);

    /**
     * 清除所有反馈
     */
    const clearAll = useCallback(() => {
        message.destroy();
        notification.destroy();
    }, []);

    /**
     * 操作成功反馈
     */
    const operationSuccess = useCallback((operation: string, target?: string) => {
        const content = target ? `${operation} "${target}" 成功` : `${operation}成功`;
        success(content);
    }, [success]);

    /**
     * 操作失败反馈
     */
    const operationError = useCallback((operation: string, target?: string, reason?: string) => {
        let content = target ? `${operation} "${target}" 失败` : `${operation}失败`;
        if (reason) {
            content += `：${reason}`;
        }
        error(content);
    }, [error]);

    /**
     * 数据加载反馈
     */
    const dataLoading = useCallback((dataType: string = '数据') => {
        loading(`正在加载${dataType}...`);
    }, [loading]);

    /**
     * 数据保存反馈
     */
    const dataSaving = useCallback((dataType: string = '数据') => {
        loading(`正在保存${dataType}...`);
    }, [loading]);

    /**
     * 数据删除反馈
     */
    const dataDeleting = useCallback((dataType: string = '数据') => {
        loading(`正在删除${dataType}...`);
    }, [loading]);

    return {
        // 基础方法
        showMessage,
        showNotification,
        showConfirm,

        // 确认对话框
        showDeleteConfirm,
        showStatusChangeConfirm,
        showBatchConfirm,

        // 便捷方法
        success,
        info,
        warning,
        error,
        loading,
        clearAll,

        // 标准反馈
        operationSuccess,
        operationError,
        dataLoading,
        dataSaving,
        dataDeleting,
    };
};

/**
 * 创建带有反馈的异步操作包装器
 */
export const withFeedback = <T extends (...args: any[]) => Promise<any>>(
    asyncFn: T,
    feedback: ReturnType<typeof useFeedback>,
    options: {
        loadingMessage?: string;
        successMessage?: string;
        errorMessage?: string;
        showLoading?: boolean;
        showSuccess?: boolean;
        showError?: boolean;
    } = {}
): T => {
    const {
        loadingMessage = '处理中...',
        successMessage = '操作成功',
        errorMessage = '操作失败',
        showLoading = true,
        showSuccess = true,
        showError = true,
    } = options;

    return (async (...args: Parameters<T>) => {
        let hideLoading: (() => void) | null = null;

        try {
            if (showLoading) {
                feedback.loading(loadingMessage);
                hideLoading = () => feedback.clearAll();
            }

            const result = await asyncFn(...args);

            if (hideLoading) {
                hideLoading();
            }

            if (showSuccess) {
                feedback.success(successMessage);
            }

            return result;
        } catch (error) {
            if (hideLoading) {
                hideLoading();
            }

            if (showError) {
                feedback.error(errorMessage);
            }

            throw error;
        }
    }) as T;
};

export default useFeedback;