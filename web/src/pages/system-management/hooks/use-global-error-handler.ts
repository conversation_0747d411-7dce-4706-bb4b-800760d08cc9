import { useEffect, useCallback } from 'react';
import { useErrorHandler, ErrorType } from './use-error-handler';
import { useFeedback } from './use-feedback';

/**
 * 全局错误处理Hook
 * 监听全局错误事件并提供统一处理
 */
export const useGlobalErrorHandler = () => {
    const errorHandler = useErrorHandler({
        showNotification: true,
        autoClose: true,
        duration: 5,
    });

    const feedback = useFeedback();

    /**
     * 处理未捕获的Promise拒绝
     */
    const handleUnhandledRejection = useCallback((event: PromiseRejectionEvent) => {
        console.error('Unhandled promise rejection:', event.reason);

        errorHandler.handleError({
            type: ErrorType.UNKNOWN_ERROR,
            message: '系统发生未处理的错误，请刷新页面重试',
            details: event.reason,
        });

        // 阻止默认的控制台错误输出
        event.preventDefault();
    }, [errorHandler]);

    /**
     * 处理全局JavaScript错误
     */
    const handleGlobalError = useCallback((event: ErrorEvent) => {
        console.error('Global error:', event.error);

        errorHandler.handleError({
            type: ErrorType.UNKNOWN_ERROR,
            message: '页面发生JavaScript错误，请刷新页面重试',
            details: {
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                error: event.error,
            },
        });
    }, [errorHandler]);

    /**
     * 处理资源加载错误
     */
    const handleResourceError = useCallback((event: Event) => {
        const target = event.target as HTMLElement;
        const tagName = target?.tagName?.toLowerCase();

        if (tagName === 'img') {
            console.warn('Image load error:', (target as HTMLImageElement).src);
            // 图片加载错误通常不需要显示给用户
            return;
        }

        if (tagName === 'script' || tagName === 'link') {
            console.error('Resource load error:', target);

            errorHandler.handleError({
                type: ErrorType.NETWORK_ERROR,
                message: '资源加载失败，请检查网络连接后刷新页面',
                details: {
                    tagName,
                    src: (target as any).src || (target as any).href,
                },
            });
        }
    }, [errorHandler]);

    /**
     * 监听网络状态变化
     */
    const handleNetworkChange = useCallback(() => {
        if (!navigator.onLine) {
            feedback.warning('网络连接已断开，请检查网络设置', {
                type: 'notification',
                duration: 0, // 不自动关闭
            });
        } else {
            feedback.success('网络连接已恢复', {
                type: 'notification',
                duration: 3,
            });
        }
    }, [feedback]);

    /**
     * 处理页面可见性变化
     */
    const handleVisibilityChange = useCallback(() => {
        if (document.visibilityState === 'visible') {
            // 页面重新可见时，可以检查网络状态或刷新数据
            if (!navigator.onLine) {
                feedback.warning('当前网络连接不可用', {
                    type: 'notification',
                });
            }
        }
    }, [feedback]);

    /**
     * 注册全局错误监听器
     */
    useEffect(() => {
        // 监听未捕获的Promise拒绝
        window.addEventListener('unhandledrejection', handleUnhandledRejection);

        // 监听全局JavaScript错误
        window.addEventListener('error', handleGlobalError);

        // 监听资源加载错误
        window.addEventListener('error', handleResourceError, true);

        // 监听网络状态变化
        window.addEventListener('online', handleNetworkChange);
        window.addEventListener('offline', handleNetworkChange);

        // 监听页面可见性变化
        document.addEventListener('visibilitychange', handleVisibilityChange);

        return () => {
            window.removeEventListener('unhandledrejection', handleUnhandledRejection);
            window.removeEventListener('error', handleGlobalError);
            window.removeEventListener('error', handleResourceError, true);
            window.removeEventListener('online', handleNetworkChange);
            window.removeEventListener('offline', handleNetworkChange);
            document.removeEventListener('visibilitychange', handleVisibilityChange);
        };
    }, [
        handleUnhandledRejection,
        handleGlobalError,
        handleResourceError,
        handleNetworkChange,
        handleVisibilityChange,
    ]);

    /**
     * 手动触发网络状态检查
     */
    const checkNetworkStatus = useCallback(() => {
        if (!navigator.onLine) {
            feedback.warning('当前网络连接不可用，请检查网络设置', {
                type: 'notification',
            });
            return false;
        }
        return true;
    }, [feedback]);

    /**
     * 重试操作的包装器
     */
    const withRetry = useCallback(<T extends (...args: any[]) => Promise<any>>(
        asyncFn: T,
        maxRetries: number = 3,
        retryDelay: number = 1000
    ): T => {
        return (async (...args: Parameters<T>) => {
            let lastError: any;

            for (let attempt = 1; attempt <= maxRetries; attempt++) {
                try {
                    return await asyncFn(...args);
                } catch (error) {
                    lastError = error;

                    // 如果是网络错误且还有重试次数，则重试
                    if (attempt < maxRetries && (
                        error?.code === 'NETWORK_ERROR' ||
                        error?.response?.status >= 500
                    )) {
                        feedback.info(`操作失败，正在重试 (${attempt}/${maxRetries})...`, {
                            duration: 2,
                        });

                        // 等待一段时间后重试
                        await new Promise(resolve => setTimeout(resolve, retryDelay * attempt));
                        continue;
                    }

                    // 不是网络错误或已达到最大重试次数，直接抛出错误
                    throw error;
                }
            }

            throw lastError;
        }) as T;
    }, [feedback]);

    /**
     * 安全执行异步操作
     */
    const safeExecute = useCallback(async <T>(
        asyncFn: () => Promise<T>,
        fallbackValue?: T,
        errorMessage?: string
    ): Promise<T | undefined> => {
        try {
            return await asyncFn();
        } catch (error) {
            errorHandler.handleError(error);

            if (errorMessage) {
                feedback.error(errorMessage);
            }

            return fallbackValue;
        }
    }, [errorHandler, feedback]);

    return {
        errorHandler,
        feedback,
        checkNetworkStatus,
        withRetry,
        safeExecute,
    };
};

export default useGlobalErrorHandler;