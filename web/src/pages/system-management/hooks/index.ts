// 导出所有系统管理相关的Hooks
export { usePermissionCheck, useSuperuserAuth } from './use-permission-check';
export { useSystemManagement } from './use-system-management';
export { useErrorHandler } from './use-error-handler';
export { useSuperuserGuard, usePermissionGuard } from './use-superuser-guard';

// 重新导出类型定义
export type {
    IPermissionCheckResult,
    ISystemManagementState,
    ISystemManagementError,
    SystemManagementErrorType,
} from '../types';