import { useFetchUserInfo } from '@/hooks/user-setting-hooks';
import { useMemo } from 'react';

export const usePermissionCheck = () => {
    const { data: userInfo, loading } = useFetchUserInfo();

    const isSuperuser = useMemo(() => {
        return userInfo?.is_superuser === true;
    }, [userInfo?.is_superuser]);

    const hasSystemManagementAccess = useMemo(() => {
        return isSuperuser;
    }, [isSuperuser]);

    return {
        userInfo,
        loading,
        isSuperuser,
        hasSystemManagementAccess,
    };
};

export default usePermissionCheck;