import { useEffect } from 'react';
import { message } from 'antd';
import { usePermissionCheck } from './use-permission-check';

/**
 * 超级用户权限守卫Hook
 * 用于在组件内部进行权限检查，如果权限不足会自动处理
 */
export const useSuperuserGuard = (options?: {
    redirectUrl?: string;
    showMessage?: boolean;
    onPermissionDenied?: () => void;
}) => {
    const { isSuperuser, loading, error } = usePermissionCheck();
    const {
        redirectUrl = '/knowledge',
        showMessage = true,
        onPermissionDenied
    } = options || {};

    useEffect(() => {
        if (!loading && !isSuperuser) {
            // 显示错误消息
            if (showMessage) {
                message.error(error || '权限不足，无法访问此功能');
            }

            // 执行自定义回调
            if (onPermissionDenied) {
                onPermissionDenied();
            } else {
                // 默认重定向
                setTimeout(() => {
                    window.location.href = redirectUrl;
                }, 1500);
            }
        }
    }, [loading, isSuperuser, error, redirectUrl, showMessage, onPermissionDenied]);

    return {
        isSuperuser,
        loading,
        error,
        hasPermission: isSuperuser && !loading && !error,
    };
};

/**
 * 权限检查Hook（不自动处理权限不足的情况）
 */
export const usePermissionGuard = () => {
    const { isSuperuser, loading, error } = usePermissionCheck();

    return {
        isSuperuser,
        loading,
        error,
        hasPermission: isSuperuser && !loading && !error,
        checkPermission: () => {
            if (loading) {
                throw new Error('权限检查中，请稍候');
            }
            if (!isSuperuser) {
                throw new Error(error || '权限不足');
            }
            return true;
        },
    };
};

export default useSuperuserGuard;