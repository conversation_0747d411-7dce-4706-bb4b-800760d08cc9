import { useCallback } from 'react';
import { message, Modal, notification } from 'antd';
import { ExclamationCircleOutlined, CloseCircleOutlined, WarningOutlined } from '@ant-design/icons';

// 错误类型枚举
export enum ErrorType {
    NETWORK_ERROR = 'NETWORK_ERROR',
    PERMISSION_ERROR = 'PERMISSION_ERROR',
    VALIDATION_ERROR = 'VALIDATION_ERROR',
    SERVER_ERROR = 'SERVER_ERROR',
    TIMEOUT_ERROR = 'TIMEOUT_ERROR',
    UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}

// 错误信息接口
export interface ErrorInfo {
    type: ErrorType;
    message: string;
    code?: number;
    details?: any;
    timestamp?: number;
}

// 错误处理配置
interface ErrorHandlerConfig {
    showNotification?: boolean;
    showMessage?: boolean;
    showModal?: boolean;
    autoClose?: boolean;
    duration?: number;
    onError?: (error: ErrorInfo) => void;
}

// 默认错误消息映射
const DEFAULT_ERROR_MESSAGES = {
    [ErrorType.NETWORK_ERROR]: '网络连接失败，请检查网络设置后重试',
    [ErrorType.PERMISSION_ERROR]: '权限不足，请联系管理员',
    [ErrorType.VALIDATION_ERROR]: '输入数据格式不正确，请检查后重试',
    [ErrorType.SERVER_ERROR]: '服务器内部错误，请稍后重试',
    [ErrorType.TIMEOUT_ERROR]: '请求超时，请稍后重试',
    [ErrorType.UNKNOWN_ERROR]: '发生未知错误，请稍后重试',
};

// HTTP状态码到错误类型的映射
const HTTP_STATUS_TO_ERROR_TYPE: Record<number, ErrorType> = {
    400: ErrorType.VALIDATION_ERROR,
    401: ErrorType.PERMISSION_ERROR,
    403: ErrorType.PERMISSION_ERROR,
    404: ErrorType.VALIDATION_ERROR,
    408: ErrorType.TIMEOUT_ERROR,
    422: ErrorType.VALIDATION_ERROR,
    429: ErrorType.SERVER_ERROR,
    500: ErrorType.SERVER_ERROR,
    502: ErrorType.SERVER_ERROR,
    503: ErrorType.SERVER_ERROR,
    504: ErrorType.TIMEOUT_ERROR,
};

/**
 * 错误处理Hook
 * 提供统一的错误处理和用户反馈机制
 */
export const useErrorHandler = (config: ErrorHandlerConfig = {}) => {
    const {
        showNotification = true,
        showMessage = false,
        showModal = false,
        autoClose = true,
        duration = 4.5,
        onError,
    } = config;

    /**
     * 解析错误信息
     */
    const parseError = useCallback((error: any): ErrorInfo => {
        const timestamp = Date.now();

        // 如果已经是ErrorInfo格式
        if (error && typeof error === 'object' && error.type) {
            return { ...error, timestamp };
        }

        // 处理HTTP响应错误
        if (error?.response) {
            const { status, data } = error.response;
            const errorType = HTTP_STATUS_TO_ERROR_TYPE[status] || ErrorType.SERVER_ERROR;

            return {
                type: errorType,
                message: data?.message || data?.retmsg || DEFAULT_ERROR_MESSAGES[errorType],
                code: status,
                details: data,
                timestamp,
            };
        }

        // 处理网络错误
        if (error?.code === 'NETWORK_ERROR' || error?.message?.includes('Network Error')) {
            return {
                type: ErrorType.NETWORK_ERROR,
                message: DEFAULT_ERROR_MESSAGES[ErrorType.NETWORK_ERROR],
                details: error,
                timestamp,
            };
        }

        // 处理超时错误
        if (error?.code === 'ECONNABORTED' || error?.message?.includes('timeout')) {
            return {
                type: ErrorType.TIMEOUT_ERROR,
                message: DEFAULT_ERROR_MESSAGES[ErrorType.TIMEOUT_ERROR],
                details: error,
                timestamp,
            };
        }

        // 处理字符串错误
        if (typeof error === 'string') {
            return {
                type: ErrorType.UNKNOWN_ERROR,
                message: error,
                timestamp,
            };
        }

        // 处理Error对象
        if (error instanceof Error) {
            return {
                type: ErrorType.UNKNOWN_ERROR,
                message: error.message || DEFAULT_ERROR_MESSAGES[ErrorType.UNKNOWN_ERROR],
                details: error,
                timestamp,
            };
        }

        // 默认未知错误
        return {
            type: ErrorType.UNKNOWN_ERROR,
            message: DEFAULT_ERROR_MESSAGES[ErrorType.UNKNOWN_ERROR],
            details: error,
            timestamp,
        };
    }, []);

    /**
     * 获取错误图标
     */
    const getErrorIcon = useCallback((type: ErrorType) => {
        switch (type) {
            case ErrorType.PERMISSION_ERROR:
                return <ExclamationCircleOutlined style={{ color: '#faad14' }} />;
            case ErrorType.VALIDATION_ERROR:
                return <WarningOutlined style={{ color: '#faad14' }} />;
            case ErrorType.NETWORK_ERROR:
            case ErrorType.SERVER_ERROR:
            case ErrorType.TIMEOUT_ERROR:
                return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />;
            default:
                return <ExclamationCircleOutlined style={{ color: '#8c8c8c' }} />;
        }
    }, []);

    /**
     * 显示通知
     */
    const showNotificationMessage = useCallback((errorInfo: ErrorInfo) => {
        const { type, message: errorMessage } = errorInfo;

        notification.error({
            message: '操作失败',
            description: errorMessage,
            icon: getErrorIcon(type),
            duration: autoClose ? duration : 0,
            placement: 'topRight',
        });
    }, [getErrorIcon, autoClose, duration]);

    /**
     * 显示消息提示
     */
    const showMessageAlert = useCallback((errorInfo: ErrorInfo) => {
        message.error({
            content: errorInfo.message,
            duration: autoClose ? duration : 0,
            icon: getErrorIcon(errorInfo.type),
        });
    }, [getErrorIcon, autoClose, duration]);

    /**
     * 显示模态框
     */
    const showModalAlert = useCallback((errorInfo: ErrorInfo) => {
        const { type, message: errorMessage, details } = errorInfo;

        Modal.error({
            title: '操作失败',
            content: (
                <div>
                <p>{ errorMessage } </p>
                    { details && process.env.NODE_ENV === 'development' && (
                <details style={{ marginTop: 16 }}>
    <summary style={{ cursor: 'pointer', color: '#8c8c8c' }}>
        详细信息(仅开发环境显示)
        </summary>
        < pre style = {{
    marginTop: 8,
        padding: 8,
            background: '#f5f5f5',
                borderRadius: 4,
                    fontSize: 12,
                        maxHeight: 200,
                            overflow: 'auto'
}}>
    { JSON.stringify(details, null, 2) }
    </pre>
    </details>
                    )}
</div>
            ),
icon: getErrorIcon(type),
    okText: '确定',
        });
    }, [getErrorIcon]);

/**
 * 显示成功消息
 */
const showSuccess = useCallback((successMessage: string) => {
    message.success({
        content: successMessage,
        duration: autoClose ? duration : 0,
    });
}, [autoClose, duration]);

/**
 * 显示成功通知
 */
const showSuccessNotification = useCallback((title: string, description: string) => {
    notification.success({
        message: title,
        description: description,
        duration: autoClose ? duration : 0,
        placement: 'topRight',
    });
}, [autoClose, duration]);

/**
 * 主要的错误处理函数
 */
const handleError = useCallback((error: any) => {
    const errorInfo = parseError(error);

    // 调用自定义错误处理回调
    if (onError) {
        onError(errorInfo);
    }

    // 根据配置显示不同类型的反馈
    if (showNotification) {
        showNotificationMessage(errorInfo);
    } else if (showMessage) {
        showMessageAlert(errorInfo);
    } else if (showModal) {
        showModalAlert(errorInfo);
    }

    // 在开发环境下打印错误详情
    if (process.env.NODE_ENV === 'development') {
        console.error('Error handled:', errorInfo);
    }

    return errorInfo;
}, [
    parseError,
    onError,
    showNotification,
    showMessage,
    showModal,
    showNotificationMessage,
    showMessageAlert,
    showModalAlert,
]);

/**
 * 处理特定类型的错误
 */
const handleNetworkError = useCallback(() => {
    handleError({
        type: ErrorType.NETWORK_ERROR,
        message: DEFAULT_ERROR_MESSAGES[ErrorType.NETWORK_ERROR],
    });
}, [handleError]);

const handlePermissionError = useCallback((message?: string) => {
    handleError({
        type: ErrorType.PERMISSION_ERROR,
        message: message || DEFAULT_ERROR_MESSAGES[ErrorType.PERMISSION_ERROR],
    });
}, [handleError]);

const handleValidationError = useCallback((message?: string) => {
    handleError({
        type: ErrorType.VALIDATION_ERROR,
        message: message || DEFAULT_ERROR_MESSAGES[ErrorType.VALIDATION_ERROR],
    });
}, [handleError]);

const handleServerError = useCallback((message?: string) => {
    handleError({
        type: ErrorType.SERVER_ERROR,
        message: message || DEFAULT_ERROR_MESSAGES[ErrorType.SERVER_ERROR],
    });
}, [handleError]);

/**
 * 清除所有错误提示
 */
const clearErrors = useCallback(() => {
    message.destroy();
    notification.destroy();
}, []);

return {
    handleError,
    handleNetworkError,
    handlePermissionError,
    handleValidationError,
    handleServerError,
    clearErrors,
    parseError,
    showSuccess,
    showSuccessNotification,
};
};

/**
 * 创建带有错误处理的异步函数包装器
 */
export const withErrorHandler = <T extends (...args: any[]) => Promise<any>>(
    asyncFn: T,
    errorHandler: ReturnType<typeof useErrorHandler>['handleError']
): T => {
    return (async (...args: Parameters<T>) => {
        try {
            return await asyncFn(...args);
        } catch (error) {
            errorHandler(error);
            throw error; // 重新抛出错误，让调用者可以进行额外处理
        }
    }) as T;
};

/**
 * 错误边界组件的错误处理
 */
export const handleComponentError = (error: Error, errorInfo: any) => {
    const errorHandler = useErrorHandler({
        showNotification: true,
        showModal: true,
    });

    errorHandler.handleError({
        type: ErrorType.UNKNOWN_ERROR,
        message: '组件渲染出现错误，请刷新页面重试',
        details: {
            error: error.message,
            stack: error.stack,
            componentStack: errorInfo.componentStack,
        },
    });
};

export default useErrorHandler;