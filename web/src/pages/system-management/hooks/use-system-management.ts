import { useState, useCallback, useEffect } from 'react';
import { message } from 'antd';
import { SystemManagementTab, ISystemManagementState } from '../types';
import { STORAGE_KEYS } from '../constants';
// import { safeJsonParse, safeJsonStringify } from '../utils';

/**
 * 系统管理主页面状态管理Hook
 */
export const useSystemManagement = () => {
    const [state, setState] = useState<ISystemManagementState>({
        activeTab: 'users',
        loading: false,
        error: null,
    });

    // 初始化时从本地存储恢复状态
    useEffect(() => {
        const savedTab = localStorage.getItem(STORAGE_KEYS.SYSTEM_MANAGEMENT_TAB);
        if (savedTab && (savedTab === 'users' || savedTab === 'knowledgebases')) {
            setState(prev => ({
                ...prev,
                activeTab: savedTab as SystemManagementTab,
            }));
        }
    }, []);

    // 切换标签页
    const handleTabChange = useCallback((tab: SystemManagementTab) => {
        setState(prev => ({
            ...prev,
            activeTab: tab,
            error: null, // 切换时清除错误状态
        }));

        // 保存到本地存储
        localStorage.setItem(STORAGE_KEYS.SYSTEM_MANAGEMENT_TAB, tab);
    }, []);

    // 设置加载状态
    const setLoading = useCallback((loading: boolean) => {
        setState(prev => ({
            ...prev,
            loading,
        }));
    }, []);

    // 设置错误状态
    const setError = useCallback((error: string | null) => {
        setState(prev => ({
            ...prev,
            error,
        }));

        if (error) {
            message.error(error);
        }
    }, []);

    // 清除错误状态
    const clearError = useCallback(() => {
        setState(prev => ({
            ...prev,
            error: null,
        }));
    }, []);

    // 重置状态
    const resetState = useCallback(() => {
        setState({
            activeTab: 'users',
            loading: false,
            error: null,
        });
        localStorage.removeItem(STORAGE_KEYS.SYSTEM_MANAGEMENT_TAB);
    }, []);

    return {
        // 状态
        activeTab: state.activeTab,
        loading: state.loading,
        error: state.error,

        // 操作方法
        handleTabChange,
        setLoading,
        setError,
        clearError,
        resetState,
    };
};

export default useSystemManagement;