@import '../../theme/vars.less';
@import './styles/mixins.less';
@import './styles/responsive.less';
@import './styles/themes.less';
@import './styles/loading.less';
@import './styles/antd-overrides.less';

.systemManagementWrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  // 网络状态警告
  .ant-alert {
    margin: 0;
    border-radius: 0;
    border-left: none;
    border-right: none;
    border-top: none;
  }
}

.systemManagement {
  flex: 1;
  width: 100%;
  height: 100%;
  display: flex;

  .contentWrapper {
    padding: 32px;
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    @media screen and (max-height: 768px) {
      overflow-y: auto;
    }
  } // 加载状态容器 - 不带内边距，确保加载动画居中
  .loadingWrapper {
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #fff;
  }

  // 加载状态
  .loading {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;

    .ant-spin {
      .ant-spin-text {
        margin-top: 12px;
        color: #8c8c8c;
        font-size: 14px;
      }
    }
  }

  // 错误状态
  .errorState {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 400px;
    background: #fff;

    .ant-result {
      padding: 24px;
    }
  }

  // 空状态
  .emptyState {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 300px;
    background: #fff;

    .ant-empty {
      .ant-empty-description {
        color: #8c8c8c;
        font-size: 14px;
      }
    }
  }
}
