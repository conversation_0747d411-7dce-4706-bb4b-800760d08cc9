export interface User {
    id: string;
    nickname: string;
    email: string;
    status: 'active' | 'inactive';
    is_superuser: boolean;
    create_time: string;
    update_time: string;
}

// 用户管理模块使用的接口
export interface IUser {
    id: string;
    nickname: string;
    email: string;
    status: '1' | '0';
    is_superuser: boolean;
    create_time: number;
    update_time: number;
    create_date: string;
    update_date: string;
}

export interface Knowledgebase {
    id: string;
    name: string;
    description: string;
    created_by: string;
    creator_name: string;
    status: 'active' | 'inactive';
    create_time: string;
    update_time: string;
    document_count: number;
}

export interface PaginationParams {
    current: number;
    pageSize: number;
    total: number;
}

// 分页接口
export interface IPagination {
    current: number;
    pageSize: number;
    total: number;
}

export interface UserFilters {
    search: string;
    status: string;
}

export interface KnowledgebaseFilters {
    search: string;
    status: string;
    creator: string;
}

export interface ApiResponse<T> {
    code: number;
    data: T;
    message?: string;
}

export interface UserListResponse {
    users: User[];
    total: number;
    page: number;
    size: number;
}

export interface KnowledgebaseListResponse {
    knowledgebases: Knowledgebase[];
    total: number;
    page: number;
    size: number;
}

export type TabType = 'users' | 'knowledgebases';

// API相关接口定义
export interface ISystemManagementApiResponse<T> {
    code: number;
    data: T;
    message?: string;
}

// 用户搜索参数接口
export interface IUserSearchParams {
    page: number;
    size: number;
    search: string;
    status: 'all' | '1' | '0';
}

// 用户列表响应接口
export interface IUserListResponse {
    users: IUser[];
    total: number;
    page: number;
    size: number;
    total_pages: number;
}

// 用户状态更新请求接口
export interface IUpdateUserStatusRequest {
    status: '1' | '0';
}

// 知识库搜索参数接口
export interface IKnowledgebaseSearchParams {
    page: number;
    size: number;
    search: string;
    status: 'all' | '1' | '0';
    creator?: string;
}

// 知识库列表响应接口
export interface IKnowledgebaseListResponse {
    knowledgebases: IKnowledgebaseForAdmin[];
    total: number;
    page: number;
    size: number;
    total_pages: number;
}

// 知识库详情响应接口
export interface IKnowledgebaseDetailResponse {
    knowledgebase: IKnowledgebaseForAdmin;
}

// 知识库状态更新请求接口
export interface IUpdateKnowledgebaseStatusRequest {
    status: '1' | '0';
}

// 系统管理标签类型
export type SystemManagementTab = 'users' | 'knowledgebases';

// 系统管理状态接口
export interface ISystemManagementState {
    activeTab: SystemManagementTab;
    loading: boolean;
    error: string | null;
}

// 权限检查结果接口
export interface IPermissionCheckResult {
    isSuperuser: boolean;
    loading: boolean;
    error: string | null;
}

// 系统管理错误类型
export type SystemManagementErrorType = 
    | 'PERMISSION_DENIED'
    | 'NETWORK_ERROR'
    | 'API_ERROR'
    | 'VALIDATION_ERROR'
    | 'UNKNOWN_ERROR';

// 系统管理错误接口
export interface ISystemManagementError {
    type: SystemManagementErrorType;
    message: string;
    details?: any;
    timestamp?: number;
}

// 用户更新请求接口
export interface IUpdateUserRequest {
    nickname?: string;
    status?: '1' | '0';
}

// 用户模态框属性接口
export interface IUserModalProps {
    visible: boolean;
    user: IUser | null;
    loading: boolean;
    onOk: (values: IUpdateUserRequest) => Promise<void>;
    onCancel: () => void;
}

// 用户管理状态接口
export interface IUserManagementState {
    users: IUser[];
    loading: boolean;
    pagination: IPagination;
    filters: {
        search: string;
        status: 'all' | '1' | '0';
    };
    selectedUser: IUser | null;
    editModalVisible: boolean;
}

// 用户管理Hook返回值接口
export interface IUseUserManagementReturn {
    users: IUser[];
    loading: boolean;
    pagination: IPagination;
    filters: IUserManagementState['filters'];
    selectedUser: IUser | null;
    editModalVisible: boolean;
    handleSearch: (search: string) => void;
    handleStatusFilter: (status: 'all' | '1' | '0') => void;
    handlePageChange: (page: number, pageSize?: number) => void;
    handleEditUser: (user: IUser) => void;
    handleUpdateUser: (values: IUpdateUserRequest) => Promise<void>;
    handleToggleUserStatus: (user: IUser) => Promise<void>;
    handleCloseEditModal: () => void;
    refreshUsers: () => Promise<void>;
    resetFilters: () => void;
}

// 知识库管理相关接口
export interface IKnowledgebaseForAdmin {
    id: string;
    name: string;
    description: string;
    creator_id: string;
    creator_name: string;
    status: '1' | '0';
    create_time: number;
    update_time: number;
    doc_num: number;
    chunk_num: number;
    token_num: number;
}

// 知识库模态框属性接口
export interface IKnowledgebaseModalProps {
    visible: boolean;
    knowledgebase: IKnowledgebaseForAdmin | null;
    loading: boolean;
    onClose: () => void;
    onToggleStatus?: (knowledgebase: IKnowledgebaseForAdmin) => Promise<void>;
}

// 知识库管理状态接口
export interface IKnowledgebaseManagementState {
    knowledgebases: IKnowledgebaseForAdmin[];
    loading: boolean;
    pagination: IPagination;
    filters: {
        search: string;
        status: 'all' | '1' | '0';
        creator: string;
    };
    selectedKnowledgebase: IKnowledgebaseForAdmin | null;
    detailModalVisible: boolean;
}

// 知识库管理Hook返回值接口
export interface IUseKnowledgebaseManagementReturn {
    knowledgebases: IKnowledgebaseForAdmin[];
    loading: boolean;
    pagination: IPagination;
    filters: IKnowledgebaseManagementState['filters'];
    selectedKnowledgebase: IKnowledgebaseForAdmin | null;
    detailModalVisible: boolean;
    handleSearch: (search: string) => void;
    handleStatusFilter: (status: 'all' | '1' | '0') => void;
    handleCreatorFilter: (creator: string) => void;
    handlePageChange: (page: number, pageSize?: number) => void;
    handleViewDetail: (knowledgebase: IKnowledgebaseForAdmin) => void;
    handleToggleStatus: (knowledgebase: IKnowledgebaseForAdmin) => Promise<void>;
    handleCloseDetailModal: () => void;
    refreshKnowledgebases: () => Promise<void>;
    resetFilters: () => void;
}