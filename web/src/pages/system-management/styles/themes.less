// 系统管理页面主题配置

// 默认主题（浅色）
.theme-light {
    // 基础颜色
    --bg-primary: #ffffff;
    --bg-secondary: #f5f5f5;
    --bg-tertiary: #fafafa;

    // 文本颜色
    --text-primary: #262626;
    --text-secondary: #595959;
    --text-tertiary: #8c8c8c;
    --text-disabled: #bfbfbf;

    // 边框颜色
    --border-primary: #f0f0f0;
    --border-secondary: #d9d9d9;
    --border-hover: #40a9ff;

    // 状态颜色
    --color-success: #52c41a;
    --color-success-bg: #f6ffed;
    --color-success-border: #b7eb8f;

    --color-warning: #faad14;
    --color-warning-bg: #fffbe6;
    --color-warning-border: #ffe58f;

    --color-error: #ff4d4f;
    --color-error-bg: #fff2f0;
    --color-error-border: #ffccc7;

    --color-info: #1890ff;
    --color-info-bg: #e6f7ff;
    --color-info-border: #91d5ff;

    // 阴影
    --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.06);
    --shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.1);
    --shadow-heavy: 0 8px 24px rgba(0, 0, 0, 0.15);

    // 悬停效果
    --hover-bg: #f5f5f5;
    --active-bg: #e6f7ff;

    // 表格
    --table-header-bg: #fafafa;
    --table-row-hover-bg: #f5f5f5;
    --table-border-color: #f0f0f0;

    // 侧边栏
    --sidebar-bg: #ffffff;
    --sidebar-border: #f0f0f0;
    --sidebar-item-hover: #f5f5f5;
    --sidebar-item-active: #e6f7ff;
    --sidebar-item-active-border: #1890ff;

    // 模态框
    --modal-bg: #ffffff;
    --modal-header-border: #f0f0f0;
    --modal-footer-border: #f0f0f0;
}

// 暗色主题
.theme-dark {
    // 基础颜色
    --bg-primary: #1f1f1f;
    --bg-secondary: #141414;
    --bg-tertiary: #0f0f0f;

    // 文本颜色
    --text-primary: #d9d9d9;
    --text-secondary: #a6a6a6;
    --text-tertiary: #8c8c8c;
    --text-disabled: #595959;

    // 边框颜色
    --border-primary: #303030;
    --border-secondary: #434343;
    --border-hover: #40a9ff;

    // 状态颜色（暗色主题下稍微调整）
    --color-success: #73d13d;
    --color-success-bg: #162312;
    --color-success-border: #274916;

    --color-warning: #ffc53d;
    --color-warning-bg: #2b2111;
    --color-warning-border: #613400;

    --color-error: #ff7875;
    --color-error-bg: #2a1215;
    --color-error-border: #58181c;

    --color-info: #40a9ff;
    --color-info-bg: #111b26;
    --color-info-border: #153450;

    // 阴影（暗色主题下更深）
    --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.15);
    --shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.25);
    --shadow-heavy: 0 8px 24px rgba(0, 0, 0, 0.35);

    // 悬停效果
    --hover-bg: #262626;
    --active-bg: #111b26;

    // 表格
    --table-header-bg: #262626;
    --table-row-hover-bg: #262626;
    --table-border-color: #303030;

    // 侧边栏
    --sidebar-bg: #1f1f1f;
    --sidebar-border: #303030;
    --sidebar-item-hover: #262626;
    --sidebar-item-active: #111b26;
    --sidebar-item-active-border: #40a9ff;

    // 模态框
    --modal-bg: #1f1f1f;
    --modal-header-border: #303030;
    --modal-footer-border: #303030;
}

// 高对比度主题
.theme-high-contrast {
    // 基础颜色
    --bg-primary: #ffffff;
    --bg-secondary: #ffffff;
    --bg-tertiary: #ffffff;

    // 文本颜色
    --text-primary: #000000;
    --text-secondary: #000000;
    --text-tertiary: #000000;
    --text-disabled: #666666;

    // 边框颜色
    --border-primary: #000000;
    --border-secondary: #000000;
    --border-hover: #0000ff;

    // 状态颜色
    --color-success: #008000;
    --color-success-bg: #ffffff;
    --color-success-border: #008000;

    --color-warning: #ff8c00;
    --color-warning-bg: #ffffff;
    --color-warning-border: #ff8c00;

    --color-error: #ff0000;
    --color-error-bg: #ffffff;
    --color-error-border: #ff0000;

    --color-info: #0000ff;
    --color-info-bg: #ffffff;
    --color-info-border: #0000ff;

    // 阴影（高对比度下移除阴影）
    --shadow-light: none;
    --shadow-medium: none;
    --shadow-heavy: none;

    // 悬停效果
    --hover-bg: #f0f0f0;
    --active-bg: #e0e0e0;

    // 表格
    --table-header-bg: #f0f0f0;
    --table-row-hover-bg: #f0f0f0;
    --table-border-color: #000000;

    // 侧边栏
    --sidebar-bg: #ffffff;
    --sidebar-border: #000000;
    --sidebar-item-hover: #f0f0f0;
    --sidebar-item-active: #e0e0e0;
    --sidebar-item-active-border: #0000ff;

    // 模态框
    --modal-bg: #ffffff;
    --modal-header-border: #000000;
    --modal-footer-border: #000000;
}

// 主题应用混合
.apply-theme() {
    background-color: var(--bg-primary);
    color: var(--text-primary);

    // 通用组件样式
    .ant-table {
        background-color: var(--bg-primary);

        .ant-table-thead>tr>th {
            background-color: var(--table-header-bg);
            color: var(--text-primary);
            border-bottom-color: var(--table-border-color);
        }

        .ant-table-tbody>tr {
            &:hover>td {
                background-color: var(--table-row-hover-bg);
            }

            td {
                border-bottom-color: var(--table-border-color);
                color: var(--text-primary);
            }
        }
    }

    .ant-modal {
        .ant-modal-content {
            background-color: var(--modal-bg);
        }

        .ant-modal-header {
            background-color: var(--modal-bg);
            border-bottom-color: var(--modal-header-border);

            .ant-modal-title {
                color: var(--text-primary);
            }
        }

        .ant-modal-body {
            background-color: var(--modal-bg);
            color: var(--text-primary);
        }

        .ant-modal-footer {
            background-color: var(--modal-bg);
            border-top-color: var(--modal-footer-border);
        }
    }

    .ant-input,
    .ant-select-selector {
        background-color: var(--bg-primary);
        border-color: var(--border-secondary);
        color: var(--text-primary);

        &:hover {
            border-color: var(--border-hover);
        }

        &:focus,
        &.ant-select-focused .ant-select-selector {
            border-color: var(--color-info);
            box-shadow: 0 0 0 2px var(--color-info-bg);
        }
    }

    .ant-btn {
        &.ant-btn-default {
            background-color: var(--bg-primary);
            border-color: var(--border-secondary);
            color: var(--text-primary);

            &:hover {
                border-color: var(--color-info);
                color: var(--color-info);
            }
        }

        &.ant-btn-primary {
            background-color: var(--color-info);
            border-color: var(--color-info);

            &:hover {
                background-color: var(--color-info);
                border-color: var(--color-info);
                opacity: 0.8;
            }
        }
    }

    .ant-card {
        background-color: var(--bg-primary);
        border-color: var(--border-primary);

        .ant-card-head {
            background-color: var(--bg-primary);
            border-bottom-color: var(--border-primary);

            .ant-card-head-title {
                color: var(--text-primary);
            }
        }

        .ant-card-body {
            background-color: var(--bg-primary);
            color: var(--text-primary);
        }
    }

    .ant-menu {
        background-color: var(--sidebar-bg);
        border-color: var(--sidebar-border);

        .ant-menu-item {
            color: var(--text-primary);

            &:hover {
                background-color: var(--sidebar-item-hover);
                color: var(--color-info);
            }

            &.ant-menu-item-selected {
                background-color: var(--sidebar-item-active);
                color: var(--color-info);
                border-right-color: var(--sidebar-item-active-border);
            }
        }
    }
}

// 主题切换动画
.theme-transition() {
    transition: background-color 0.3s ease,
        color 0.3s ease,
        border-color 0.3s ease,
        box-shadow 0.3s ease;
}

// 响应式主题
@media (prefers-color-scheme: dark) {
    .theme-auto {
        .apply-theme();
        .theme-dark();
    }
}

@media (prefers-color-scheme: light) {
    .theme-auto {
        .apply-theme();
        .theme-light();
    }
}

@media (prefers-contrast: high) {

    .theme-auto,
    .theme-light,
    .theme-dark {
        .theme-high-contrast();
    }
}

// 主题类应用
.systemManagement {
    &.theme-light {
        .theme-light();
        .apply-theme();
        .theme-transition();
    }

    &.theme-dark {
        .theme-dark();
        .apply-theme();
        .theme-transition();
    }

    &.theme-high-contrast {
        .theme-high-contrast();
        .apply-theme();
    }

    &.theme-auto {
        .apply-theme();
        .theme-transition();

        @media (prefers-color-scheme: dark) {
            .theme-dark();
        }

        @media (prefers-color-scheme: light) {
            .theme-light();
        }
    }
}