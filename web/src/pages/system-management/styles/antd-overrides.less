// Ant Design 组件样式覆盖
// 用于修复和优化 Ant Design 组件在系统管理模块中的显示效果

// 全局表格加载动画居中修复
.ant-table-wrapper {
    .ant-spin-nested-loading {
        // 表格加载时的遮罩层
        > .ant-spin {
            max-height: unset !important;
            
            .ant-spin-container {
                position: relative;
                
                &::after {
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: rgba(255, 255, 255, 0.9);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    content: '';
                    z-index: 1;
                }
            }
            
            .ant-spin-dot {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                z-index: 2;
            }
            
            .ant-spin-text {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, calc(-50% + 30px));
                z-index: 2;
                white-space: nowrap;
            }
        }

        // 表格为空时的加载动画
        .ant-table-placeholder {
            .ant-spin {
                display: flex;
                justify-content: center;
                align-items: center;
                min-height: 200px;
            }
        }
    }
}

// 全局 Spin 组件居中
.ant-spin-container {
    &.ant-spin-blur {
        > .ant-spin {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
    }
}

// Modal 中的加载动画居中
.ant-modal-content {
    .ant-spin-nested-loading {
        > .ant-spin {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
    }
}

// Card 中的加载动画居中
.ant-card {
    .ant-spin-nested-loading {
        > .ant-spin {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
    }
}

// 暗色主题适配
.theme-dark {
    .ant-table-wrapper {
        .ant-spin-nested-loading {
            > .ant-spin {
                .ant-spin-container {
                    &::after {
                        background: rgba(0, 0, 0, 0.8);
                    }
                }
            }
        }
    }
}