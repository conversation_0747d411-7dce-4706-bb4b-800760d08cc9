// 系统管理页面响应式设计工具

// 断点定义
@breakpoint-xs: 480px;
@breakpoint-sm: 768px;
@breakpoint-md: 1024px;
@breakpoint-lg: 1200px;
@breakpoint-xl: 1600px;

// 响应式混合
.responsive-xs(@rules) {
    @media (max-width: @breakpoint-xs) {
        @rules();
    }
}

.responsive-sm(@rules) {
    @media (max-width: @breakpoint-sm) {
        @rules();
    }
}

.responsive-md(@rules) {
    @media (max-width: @breakpoint-md) {
        @rules();
    }
}

.responsive-lg(@rules) {
    @media (max-width: @breakpoint-lg) {
        @rules();
    }
}

.responsive-xl(@rules) {
    @media (max-width: @breakpoint-xl) {
        @rules();
    }
}

// 响应式容器
.container-responsive() {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;

    .responsive-lg({
        padding: 0 16px;
    });

.responsive-sm({
    padding: 0 12px;
});
}

// 响应式网格
.grid-responsive(@columns: 12, @gap: 16px) {
    display: grid;
    grid-template-columns: repeat(@columns, 1fr);
    gap: @gap;

    .responsive-lg({
        grid-template-columns: repeat((@columns * 2 / 3), 1fr);
        gap: (@gap * 0.75);
    });

.responsive-md({
    grid-template-columns: repeat((@columns / 2), 1fr);
    gap: (@gap * 0.5);
});

.responsive-sm({
    grid-template-columns: repeat(2, 1fr);
    gap: (@gap * 0.5);
});

.responsive-xs({
    grid-template-columns: 1fr;
    gap: (@gap * 0.25);
});
}

// 响应式Flex布局
.flex-responsive() {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;

    .responsive-sm({
        flex-direction: column;
        gap: 12px;
    });
}

// 响应式表格
.table-responsive() {
    .ant-table-wrapper {
        overflow-x: auto;

        .ant-table {
            min-width: 800px;

            .responsive-lg({
                min-width: 700px;
            });

        .responsive-md({
            min-width: 600px;
        });

    .responsive-sm({
        min-width: 500px;

        .ant-table-thead > tr > th,
        .ant-table-tbody > tr > td {
            padding: 8px 12px;
            font-size: 13px;
        }
    });

.responsive-xs({
    min-width: 400px;

    .ant-table-thead > tr > th,
    .ant-table-tbody > tr > td {
        padding: 6px 8px;
        font-size: 12px;
    }
});
}
}
}

// 响应式侧边栏
.sidebar-responsive() {
    width: 240px;
    min-width: 240px;
    transition: all 0.3s ease;

    .responsive-lg({
        width: 200px;
        min-width: 200px;
    });

.responsive-sm({
    width: 100%;
    min-width: auto;
    border-right: none;
    border-bottom: 1px solid var(--border-primary);

    .ant-menu {
        height: auto;

        .ant-menu-item {
            height: 40px;
            line-height: 40px;
            padding: 0 16px;

            &.ant-menu-item-selected {
                border-right: none;
                border-bottom: 3px solid var(--color-info);
            }
        }
    }
});

.responsive-xs({
    .ant-menu {
        .ant-menu-item {
            padding: 0 12px;
            font-size: 13px;

            .anticon {
                margin-right: 8px;
                font-size: 14px;
            }
        }
    }
});
}

// 响应式内容区域
.content-responsive() {
    flex: 1;
    padding: 24px;
    overflow: auto;

    .responsive-lg({
        padding: 16px;
    });

.responsive-sm({
    padding: 12px;
});

.responsive-xs({
    padding: 8px;
});
}

// 响应式页面标题
.page-title-responsive() {
    padding: 24px 24px 0;
    margin-bottom: 24px;

    h1 {
        font-size: 24px;
        font-weight: 600;
        margin: 0 0 8px 0;

        .responsive-lg({
            font-size: 22px;
        });

    .responsive-sm({
        font-size: 20px;
    });

.responsive-xs({
    font-size: 18px;
});
}

.description {
    font-size: 14px;
    margin: 0 0 24px 0;

    .responsive-sm({
        font-size: 13px;
        margin-bottom: 16px;
    });

.responsive-xs({
    font-size: 12px;
    margin-bottom: 12px;
});
}

.responsive-lg({
    padding: 16px 16px 0;
    margin-bottom: 16px;
});

.responsive-sm({
    padding: 12px 12px 0;
    margin-bottom: 12px;
});
}

// 响应式表格头部
.table-header-responsive() {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    flex-wrap: wrap;
    gap: 16px;

    .searchContainer {
        display: flex;
        gap: 12px;
        align-items: center;
        flex-wrap: wrap;

        .ant-input-search {
            width: 280px;
            min-width: 200px;
        }

        .ant-select {
            min-width: 120px;
        }
    }

    .actionContainer {
        display: flex;
        gap: 8px;
        align-items: center;
    }

    .responsive-lg({
        flex-direction: column;
        align-items: stretch;
        gap: 12px;

        .searchContainer {
            justify-content: flex-start;

            .ant-input-search {
                width: 100%;
                max-width: 300px;
            }
        }

        .actionContainer {
            justify-content: flex-end;
        }
    });

.responsive-sm({
    .searchContainer {
        .ant-input-search {
            width: 100%;
        }

        .ant-select {
            width: 100%;
        }
    }

    .actionContainer {
        width: 100%;
        justify-content: stretch;

        .ant-btn {
            flex: 1;
        }
    }
});
}

// 响应式统计卡片
.stats-responsive() {
    display: flex;
    gap: 16px;
    margin-bottom: 24px;
    flex-wrap: wrap;

    .statCard {
        flex: 1;
        min-width: 200px;
        padding: 16px;
        text-align: center;

        .statNumber {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .statLabel {
            font-size: 14px;
        }
    }

    .responsive-lg({
        .statCard {
            min-width: 150px;
        }
    });

.responsive-sm({
    .statCard {
        min-width: 120px;
        padding: 12px;

        .statNumber {
            font-size: 20px;
        }

        .statLabel {
            font-size: 12px;
        }
    }
});

.responsive-xs({
    .statCard {
        min-width: 100px;
        padding: 8px;

        .statNumber {
            font-size: 18px;
        }

        .statLabel {
            font-size: 11px;
        }
    }
});
}

// 响应式模态框
.modal-responsive() {
    .ant-modal {
        .responsive-sm({
            max-width: 90vw;
            margin: 20px auto;
        });

    .responsive-xs({
        max-width: 95vw;
        margin: 10px auto;
    });

.ant-modal-body {
    .responsive-sm({
        padding: 16px;
    });

.responsive-xs({
    padding: 12px;
});
}
}
}

// 响应式操作按钮
.action-buttons-responsive() {
    display: flex;
    gap: 8px;
    align-items: center;

    .ant-btn {
        padding: 4px 12px;
        height: auto;
        font-size: 12px;
        border-radius: 4px;
        display: flex;
        align-items: center;
        gap: 4px;

        .anticon {
            font-size: 12px;
        }
    }

    .responsive-sm({
        flex-direction: column;
        gap: 4px;

        .ant-btn {
            width: 100%;
            justify-content: center;
        }
    });

.responsive-xs({
    .ant-btn {
        padding: 2px 8px;
        font-size: 11px;

        .anticon {
            font-size: 10px;
        }
    }
});
}

// 响应式布局主容器
.layout-responsive() {
    height: 100%;
    display: flex;
    flex-direction: row;
    overflow: hidden;

    .responsive-sm({
        flex-direction: column;
    });
}

// 响应式工具类
.responsive-utils {

    // 显示/隐藏工具类
    .show-xs {
        display: none;

        .responsive-xs({
            display: block;
        });
}

.show-sm {
    display: none;

    .responsive-sm({
        display: block;
    });
}

.show-md {
    display: none;

    .responsive-md({
        display: block;
    });
}

.show-lg {
    display: none;

    .responsive-lg({
        display: block;
    });
}

.hide-xs {
    .responsive-xs({
        display: none;
    });
}

.hide-sm {
    .responsive-sm({
        display: none;
    });
}

.hide-md {
    .responsive-md({
        display: none;
    });
}

.hide-lg {
    .responsive-lg({
        display: none;
    });
}

// 文本大小响应式
.text-responsive {
    font-size: 16px;

    .responsive-lg({
        font-size: 15px;
    });

.responsive-sm({
    font-size: 14px;
});

.responsive-xs({
    font-size: 13px;
});
}

// 间距响应式
.spacing-responsive {
    padding: 24px;

    .responsive-lg({
        padding: 16px;
    });

.responsive-sm({
    padding: 12px;
});

.responsive-xs({
    padding: 8px;
});
}

// 边距响应式
.margin-responsive {
    margin: 24px;

    .responsive-lg({
        margin: 16px;
    });

.responsive-sm({
    margin: 12px;
});

.responsive-xs({
    margin: 8px;
});
}
}