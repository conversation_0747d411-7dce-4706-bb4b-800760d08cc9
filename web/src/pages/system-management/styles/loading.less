// 系统管理页面加载状态样式

// 加载容器
.loadingContainer {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    min-height: 400px;
    background: var(--bg-primary);
    border-radius: 8px;

    .loadingSpinner {
        .ant-spin-dot {
            font-size: 24px;
        }

        .ant-spin-text {
            margin-top: 16px;
            color: var(--text-secondary);
            font-size: 14px;
        }
    }

    .loadingMessage {
        margin-top: 12px;
        color: var(--text-tertiary);
        font-size: 13px;
        text-align: center;
        max-width: 300px;
        line-height: 1.5;
    }
}

// 表格加载骨架屏
.tableLoadingSkeleton {
    .skeletonRow {
        display: flex;
        align-items: center;
        padding: 12px 16px;
        border-bottom: 1px solid var(--border-primary);

        .skeletonCell {
            height: 16px;
            background: var(--bg-secondary);
            border-radius: 4px;
            margin-right: 16px;
            animation: skeleton-loading 1.5s infinite;

            &.avatar {
                width: 32px;
                height: 32px;
                border-radius: 50%;
            }

            &.short {
                width: 80px;
            }

            &.medium {
                width: 120px;
            }

            &.long {
                width: 200px;
            }

            &.full {
                flex: 1;
            }
        }
    }

    @keyframes skeleton-loading {
        0% {
            opacity: 1;
        }

        50% {
            opacity: 0.4;
        }

        100% {
            opacity: 1;
        }
    }
}

// 卡片加载骨架屏
.cardLoadingSkeleton {
    padding: 24px;
    background: var(--bg-primary);
    border: 1px solid var(--border-primary);
    border-radius: 8px;

    .skeletonTitle {
        height: 20px;
        width: 60%;
        background: var(--bg-secondary);
        border-radius: 4px;
        margin-bottom: 16px;
        animation: skeleton-loading 1.5s infinite;
    }

    .skeletonContent {
        .skeletonLine {
            height: 14px;
            background: var(--bg-secondary);
            border-radius: 4px;
            margin-bottom: 8px;
            animation: skeleton-loading 1.5s infinite;

            &:nth-child(1) {
                width: 100%;
            }

            &:nth-child(2) {
                width: 85%;
            }

            &:nth-child(3) {
                width: 70%;
            }

            &:nth-child(4) {
                width: 90%;
            }
        }
    }

    .skeletonActions {
        display: flex;
        gap: 8px;
        margin-top: 16px;

        .skeletonButton {
            height: 32px;
            width: 80px;
            background: var(--bg-secondary);
            border-radius: 4px;
            animation: skeleton-loading 1.5s infinite;
        }
    }
}

// 页面级加载遮罩
.pageLoadingOverlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(2px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    animation: fadeIn 0.3s ease-in-out;

    .overlayContent {
        background: var(--bg-primary);
        padding: 32px;
        border-radius: 12px;
        box-shadow: var(--shadow-heavy);
        text-align: center;
        min-width: 200px;

        .overlaySpinner {
            .ant-spin-dot {
                font-size: 32px;
            }
        }

        .overlayText {
            margin-top: 16px;
            color: var(--text-primary);
            font-size: 16px;
            font-weight: 500;
        }

        .overlaySubtext {
            margin-top: 8px;
            color: var(--text-secondary);
            font-size: 14px;
        }
    }

    &.dark {
        background: rgba(0, 0, 0, 0.6);
    }
}

// 按钮加载状态
.buttonLoading {
    .ant-btn {
        position: relative;
        pointer-events: none;

        &::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 16px;
            height: 16px;
            margin: -8px 0 0 -8px;
            border: 2px solid transparent;
            border-top-color: currentColor;
            border-radius: 50%;
            animation: button-spin 1s linear infinite;
        }

        .ant-btn-loading-icon {
            display: none;
        }
    }

    @keyframes button-spin {
        0% {
            transform: rotate(0deg);
        }

        100% {
            transform: rotate(360deg);
        }
    }
}

// 内联加载指示器
.inlineLoading {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    color: var(--text-secondary);
    font-size: 13px;

    .loadingDots {
        display: inline-flex;
        gap: 2px;

        .dot {
            width: 4px;
            height: 4px;
            background: currentColor;
            border-radius: 50%;
            animation: dot-bounce 1.4s infinite ease-in-out;

            &:nth-child(1) {
                animation-delay: -0.32s;
            }

            &:nth-child(2) {
                animation-delay: -0.16s;
            }

            &:nth-child(3) {
                animation-delay: 0s;
            }
        }
    }

    @keyframes dot-bounce {

        0%,
        80%,
        100% {
            transform: scale(0.8);
            opacity: 0.5;
        }

        40% {
            transform: scale(1);
            opacity: 1;
        }
    }
}

// 进度条加载
.progressLoading {
    .progressBar {
        width: 100%;
        height: 4px;
        background: var(--bg-secondary);
        border-radius: 2px;
        overflow: hidden;
        position: relative;

        .progressFill {
            height: 100%;
            background: var(--color-info);
            border-radius: 2px;
            transition: width 0.3s ease;
        }

        .progressIndeterminate {
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            width: 30%;
            background: var(--color-info);
            border-radius: 2px;
            animation: progress-indeterminate 2s infinite;
        }
    }

    @keyframes progress-indeterminate {
        0% {
            left: -30%;
        }

        100% {
            left: 100%;
        }
    }

    .progressText {
        margin-top: 8px;
        text-align: center;
        color: var(--text-secondary);
        font-size: 12px;
    }
}

// 响应式加载状态
@media (max-width: 768px) {
    .loadingContainer {
        min-height: 300px;
        padding: 20px;

        .loadingSpinner {
            .ant-spin-dot {
                font-size: 20px;
            }
        }
    }

    .pageLoadingOverlay {
        .overlayContent {
            padding: 24px;
            margin: 20px;

            .overlaySpinner {
                .ant-spin-dot {
                    font-size: 24px;
                }
            }

            .overlayText {
                font-size: 14px;
            }
        }
    }

    .cardLoadingSkeleton {
        padding: 16px;
    }
}

@media (max-width: 480px) {
    .loadingContainer {
        min-height: 250px;
        padding: 16px;
    }

    .pageLoadingOverlay {
        .overlayContent {
            padding: 20px;
            margin: 16px;
        }
    }
}

// 暗色主题适配
.theme-dark {
    .pageLoadingOverlay {
        background: rgba(0, 0, 0, 0.8);

        .overlayContent {
            background: var(--bg-primary);
            color: var(--text-primary);
        }
    }

    .tableLoadingSkeleton,
    .cardLoadingSkeleton {

        .skeletonCell,
        .skeletonTitle,
        .skeletonLine,
        .skeletonButton {
            background: #2a2a2a;
        }
    }
}

// 高对比度模式
@media (prefers-contrast: high) {

    .loadingContainer,
    .cardLoadingSkeleton {
        border: 2px solid var(--border-primary);
    }

    .progressLoading {
        .progressBar {
            border: 1px solid var(--border-primary);
        }
    }
}

// 减少动画模式
@media (prefers-reduced-motion: reduce) {

    .tableLoadingSkeleton,
    .cardLoadingSkeleton,
    .buttonLoading,
    .inlineLoading,
    .progressLoading {
        * {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
        }
    }
}