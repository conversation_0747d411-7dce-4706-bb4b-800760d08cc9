// 系统管理页面样式混合和工具类

// 响应式断点
@screen-xs: 480px;
@screen-sm: 768px;
@screen-md: 1024px;
@screen-lg: 1200px;
@screen-xl: 1600px;

// 颜色变量
@success-color: #52c41a;
@warning-color: #faad14;
@error-color: #ff4d4f;
@info-color: #1890ff;
@text-color: #262626;
@text-color-secondary: #595959;
@text-color-disabled: #8c8c8c;
@border-color: #f0f0f0;
@background-color: #fafafa;

// 阴影
@box-shadow-light: 0 2px 8px rgba(0, 0, 0, 0.06);
@box-shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.1);
@box-shadow-heavy: 0 8px 24px rgba(0, 0, 0, 0.15);

// 圆角
@border-radius-sm: 4px;
@border-radius-base: 6px;
@border-radius-lg: 8px;

// 间距
@spacing-xs: 4px;
@spacing-sm: 8px;
@spacing-base: 12px;
@spacing-md: 16px;
@spacing-lg: 24px;
@spacing-xl: 32px;

// 字体大小
@font-size-xs: 11px;
@font-size-sm: 12px;
@font-size-base: 14px;
@font-size-md: 16px;
@font-size-lg: 18px;
@font-size-xl: 20px;
@font-size-xxl: 24px;

// 字体权重
@font-weight-normal: 400;
@font-weight-medium: 500;
@font-weight-semibold: 600;
@font-weight-bold: 700;

// 混合样式

// 卡片样式
.card-style() {
    background: #fff;
    border: 1px solid @border-color;
    border-radius: @border-radius-lg;
    box-shadow: @box-shadow-light;
    transition: all 0.3s ease;

    &:hover {
        box-shadow: @box-shadow-medium;
    }
}

// 按钮样式
.button-style(@color: @info-color) {
    border-radius: @border-radius-base;
    font-weight: @font-weight-medium;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: @spacing-xs;

    &:hover {
        background: fade(@color, 10%);
        border-color: @color;
        color: @color;
    }

    &:focus {
        box-shadow: 0 0 0 2px fade(@color, 20%);
    }
}

// 状态标签样式
.status-tag(@color: @info-color, @bg-color: fade(@info-color, 10%)) {
    font-size: @font-size-sm;
    padding: 2px 8px;
    border-radius: 10px;
    font-weight: @font-weight-medium;
    border: 1px solid fade(@color, 30%);
    color: @color;
    background: @bg-color;
    display: inline-block;
}

// 表格行悬停效果
.table-row-hover() {
    transition: background-color 0.2s ease;

    &:hover {
        background-color: #f5f5f5;
    }
}

// 响应式混合
.responsive(@screen) when (@screen =xs) {
    @media (max-width: @screen-xs) {
        @content();
    }
}

.responsive(@screen) when (@screen =sm) {
    @media (max-width: @screen-sm) {
        @content();
    }
}

.responsive(@screen) when (@screen =md) {
    @media (max-width: @screen-md) {
        @content();
    }
}

.responsive(@screen) when (@screen =lg) {
    @media (max-width: @screen-lg) {
        @content();
    }
}

// 文本截断
.text-ellipsis() {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.text-ellipsis-multiline(@lines: 2) {
    display: -webkit-box;
    -webkit-line-clamp: @lines;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

// Flexbox 工具
.flex-center() {
    display: flex;
    align-items: center;
    justify-content: center;
}

.flex-between() {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.flex-start() {
    display: flex;
    align-items: center;
    justify-content: flex-start;
}

.flex-end() {
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

// 加载动画
.loading-animation() {
    @keyframes loading {
        0% {
            transform: rotate(0deg);
        }

        100% {
            transform: rotate(360deg);
        }
    }

    animation: loading 1s linear infinite;
}

// 淡入动画
.fade-in-animation(@duration: 0.3s) {
    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(10px);
        }

        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    animation: fadeIn @duration ease-in-out;
}

// 滑入动画
.slide-in-animation(@duration: 0.3s, @direction: left) {
    @keyframes slideInLeft {
        from {
            transform: translateX(-20px);
            opacity: 0;
        }

        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    @keyframes slideInRight {
        from {
            transform: translateX(20px);
            opacity: 0;
        }

        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    & when (@direction = left) {
        animation: slideInLeft @duration ease-out;
    }
    
    & when (@direction = right) {
        animation: slideInRight @duration ease-out;
    }
}

// 暗色主题混合
.dark-theme() {
    background: #141414;
    color: #d9d9d9;

    .card-style-dark() {
        background: #1f1f1f;
        border-color: #303030;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }

    .text-color-dark() {
        color: #d9d9d9;
    }

    .text-color-secondary-dark() {
        color: #8c8c8c;
    }

    .border-color-dark() {
        border-color: #303030;
    }

    .background-color-dark() {
        background-color: #262626;
    }
}

// 高对比度模式
.high-contrast() {
    @media (prefers-contrast: high) {
        border-width: 2px;
        border-color: #000;
        color: #000;
        background: #fff;
    }
}

// 减少动画模式
.reduce-motion() {
    @media (prefers-reduced-motion: reduce) {
        * {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
        }
    }
}

// 打印样式
.print-styles() {
    @media print {
        background: #fff !important;
        color: #000 !important;
        box-shadow: none !important;
        border-radius: 0 !important;

        .no-print {
            display: none !important;
        }

        .print-only {
            display: block !important;
        }
    }
}

// 工具类
.utils {

    // 间距工具类
    .margin-top-xs {
        margin-top: @spacing-xs;
    }

    .margin-top-sm {
        margin-top: @spacing-sm;
    }

    .margin-top-base {
        margin-top: @spacing-base;
    }

    .margin-top-md {
        margin-top: @spacing-md;
    }

    .margin-top-lg {
        margin-top: @spacing-lg;
    }

    .margin-top-xl {
        margin-top: @spacing-xl;
    }

    .margin-bottom-xs {
        margin-bottom: @spacing-xs;
    }

    .margin-bottom-sm {
        margin-bottom: @spacing-sm;
    }

    .margin-bottom-base {
        margin-bottom: @spacing-base;
    }

    .margin-bottom-md {
        margin-bottom: @spacing-md;
    }

    .margin-bottom-lg {
        margin-bottom: @spacing-lg;
    }

    .margin-bottom-xl {
        margin-bottom: @spacing-xl;
    }

    .padding-xs {
        padding: @spacing-xs;
    }

    .padding-sm {
        padding: @spacing-sm;
    }

    .padding-base {
        padding: @spacing-base;
    }

    .padding-md {
        padding: @spacing-md;
    }

    .padding-lg {
        padding: @spacing-lg;
    }

    .padding-xl {
        padding: @spacing-xl;
    }

    // 文本工具类
    .text-center {
        text-align: center;
    }

    .text-left {
        text-align: left;
    }

    .text-right {
        text-align: right;
    }

    .text-bold {
        font-weight: @font-weight-bold;
    }

    .text-semibold {
        font-weight: @font-weight-semibold;
    }

    .text-medium {
        font-weight: @font-weight-medium;
    }

    .text-normal {
        font-weight: @font-weight-normal;
    }

    .text-xs {
        font-size: @font-size-xs;
    }

    .text-sm {
        font-size: @font-size-sm;
    }

    .text-base {
        font-size: @font-size-base;
    }

    .text-md {
        font-size: @font-size-md;
    }

    .text-lg {
        font-size: @font-size-lg;
    }

    .text-xl {
        font-size: @font-size-xl;
    }

    .text-xxl {
        font-size: @font-size-xxl;
    }

    // 颜色工具类
    .text-primary {
        color: @info-color;
    }

    .text-success {
        color: @success-color;
    }

    .text-warning {
        color: @warning-color;
    }

    .text-error {
        color: @error-color;
    }

    .text-secondary {
        color: @text-color-secondary;
    }

    .text-disabled {
        color: @text-color-disabled;
    }

    // 显示工具类
    .hidden {
        display: none;
    }

    .visible {
        display: block;
    }

    .flex {
        display: flex;
    }

    .inline-flex {
        display: inline-flex;
    }

    // 位置工具类
    .relative {
        position: relative;
    }

    .absolute {
        position: absolute;
    }

    .fixed {
        position: fixed;
    }

    .sticky {
        position: sticky;
    }

    // 溢出工具类
    .overflow-hidden {
        overflow: hidden;
    }

    .overflow-auto {
        overflow: auto;
    }

    .overflow-scroll {
        overflow: scroll;
    }

    // 圆角工具类
    .rounded-sm {
        border-radius: @border-radius-sm;
    }

    .rounded {
        border-radius: @border-radius-base;
    }

    .rounded-lg {
        border-radius: @border-radius-lg;
    }

    .rounded-full {
        border-radius: 50%;
    }

    // 阴影工具类
    .shadow-light {
        box-shadow: @box-shadow-light;
    }

    .shadow-medium {
        box-shadow: @box-shadow-medium;
    }

    .shadow-heavy {
        box-shadow: @box-shadow-heavy;
    }

    .shadow-none {
        box-shadow: none;
    }
}