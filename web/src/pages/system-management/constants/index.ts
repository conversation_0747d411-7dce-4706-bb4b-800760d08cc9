export const DEFAULT_PAGE_SIZE = 20;

export const USER_STATUS_OPTIONS = [
    { label: '全部', value: 'all' },
    { label: '激活', value: '1' },
    { label: '禁用', value: '0' },
];

export const KNOWLEDGEBASE_STATUS_OPTIONS = [
    { label: '全部', value: 'all' },
    { label: '激活', value: '1' },
    { label: '禁用', value: '0' },
];

export const SYSTEM_MANAGEMENT_TABS = {
    USERS: 'users' as const,
    KNOWLEDGEBASES: 'knowledgebases' as const,
};

export const API_ENDPOINTS = {
    USERS: '/api/v1/system/users',
    KNOWLEDGEBASES: '/api/v1/system/knowledgebases',
} as const;

// 状态映射
export const USER_STATUS_MAP = {
    '1': { text: '激活', color: 'success' },
    '0': { text: '禁用', color: 'error' },
} as const;

export const KNOWLEDGEBASE_STATUS_MAP = {
    '1': { text: '激活', color: 'success' }, 
    '0': { text: '禁用', color: 'error' },
} as const;

// 表格配置
export const TABLE_CONFIG = {
    pageSize: DEFAULT_PAGE_SIZE,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total: number, range: [number, number]) => 
        `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
    PAGE_SIZE_OPTIONS: [10, 20, 50, 100],
    ROW_KEY: 'id',
    SCROLL_Y: 400,
} as const;

// 确认消息
export const CONFIRM_MESSAGES = {
    DELETE_USER: '确定要删除该用户吗？',
    DELETE_KNOWLEDGEBASE: '确定要删除该知识库吗？',
    BATCH_DELETE: '确定要批量删除选中的项目吗？',
    DISABLE_USER: '确定要禁用该用户吗？',
    ENABLE_USER: '确定要启用该用户吗？',
    DISABLE_KNOWLEDGEBASE: '确定要禁用该知识库吗？',
    ENABLE_KNOWLEDGEBASE: '确定要启用该知识库吗？',
    WARNING: '此操作将会影响相关功能，请谨慎操作！',
} as const;

// 占位符文本
export const PLACEHOLDER_TEXTS = {
    SEARCH_USER: '请输入用户名或邮箱',
    SEARCH_USERS: '请输入用户名或邮箱',
    SEARCH_KNOWLEDGEBASE: '请输入知识库名称',
    SEARCH_KNOWLEDGEBASES: '请输入知识库名称',
    SELECT_STATUS: '选择状态',
    SELECT_CREATOR: '选择创建者',
    USERNAME: '请输入用户名',
    EMAIL: '请输入邮箱地址',
    PASSWORD: '请输入密码',
    KNOWLEDGEBASE_NAME: '请输入知识库名称',
} as const;

// 表单规则
export const FORM_RULES = {
    username: [
        { required: true, message: '请输入用户名' },
        { min: 2, max: 20, message: '用户名长度为2-20个字符' },
    ],
    email: [
        { required: true, message: '请输入邮箱地址' },
        { type: 'email' as const, message: '请输入有效的邮箱地址' },
    ],
    password: [
        { required: true, message: '请输入密码' },
        { min: 6, max: 20, message: '密码长度为6-20个字符' },
    ],
    knowledgebase_name: [
        { required: true, message: '请输入知识库名称' },
        { min: 1, max: 50, message: '知识库名称长度为1-50个字符' },
    ],
    NICKNAME: [
        { max: 32, message: '昵称长度不能超过32个字符' },
    ],
    EMAIL: [
        { required: true, message: '请输入邮箱地址' },
        { type: 'email' as const, message: '请输入有效的邮箱地址' },
        { max: 255, message: '邮箱长度不能超过255个字符' },
    ],
} as const;

// 模态框配置
export const MODAL_CONFIG = {
    width: 600,
    maskClosable: false,
    destroyOnClose: true,
    WIDTH: {
        SMALL: 480,
        MEDIUM: 600,
        LARGE: 800,
        EXTRA_LARGE: 1000,
    },
    MASK_CLOSABLE: false,
    DESTROY_ON_CLOSE: true,
} as const;

// 表单配置
export const FORM_CONFIG = {
    labelCol: { span: 6 },
    wrapperCol: { span: 18 },
    autoComplete: 'off',
    LAYOUT: 'horizontal',
    LABEL_COL: { span: 6 },
    WRAPPER_COL: { span: 18 },
    VALIDATE_TRIGGER: 'onBlur',
} as const;

// 搜索防抖延迟
export const SEARCH_DEBOUNCE_DELAY = 300;

// 成功消息
export const SUCCESS_MESSAGES = {
    CREATE_USER: '用户创建成功',
    UPDATE_USER: '用户更新成功', 
    DELETE_USER: '用户删除成功',
    USER_STATUS_UPDATED: '用户状态更新成功',
    CREATE_KNOWLEDGEBASE: '知识库创建成功',
    UPDATE_KNOWLEDGEBASE: '知识库更新成功',
    DELETE_KNOWLEDGEBASE: '知识库删除成功',
    KNOWLEDGEBASE_STATUS_UPDATED: '知识库状态更新成功',
    BATCH_DELETE: '批量删除成功',
} as const;

// 存储键
export const STORAGE_KEYS = {
    USER_MANAGEMENT_FILTERS: 'system_management_user_management_filters',
    USER_LIST_FILTER: 'system_management_user_list_filter',
    KNOWLEDGEBASE_MANAGEMENT_FILTERS: 'system_management_knowledgebase_management_filters',
    KNOWLEDGEBASE_LIST_FILTER: 'system_management_knowledgebase_list_filter',
    SYSTEM_MANAGEMENT_TAB: 'system_management_tab',
    TABLE_PAGE_SIZE: 'system_management_table_page_size',
} as const;