import { IUserInfo } from '@/interfaces/database/user-setting';

/**
 * 权限检查工具函数
 */

/**
 * 检查用户是否为超级用户
 */
export const checkIsSuperuser = (userInfo: IUserInfo | null | undefined): boolean => {
    return userInfo?.is_superuser === true;
};

/**
 * 检查用户是否有系统管理权限
 */
export const checkSystemManagementPermission = (userInfo: IUserInfo | null | undefined): boolean => {
    return checkIsSuperuser(userInfo);
};

/**
 * 获取权限错误消息
 */
export const getPermissionErrorMessage = (userInfo: IUserInfo | null | undefined): string => {
    if (!userInfo) {
        return '用户信息获取失败，请重新登录';
    }

    if (!checkIsSuperuser(userInfo)) {
        return '权限不足，需要超级用户权限才能访问系统管理功能';
    }

    return '';
};

/**
 * 权限检查结果类型
 */
export interface PermissionCheckResult {
    hasPermission: boolean;
    errorMessage: string;
}

/**
 * 综合权限检查
 */
export const checkPermission = (userInfo: IUserInfo | null | undefined): PermissionCheckResult => {
    const hasPermission = checkSystemManagementPermission(userInfo);
    const errorMessage = hasPermission ? '' : getPermissionErrorMessage(userInfo);

    return {
        hasPermission,
        errorMessage,
    };
};

/**
 * 权限装饰器函数（用于包装需要权限检查的函数）
 */
export const withPermissionCheck = <T extends (...args: any[]) => any>(
    fn: T,
    userInfo: IUserInfo | null | undefined
): T => {
    return ((...args: Parameters<T>) => {
        const { hasPermission, errorMessage } = checkPermission(userInfo);

        if (!hasPermission) {
            throw new Error(errorMessage);
        }

        return fn(...args);
    }) as T;
};

/**
 * 异步权限装饰器函数
 */
export const withAsyncPermissionCheck = <T extends (...args: any[]) => Promise<any>>(
    fn: T,
    userInfo: IUserInfo | null | undefined
): T => {
    return (async (...args: Parameters<T>) => {
        const { hasPermission, errorMessage } = checkPermission(userInfo);

        if (!hasPermission) {
            throw new Error(errorMessage);
        }

        return await fn(...args);
    }) as T;
};

export default {
    checkIsSuperuser,
    checkSystemManagementPermission,
    getPermissionErrorMessage,
    checkPermission,
    withPermissionCheck,
    withAsyncPermissionCheck,
};