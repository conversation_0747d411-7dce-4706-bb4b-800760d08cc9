import { message, notification } from 'antd';
import { ErrorType } from '../hooks/use-error-handler';

// 网络错误类型
export enum NetworkErrorType {
    CONNECTION_ERROR = 'CONNECTION_ERROR',
    TIMEOUT_ERROR = 'TIMEOUT_ERROR',
    SERVER_ERROR = 'SERVER_ERROR',
    PERMISSION_ERROR = 'PERMISSION_ERROR',
    NOT_FOUND_ERROR = 'NOT_FOUND_ERROR',
    VALIDATION_ERROR = 'VALIDATION_ERROR',
}

// 网络错误信息
export interface NetworkError {
    type: NetworkErrorType;
    status?: number;
    message: string;
    details?: any;
    timestamp: number;
}

// 错误重试配置
export interface RetryConfig {
    maxRetries: number;
    retryDelay: number;
    retryCondition?: (error: any) => boolean;
}

/**
 * 解析网络错误
 */
export const parseNetworkError = (error: any): NetworkError => {
    const timestamp = Date.now();

    // 处理Axios响应错误
    if (error?.response) {
        const { status, data } = error.response;

        let type: NetworkErrorType;
        let message: string;

        switch (status) {
            case 400:
                type = NetworkErrorType.VALIDATION_ERROR;
                message = data?.message || data?.retmsg || '请求参数错误';
                break;
            case 401:
                type = NetworkErrorType.PERMISSION_ERROR;
                message = '登录已过期，请重新登录';
                break;
            case 403:
                type = NetworkErrorType.PERMISSION_ERROR;
                message = data?.message || data?.retmsg || '权限不足，请联系管理员';
                break;
            case 404:
                type = NetworkErrorType.NOT_FOUND_ERROR;
                message = data?.message || data?.retmsg || '请求的资源不存在';
                break;
            case 408:
                type = NetworkErrorType.TIMEOUT_ERROR;
                message = '请求超时，请稍后重试';
                break;
            case 422:
                type = NetworkErrorType.VALIDATION_ERROR;
                message = data?.message || data?.retmsg || '数据验证失败';
                break;
            case 429:
                type = NetworkErrorType.SERVER_ERROR;
                message = '请求过于频繁，请稍后重试';
                break;
            case 500:
            case 502:
            case 503:
                type = NetworkErrorType.SERVER_ERROR;
                message = data?.message || data?.retmsg || '服务器内部错误，请稍后重试';
                break;
            case 504:
                type = NetworkErrorType.TIMEOUT_ERROR;
                message = '服务器响应超时，请稍后重试';
                break;
            default:
                type = NetworkErrorType.SERVER_ERROR;
                message = data?.message || data?.retmsg || `服务器错误 (${status})`;
        }

        return {
            type,
            status,
            message,
            details: data,
            timestamp,
        };
    }

    // 处理网络连接错误
    if (error?.code === 'NETWORK_ERROR' || error?.message?.includes('Network Error')) {
        return {
            type: NetworkErrorType.CONNECTION_ERROR,
            message: '网络连接失败，请检查网络设置后重试',
            details: error,
            timestamp,
        };
    }

    // 处理超时错误
    if (error?.code === 'ECONNABORTED' || error?.message?.includes('timeout')) {
        return {
            type: NetworkErrorType.TIMEOUT_ERROR,
            message: '请求超时，请稍后重试',
            details: error,
            timestamp,
        };
    }

    // 默认服务器错误
    return {
        type: NetworkErrorType.SERVER_ERROR,
        message: error?.message || '网络请求失败，请稍后重试',
        details: error,
        timestamp,
    };
};

/**
 * 显示网络错误提示
 */
export const showNetworkError = (error: NetworkError, useNotification = false) => {
    const { type, message } = error;

    if (useNotification) {
        let notificationType: 'error' | 'warning' | 'info' = 'error';

        if (type === NetworkErrorType.PERMISSION_ERROR) {
            notificationType = 'warning';
        } else if (type === NetworkErrorType.VALIDATION_ERROR) {
            notificationType = 'info';
        }

        notification[notificationType]({
            message: '操作失败',
            description: message,
            duration: 4.5,
            placement: 'topRight',
        });
    } else {
        message.error(message);
    }
};

/**
 * 带重试机制的网络请求包装器
 */
export const withRetry = async <T>(
    requestFn: () => Promise<T>,
    config: RetryConfig = { maxRetries: 3, retryDelay: 1000 }
): Promise<T> => {
    const { maxRetries, retryDelay, retryCondition } = config;
    let lastError: any;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            return await requestFn();
        } catch (error) {
            lastError = error;
            const networkError = parseNetworkError(error);

            // 检查是否应该重试
            const shouldRetry = retryCondition
                ? retryCondition(error)
                : (
                    networkError.type === NetworkErrorType.CONNECTION_ERROR ||
                    networkError.type === NetworkErrorType.TIMEOUT_ERROR ||
                    (networkError.type === NetworkErrorType.SERVER_ERROR && networkError.status && networkError.status >= 500)
                );

            if (attempt < maxRetries && shouldRetry) {
                // 显示重试提示
                message.info(`网络请求失败，正在重试 (${attempt}/${maxRetries})...`, 2);

                // 等待后重试
                await new Promise(resolve => setTimeout(resolve, retryDelay * attempt));
                continue;
            }

            // 不重试或已达到最大重试次数
            throw error;
        }
    }

    throw lastError;
};

/**
 * 检查网络连接状态
 */
export const checkNetworkConnection = (): boolean => {
    if (!navigator.onLine) {
        message.warning('当前网络连接不可用，请检查网络设置');
        return false;
    }
    return true;
};

/**
 * 网络状态监听器
 */
export class NetworkStatusMonitor {
    private listeners: Array<(online: boolean) => void> = [];

    constructor() {
        this.init();
    }

    private init() {
        window.addEventListener('online', () => this.notifyListeners(true));
        window.addEventListener('offline', () => this.notifyListeners(false));
    }

    private notifyListeners(online: boolean) {
        this.listeners.forEach(listener => listener(online));
    }

    public addListener(listener: (online: boolean) => void) {
        this.listeners.push(listener);
    }

    public removeListener(listener: (online: boolean) => void) {
        const index = this.listeners.indexOf(listener);
        if (index > -1) {
            this.listeners.splice(index, 1);
        }
    }

    public isOnline(): boolean {
        return navigator.onLine;
    }

    public destroy() {
        window.removeEventListener('online', () => this.notifyListeners(true));
        window.removeEventListener('offline', () => this.notifyListeners(false));
        this.listeners = [];
    }
}

// 全局网络状态监听器实例
export const networkMonitor = new NetworkStatusMonitor();

/**
 * 处理权限错误的特殊逻辑
 */
export const handlePermissionError = (error: NetworkError) => {
    if (error.status === 401) {
        // 登录过期，重定向到登录页
        notification.warning({
            message: '登录已过期',
            description: '请重新登录后继续操作',
            duration: 3,
        });

        // 延迟跳转，让用户看到提示
        setTimeout(() => {
            window.location.href = '/login';
        }, 1500);
    } else if (error.status === 403) {
        // 权限不足
        notification.error({
            message: '权限不足',
            description: error.message,
            duration: 5,
        });
    }
};

/**
 * 创建带有网络错误处理的请求函数
 */
export const createNetworkRequest = <T extends (...args: any[]) => Promise<any>>(
    requestFn: T,
    options: {
        showError?: boolean;
        useNotification?: boolean;
        enableRetry?: boolean;
        retryConfig?: RetryConfig;
        onError?: (error: NetworkError) => void;
    } = {}
): T => {
    const {
        showError = true,
        useNotification = false,
        enableRetry = false,
        retryConfig,
        onError,
    } = options;

    return (async (...args: Parameters<T>) => {
        try {
            const actualRequest = enableRetry
                ? () => requestFn(...args)
                : requestFn;

            if (enableRetry) {
                return await withRetry(actualRequest, retryConfig);
            } else {
                return await actualRequest(...args);
            }
        } catch (error) {
            const networkError = parseNetworkError(error);

            // 调用自定义错误处理
            if (onError) {
                onError(networkError);
            }

            // 处理权限错误
            if (networkError.type === NetworkErrorType.PERMISSION_ERROR) {
                handlePermissionError(networkError);
            } else if (showError) {
                // 显示其他错误
                showNetworkError(networkError, useNotification);
            }

            throw error;
        }
    }) as T;
};

export default {
    parseNetworkError,
    showNetworkError,
    withRetry,
    checkNetworkConnection,
    networkMonitor,
    handlePermissionError,
    createNetworkRequest,
};