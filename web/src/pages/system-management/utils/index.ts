import { IUser, IKnowledgebaseForAdmin } from '../types';
import { USER_STATUS_MAP, KNOWLEDGEBASE_STATUS_MAP } from '../constants';

/**
 * 格式化时间戳为可读日期
 * 格式：dd/MM/yyyy HH:mm:ss
 * 注意：后端时间戳已经是毫秒单位，无需再次乘以1000
 */
export const formatTimestamp = (timestamp: number): string => {
    if (!timestamp) return '-';
    
    // 后端返回的时间戳已经是毫秒单位，直接使用
    const date = new Date(timestamp);
    
    // 获取各个时间组件
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0'); // getMonth() 返回 0-11
    const year = date.getFullYear();
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    const seconds = date.getSeconds().toString().padStart(2, '0');
    
    // 格式：dd/MM/yyyy HH:mm:ss
    return `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;
};

/**
 * 获取用户状态显示信息
 */
export const getUserStatusInfo = (status: '1' | '0') => {
    return USER_STATUS_MAP[status] || { text: '未知', color: 'default' as const };
};

/**
 * 获取知识库状态显示信息
 */
export const getKnowledgebaseStatusInfo = (status: '1' | '0') => {
    return KNOWLEDGEBASE_STATUS_MAP[status] || { text: '未知', color: 'default' as const };
};

/**
 * 检查用户是否为超级用户
 */
export const isSuperuser = (user: IUser | null): boolean => {
    return user?.is_superuser === true;
};

/**
 * 格式化数字显示（添加千分位分隔符）
 */
export const formatNumber = (num: number): string => {
    if (typeof num !== 'number' || isNaN(num)) return '0';
    return num.toLocaleString('zh-CN');
};

/**
 * 生成用户显示名称
 */
export const getUserDisplayName = (user: IUser): string => {
    return user.nickname || user.email || user.id;
};

/**
 * 生成知识库显示名称
 */
export const getKnowledgebaseDisplayName = (kb: IKnowledgebaseForAdmin): string => {
    return kb.name || kb.id;
};

/**
 * 检查字符串是否为空
 */
export const isEmpty = (str: string | null | undefined): boolean => {
    return !str || str.trim().length === 0;
};

/**
 * 防抖函数
 */
export const debounce = <T extends (...args: any[]) => any>(
    func: T,
    delay: number
): ((...args: Parameters<T>) => void) => {
    let timeoutId: NodeJS.Timeout;
    return (...args: Parameters<T>) => {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => func(...args), delay);
    };
};

/**
 * 节流函数
 */
export const throttle = <T extends (...args: any[]) => any>(
    func: T,
    delay: number
): ((...args: Parameters<T>) => void) => {
    let lastCall = 0;
    return (...args: Parameters<T>) => {
        const now = Date.now();
        if (now - lastCall >= delay) {
            lastCall = now;
            func(...args);
        }
    };
};

/**
 * 深拷贝对象
 */
export const deepClone = <T>(obj: T): T => {
    if (obj === null || typeof obj !== 'object') return obj;
    if (obj instanceof Date) return new Date(obj.getTime()) as unknown as T;
    if (obj instanceof Array) return obj.map(item => deepClone(item)) as unknown as T;
    if (typeof obj === 'object') {
        const clonedObj = {} as T;
        for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
                clonedObj[key] = deepClone(obj[key]);
            }
        }
        return clonedObj;
    }
    return obj;
};

/**
 * 生成唯一ID
 */
export const generateId = (): string => {
    return Math.random().toString(36).substr(2, 9);
};

/**
 * 验证邮箱格式
 */
export const isValidEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
};

/**
 * 验证昵称格式
 */
export const isValidNickname = (nickname: string): boolean => {
    return nickname && nickname.trim().length >= 1 && nickname.trim().length <= 32;
};

/**
 * 获取错误消息
 */
export const getErrorMessage = (error: any): string => {
    if (typeof error === 'string') return error;
    if (error?.message) return error.message;
    if (error?.response?.data?.message) return error.response.data.message;
    return '操作失败，请稍后重试';
};

/**
 * 处理API响应错误
 */
export const handleApiError = (error: any): never => {
    const message = getErrorMessage(error);
    throw new Error(message);
};

/**
 * 安全的JSON解析
 */
export const safeJsonParse = <T>(str: string, defaultValue: T): T => {
    try {
        return JSON.parse(str);
    } catch {
        return defaultValue;
    }
};

/**
 * 安全的JSON字符串化
 */
export const safeJsonStringify = (obj: any): string => {
    try {
        return JSON.stringify(obj);
    } catch {
        return '';
    }
};

/**
 * 计算文件大小显示
 */
export const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * 截断文本
 */
export const truncateText = (text: string, maxLength: number): string => {
    if (!text || text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
};

/**
 * 高亮搜索关键词
 */
export const highlightSearchText = (text: string, searchText: string): string => {
    if (!searchText || !text) return text;
    const regex = new RegExp(`(${searchText})`, 'gi');
    return text.replace(regex, '<mark>$1</mark>');
};

// 重新导出权限检查工具
export * from './permission-utils';