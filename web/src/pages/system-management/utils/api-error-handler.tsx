import { message, notification, Modal } from 'antd';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { parseNetworkError, NetworkErrorType, handlePermissionError } from './network-error-handler';

// API错误处理配置
export interface ApiErrorHandlerConfig {
    showError?: boolean;
    useNotification?: boolean;
    showModal?: boolean;
    customMessage?: string;
    onError?: (error: any) => void;
    onPermissionError?: () => void;
    onNetworkError?: () => void;
    onServerError?: () => void;
}

// 默认配置
const DEFAULT_CONFIG: ApiErrorHandlerConfig = {
    showError: true,
    useNotification: false,
    showModal: false,
};

/**
 * API错误处理器类
 */
export class ApiErrorHandler {
    private config: ApiErrorHandlerConfig;

    constructor(config: ApiErrorHandlerConfig = {}) {
        this.config = { ...DEFAULT_CONFIG, ...config };
    }

    /**
     * 处理API错误
     */
    public handleError = (error: any): void => {
        const networkError = parseNetworkError(error);
        const { type, message: errorMessage, status } = networkError;

        // 调用自定义错误处理回调
        if (this.config.onError) {
            this.config.onError(error);
        }

        // 根据错误类型调用特定回调
        switch (type) {
            case NetworkErrorType.PERMISSION_ERROR:
                if (this.config.onPermissionError) {
                    this.config.onPermissionError();
                }
                handlePermissionError(networkError);
                return;

            case NetworkErrorType.CONNECTION_ERROR:
            case NetworkErrorType.TIMEOUT_ERROR:
                if (this.config.onNetworkError) {
                    this.config.onNetworkError();
                }
                break;

            case NetworkErrorType.SERVER_ERROR:
                if (this.config.onServerError) {
                    this.config.onServerError();
                }
                break;
        }

        // 显示错误信息
        if (this.config.showError) {
            const displayMessage = this.config.customMessage || errorMessage;
            this.showError(displayMessage, type, status);
        }
    };

    /**
     * 显示错误信息
     */
    private showError = (message: string, type: NetworkErrorType, status?: number): void => {
        if (this.config.showModal) {
            this.showErrorModal(message, type);
        } else if (this.config.useNotification) {
            this.showErrorNotification(message, type);
        } else {
            this.showErrorMessage(message, type);
        }
    };

    /**
     * 显示错误消息
     */
    private showErrorMessage = (errorMessage: string, type: NetworkErrorType): void => {
        switch (type) {
            case NetworkErrorType.PERMISSION_ERROR:
                message.warning(errorMessage);
                break;
            case NetworkErrorType.VALIDATION_ERROR:
                message.info(errorMessage);
                break;
            default:
                message.error(errorMessage);
                break;
        }
    };

    /**
     * 显示错误通知
     */
    private showErrorNotification = (errorMessage: string, type: NetworkErrorType): void => {
        const config = {
            message: '操作失败',
            description: errorMessage,
            duration: 4.5,
            placement: 'topRight' as const,
        };

        switch (type) {
            case NetworkErrorType.PERMISSION_ERROR:
                notification.warning(config);
                break;
            case NetworkErrorType.VALIDATION_ERROR:
                notification.info(config);
                break;
            default:
                notification.error(config);
                break;
        }
    };

    /**
     * 显示错误模态框
     */
    private showErrorModal = (errorMessage: string, type: NetworkErrorType): void => {
        const isDanger = type === NetworkErrorType.SERVER_ERROR ||
            type === NetworkErrorType.CONNECTION_ERROR ||
            type === NetworkErrorType.TIMEOUT_ERROR;

        Modal.error({
            title: '操作失败',
            content: errorMessage,
            icon: <ExclamationCircleOutlined style={{
            color: isDanger ? '#ff4d4f' : '#faad14'
        }} />,
okText: '确定',
    centered: true,
        });
    };

    /**
     * 更新配置
     */
    public updateConfig = (newConfig: Partial<ApiErrorHandlerConfig>): void => {
    this.config = { ...this.config, ...newConfig };
};
}

/**
 * 创建API错误处理器实例
 */
export const createApiErrorHandler = (config?: ApiErrorHandlerConfig): ApiErrorHandler => {
    return new ApiErrorHandler(config);
};

/**
 * 默认API错误处理器实例
 */
export const defaultApiErrorHandler = new ApiErrorHandler();

/**
 * 为异步函数添加错误处理的装饰器
 */
export const withApiErrorHandler = <T extends (...args: any[]) => Promise<any>>(
    asyncFn: T,
    config?: ApiErrorHandlerConfig
): T => {
    const errorHandler = new ApiErrorHandler(config);

    return (async (...args: Parameters<T>) => {
        try {
            return await asyncFn(...args);
        } catch (error) {
            errorHandler.handleError(error);
            throw error; // 重新抛出错误，让调用者可以进行额外处理
        }
    }) as T;
};

/**
 * 系统管理专用的API错误处理器
 */
export const systemManagementErrorHandler = new ApiErrorHandler({
    showError: true,
    useNotification: true,
    onPermissionError: () => {
        // 权限错误时的特殊处理
        console.warn('System management permission error');
    },
    onNetworkError: () => {
        // 网络错误时的特殊处理
        console.warn('System management network error');
    },
    onServerError: () => {
        // 服务器错误时的特殊处理
        console.error('System management server error');
    },
});

/**
 * 用户管理API错误处理器
 */
export const userManagementErrorHandler = new ApiErrorHandler({
    showError: true,
    useNotification: true,
    customMessage: '用户管理操作失败，请稍后重试',
});

/**
 * 知识库管理API错误处理器
 */
export const knowledgebaseManagementErrorHandler = new ApiErrorHandler({
    showError: true,
    useNotification: true,
    customMessage: '知识库管理操作失败，请稍后重试',
});

/**
 * 批量操作错误处理器
 */
export const batchOperationErrorHandler = new ApiErrorHandler({
    showError: true,
    showModal: true,
    customMessage: '批量操作失败，部分项目可能未能成功处理',
});

/**
 * 静默错误处理器（不显示错误信息）
 */
export const silentErrorHandler = new ApiErrorHandler({
    showError: false,
});

/**
 * 创建带有重试机制的API错误处理器
 */
export const createRetryableApiErrorHandler = (
    maxRetries: number = 3,
    retryDelay: number = 1000,
    config?: ApiErrorHandlerConfig
) => {
    const errorHandler = new ApiErrorHandler(config);

    return <T extends (...args: any[]) => Promise<any>>(asyncFn: T): T => {
        return (async (...args: Parameters<T>) => {
            let lastError: any;

            for (let attempt = 1; attempt <= maxRetries; attempt++) {
                try {
                    return await asyncFn(...args);
                } catch (error) {
                    lastError = error;
                    const networkError = parseNetworkError(error);

                    // 只对网络错误和服务器错误进行重试
                    const shouldRetry = (
                        networkError.type === NetworkErrorType.CONNECTION_ERROR ||
                        networkError.type === NetworkErrorType.TIMEOUT_ERROR ||
                        (networkError.type === NetworkErrorType.SERVER_ERROR &&
                            networkError.status && networkError.status >= 500)
                    );

                    if (attempt < maxRetries && shouldRetry) {
                        message.info(`操作失败，正在重试 (${attempt}/${maxRetries})...`, 2);
                        await new Promise(resolve => setTimeout(resolve, retryDelay * attempt));
                        continue;
                    }

                    // 不重试或已达到最大重试次数，处理错误
                    errorHandler.handleError(error);
                    throw error;
                }
            }

            throw lastError;
        }) as T;
    };
};

export default {
    ApiErrorHandler,
    createApiErrorHandler,
    defaultApiErrorHandler,
    withApiErrorHandler,
    systemManagementErrorHandler,
    userManagementErrorHandler,
    knowledgebaseManagementErrorHandler,
    batchOperationErrorHandler,
    silentErrorHandler,
    createRetryableApiErrorHandler,
};