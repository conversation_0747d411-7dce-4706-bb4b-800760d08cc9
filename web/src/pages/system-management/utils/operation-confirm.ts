import { Modal, message } from 'antd';
import { ExclamationCircleOutlined, DeleteOutlined, EditOutlined, StopOutlined, PlayCircleOutlined } from '@ant-design/icons';

// 操作类型枚举
export enum OperationType {
    DELETE = 'delete',
    BATCH_DELETE = 'batch_delete',
    ACTIVATE = 'activate',
    DEACTIVATE = 'deactivate',
    BATCH_ACTIVATE = 'batch_activate',
    BATCH_DEACTIVATE = 'batch_deactivate',
    EDIT = 'edit',
    RESET_PASSWORD = 'reset_password',
    TRANSFER_OWNERSHIP = 'transfer_ownership',
    CUSTOM = 'custom',
}

// 确认配置接口
export interface ConfirmConfig {
    type: OperationType;
    title?: string;
    content?: string;
    itemName?: string;
    itemCount?: number;
    okText?: string;
    cancelText?: string;
    danger?: boolean;
    onOk: () => void | Promise<void>;
    onCancel?: () => void;
    showWarning?: boolean;
    warningText?: string;
}

// 操作配置映射
const OPERATION_CONFIGS = {
    [OperationType.DELETE]: {
        title: '确认删除',
        okText: '删除',
        danger: true,
        icon: <DeleteOutlined style={{ color: '#ff4d4f' }} />,
getContent: (itemName?: string) =>
    itemName ? `确定要删除 "${itemName}" 吗？此操作不可撤销。` : '确定要删除此项吗？此操作不可撤销。',
    warningText: '删除后数据将无法恢复，请谨慎操作',
    },
[OperationType.BATCH_DELETE]: {
    title: '确认批量删除',
        okText: '批量删除',
            danger: true,
                icon: <DeleteOutlined style={ { color: '#ff4d4f' } } />,
    getContent: (itemName?: string, itemCount?: number) =>
        `确定要删除选中的 ${itemCount || 0} 个项目吗？此操作不可撤销。`,
        warningText: '批量删除后数据将无法恢复，请谨慎操作',
    },
[OperationType.ACTIVATE]: {
    title: '确认激活',
        okText: '激活',
            danger: false,
                icon: <PlayCircleOutlined style={ { color: '#52c41a' } } />,
    getContent: (itemName?: string) =>
        itemName ? `确定要激活 "${itemName}" 吗？` : '确定要激活此项吗？',
    },
[OperationType.DEACTIVATE]: {
    title: '确认禁用',
        okText: '禁用',
            danger: true,
                icon: <StopOutlined style={ { color: '#ff4d4f' } } />,
    getContent: (itemName?: string) =>
        itemName ? `确定要禁用 "${itemName}" 吗？` : '确定要禁用此项吗？',
        warningText: '禁用后用户将无法正常使用相关功能',
    },
[OperationType.BATCH_ACTIVATE]: {
    title: '确认批量激活',
        okText: '批量激活',
            danger: false,
                icon: <PlayCircleOutlined style={ { color: '#52c41a' } } />,
    getContent: (itemName?: string, itemCount?: number) =>
        `确定要激活选中的 ${itemCount || 0} 个项目吗？`,
    },
[OperationType.BATCH_DEACTIVATE]: {
    title: '确认批量禁用',
        okText: '批量禁用',
            danger: true,
                icon: <StopOutlined style={ { color: '#ff4d4f' } } />,
    getContent: (itemName?: string, itemCount?: number) =>
        `确定要禁用选中的 ${itemCount || 0} 个项目吗？`,
        warningText: '批量禁用后用户将无法正常使用相关功能',
    },
[OperationType.EDIT]: {
    title: '确认修改',
        okText: '保存',
            danger: false,
                icon: <EditOutlined style={ { color: '#1890ff' } } />,
    getContent: (itemName?: string) =>
        itemName ? `确定要保存对 "${itemName}" 的修改吗？` : '确定要保存修改吗？',
    },
[OperationType.RESET_PASSWORD]: {
    title: '确认重置密码',
        okText: '重置',
            danger: true,
                icon: <ExclamationCircleOutlined style={ { color: '#faad14' } } />,
    getContent: (itemName?: string) =>
        itemName ? `确定要重置 "${itemName}" 的密码吗？` : '确定要重置密码吗？',
        warningText: '重置后用户需要使用新密码重新登录',
    },
[OperationType.TRANSFER_OWNERSHIP]: {
    title: '确认转移所有权',
        okText: '转移',
            danger: true,
                icon: <ExclamationCircleOutlined style={ { color: '#faad14' } } />,
    getContent: (itemName?: string) =>
        itemName ? `确定要转移 "${itemName}" 的所有权吗？` : '确定要转移所有权吗？',
        warningText: '转移后原所有者将失去管理权限',
    },
};

/**
 * 显示操作确认对话框
 */
export const showOperationConfirm = (config: ConfirmConfig): void => {
    const {
        type,
        title,
        content,
        itemName,
        itemCount,
        okText,
        cancelText = '取消',
        danger,
        onOk,
        onCancel,
        showWarning = true,
        warningText,
    } = config;

    const operationConfig = OPERATION_CONFIGS[type];

    if (!operationConfig) {
        console.error(`Unknown operation type: ${type}`);
        return;
    }

    const finalTitle = title || operationConfig.title;
    const finalContent = content || operationConfig.getContent(itemName, itemCount);
    const finalOkText = okText || operationConfig.okText;
    const finalDanger = danger !== undefined ? danger : operationConfig.danger;
    const finalWarningText = warningText || operationConfig.warningText;

    Modal.confirm({
        title: (
            <div style= {{ display: 'flex', alignItems: 'center', gap: 8 }}>
                { operationConfig.icon }
                < span > { finalTitle } </span>
                </div>
        ),
content: (
    <div>
    <p style= {{ marginBottom: showWarning && finalWarningText ? 16 : 0 }}>
        { finalContent }
        </p>
{
    showWarning && finalWarningText && (
        <div style={
            {
                padding: '8px 12px',
                    backgroundColor: '#fff7e6',
                        border: '1px solid #ffd591',
                            borderRadius: 4,
                                display: 'flex',
                                    alignItems: 'center',
                                        gap: 8,
                    }
    }>
        <ExclamationCircleOutlined style={ { color: '#faad14' } } />
            < span style = {{ color: '#d46b08', fontSize: 12 }
}>
    { finalWarningText }
    </span>
    </div>
                )}
</div>
        ),
okText: finalOkText,
    cancelText,
    okType: finalDanger ? 'danger' : 'primary',
        icon: null, // 我们在title中已经显示了图标
            width: 480,
                centered: true,
                    onOk: async () => {
                        try {
                            await onOk();
                        } catch (error) {
                            console.error('Operation confirm error:', error);
                            message.error('操作执行失败，请重试');
                        }
                    },
                        onCancel,
    });
};

/**
 * 删除确认的便捷方法
 */
export const confirmDelete = (
    itemName: string,
    onConfirm: () => void | Promise<void>,
    onCancel?: () => void
) => {
    showOperationConfirm({
        type: OperationType.DELETE,
        itemName,
        onOk: onConfirm,
        onCancel,
    });
};

/**
 * 批量删除确认的便捷方法
 */
export const confirmBatchDelete = (
    itemCount: number,
    onConfirm: () => void | Promise<void>,
    onCancel?: () => void
) => {
    showOperationConfirm({
        type: OperationType.BATCH_DELETE,
        itemCount,
        onOk: onConfirm,
        onCancel,
    });
};

/**
 * 状态切换确认的便捷方法
 */
export const confirmStatusChange = (
    action: 'activate' | 'deactivate',
    itemName: string,
    onConfirm: () => void | Promise<void>,
    onCancel?: () => void
) => {
    const type = action === 'activate' ? OperationType.ACTIVATE : OperationType.DEACTIVATE;

    showOperationConfirm({
        type,
        itemName,
        onOk: onConfirm,
        onCancel,
    });
};

/**
 * 批量状态切换确认的便捷方法
 */
export const confirmBatchStatusChange = (
    action: 'activate' | 'deactivate',
    itemCount: number,
    onConfirm: () => void | Promise<void>,
    onCancel?: () => void
) => {
    const type = action === 'activate' ? OperationType.BATCH_ACTIVATE : OperationType.BATCH_DEACTIVATE;

    showOperationConfirm({
        type,
        itemCount,
        onOk: onConfirm,
        onCancel,
    });
};

/**
 * 编辑确认的便捷方法
 */
export const confirmEdit = (
    itemName: string,
    onConfirm: () => void | Promise<void>,
    onCancel?: () => void
) => {
    showOperationConfirm({
        type: OperationType.EDIT,
        itemName,
        onOk: onConfirm,
        onCancel,
    });
};

/**
 * 重置密码确认的便捷方法
 */
export const confirmResetPassword = (
    userName: string,
    onConfirm: () => void | Promise<void>,
    onCancel?: () => void
) => {
    showOperationConfirm({
        type: OperationType.RESET_PASSWORD,
        itemName: userName,
        onOk: onConfirm,
        onCancel,
    });
};

/**
 * 自定义确认对话框
 */
export const confirmCustom = (
    title: string,
    content: string,
    onConfirm: () => void | Promise<void>,
    options: {
        okText?: string;
        cancelText?: string;
        danger?: boolean;
        showWarning?: boolean;
        warningText?: string;
        onCancel?: () => void;
    } = {}
) => {
    showOperationConfirm({
        type: OperationType.CUSTOM,
        title,
        content,
        onOk: onConfirm,
        ...options,
    });
};

/**
 * 创建带确认的操作函数
 */
export const withConfirm = <T extends (...args: any[]) => void | Promise<void>>(
    operation: T,
    confirmConfig: Omit<ConfirmConfig, 'onOk'>
): T => {
    return ((...args: Parameters<T>) => {
        showOperationConfirm({
            ...confirmConfig,
            onOk: () => operation(...args),
        });
    }) as T;
};

/**
 * 危险操作的二次确认
 */
export const showDoubleConfirm = (
    firstConfig: ConfirmConfig,
    secondConfig: Omit<ConfirmConfig, 'onOk'>
) => {
    showOperationConfirm({
        ...firstConfig,
        onOk: () => {
            showOperationConfirm({
                ...secondConfig,
                onOk: firstConfig.onOk,
            });
        },
    });
};

export default {
    showOperationConfirm,
    confirmDelete,
    confirmBatchDelete,
    confirmStatusChange,
    confirmBatchStatusChange,
    confirmEdit,
    confirmResetPassword,
    confirmCustom,
    withConfirm,
    showDoubleConfirm,
};