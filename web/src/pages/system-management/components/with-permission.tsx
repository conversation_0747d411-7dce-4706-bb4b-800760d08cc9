import React from 'react';
import { Result, Button } from 'antd';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { usePermissionCheck } from '../hooks/use-permission-check';

/**
 * 权限检查高阶组件
 * 用于包装需要超级用户权限的组件
 */
export function withPermission<P extends object>(
    WrappedComponent: React.ComponentType<P>
): React.FC<P> {
    const WithPermissionComponent: React.FC<P> = (props) => {
        const { isSuperuser, loading, error } = usePermissionCheck();

        // 权限不足时显示错误页面
        if (!loading && !isSuperuser) {
            return (
                <Result
                    status="403"
                    title="权限不足"
                    subTitle={error || "抱歉，您需要超级用户权限才能访问此功能。"}
                    icon={<ExclamationCircleOutlined />}
                    extra={[
                        <Button
                            type="primary"
                            key="back"
                            onClick={() => window.history.back()}
                        >
                            返回上一页
                        </Button>,
                        <Button
                            key="refresh"
                            onClick={() => window.location.reload()}
                        >
                            重新验证
                        </Button>
                    ]}
                />
            );
        }

        // 权限验证通过或正在加载时渲染原组件
        return <WrappedComponent {...props} />;
    };

    // 设置显示名称，便于调试
    WithPermissionComponent.displayName = `withPermission(${WrappedComponent.displayName || WrappedComponent.name})`;

    return WithPermissionComponent;
}

/**
 * 权限检查装饰器（使用装饰器语法）
 */
export const requireSuperuser = withPermission;

export default withPermission;