import React from 'react';
import { Modal, Button, Space } from 'antd';
import { ExclamationCircleOutlined, DeleteOutlined, EditOutlined, StopOutlined, PlayCircleOutlined } from '@ant-design/icons';
import './index.less';

// 确认对话框类型
export enum ConfirmType {
    DELETE = 'delete',
    EDIT = 'edit',
    ACTIVATE = 'activate',
    DEACTIVATE = 'deactivate',
    BATCH_DELETE = 'batch_delete',
    BATCH_ACTIVATE = 'batch_activate',
    BATCH_DEACTIVATE = 'batch_deactivate',
    CUSTOM = 'custom',
}

// 确认对话框属性
export interface ConfirmDialogProps {
    open: boolean;
    type: ConfirmType;
    title?: string;
    content?: string;
    itemName?: string;
    itemCount?: number;
    loading?: boolean;
    okText?: string;
    cancelText?: string;
    onConfirm: () => void | Promise<void>;
    onCancel: () => void;
    children?: React.ReactNode;
}

// 获取确认对话框配置
const getConfirmConfig = (type: ConfirmType, itemName?: string, itemCount?: number) => {
    switch (type) {
        case ConfirmType.DELETE:
            return {
                title: '确认删除',
                content: itemName ? `确定要删除 "${itemName}" 吗？此操作不可撤销。` : '确定要删除此项吗？此操作不可撤销。',
                okText: '删除',
                okType: 'danger' as const,
                icon: <DeleteOutlined style={{ color: '#ff4d4f' }} />,
                danger: true,
            };

        case ConfirmType.BATCH_DELETE:
            return {
                title: '确认批量删除',
                content: `确定要删除选中的 ${itemCount || 0} 个项目吗？此操作不可撤销。`,
                okText: '批量删除',
                okType: 'danger' as const,
                icon: <DeleteOutlined style={{ color: '#ff4d4f' }} />,
                danger: true,
            };

        case ConfirmType.EDIT:
            return {
                title: '确认修改',
                content: itemName ? `确定要修改 "${itemName}" 吗？` : '确定要保存修改吗？',
                okText: '保存',
                okType: 'primary' as const,
                icon: <EditOutlined style={{ color: '#1890ff' }} />,
                danger: false,
            };

        case ConfirmType.ACTIVATE:
            return {
                title: '确认激活',
                content: itemName ? `确定要激活 "${itemName}" 吗？` : '确定要激活此项吗？',
                okText: '激活',
                okType: 'primary' as const,
                icon: <PlayCircleOutlined style={{ color: '#52c41a' }} />,
                danger: false,
            };

        case ConfirmType.DEACTIVATE:
            return {
                title: '确认禁用',
                content: itemName ? `确定要禁用 "${itemName}" 吗？` : '确定要禁用此项吗？',
                okText: '禁用',
                okType: 'danger' as const,
                icon: <StopOutlined style={{ color: '#ff4d4f' }} />,
                danger: true,
            };

        case ConfirmType.BATCH_ACTIVATE:
            return {
                title: '确认批量激活',
                content: `确定要激活选中的 ${itemCount || 0} 个项目吗？`,
                okText: '批量激活',
                okType: 'primary' as const,
                icon: <PlayCircleOutlined style={{ color: '#52c41a' }} />,
                danger: false,
            };

        case ConfirmType.BATCH_DEACTIVATE:
            return {
                title: '确认批量禁用',
                content: `确定要禁用选中的 ${itemCount || 0} 个项目吗？`,
                okText: '批量禁用',
                okType: 'danger' as const,
                icon: <StopOutlined style={{ color: '#ff4d4f' }} />,
                danger: true,
            };

        default:
            return {
                title: '确认操作',
                content: '确定要执行此操作吗？',
                okText: '确定',
                okType: 'primary' as const,
                icon: <ExclamationCircleOutlined style={{ color: '#faad14' }} />,
                danger: false,
            };
    }
};

/**
 * 确认对话框组件
 */
const ConfirmDialog: React.FC<ConfirmDialogProps> = ({
    open,
    type,
    title,
    content,
    itemName,
    itemCount,
    loading = false,
    okText,
    cancelText = '取消',
    onConfirm,
    onCancel,
    children,
}) => {
    const config = getConfirmConfig(type, itemName, itemCount);

    const finalTitle = title || config.title;
    const finalContent = content || config.content;
    const finalOkText = okText || config.okText;

    const handleConfirm = async () => {
        try {
            await onConfirm();
        } catch (error) {
            console.error('Confirm dialog error:', error);
        }
    };

    return (
        <Modal
            open={open}
            title={
                <div className="confirm-dialog-title">
                    {config.icon}
                    <span>{finalTitle}</span>
                </div>
            }
            onCancel={onCancel}
            footer={
                <div className="confirm-dialog-footer">
                    <Space>
                        <Button onClick={onCancel} disabled={loading}>
                            {cancelText}
                        </Button>
                        <Button
                            type={config.okType}
                            danger={config.danger}
                            loading={loading}
                            onClick={handleConfirm}
                        >
                            {finalOkText}
                        </Button>
                    </Space>
                </div>
            }
            className={`confirm-dialog ${config.danger ? 'danger' : ''}`}
            width={480}
            centered
        >
            <div className="confirm-dialog-content">
                {children || (
                    <div className="confirm-message">
                        <p>{finalContent}</p>
                        {config.danger && (
                            <div className="warning-note">
                                <ExclamationCircleOutlined />
                                <span>此操作不可撤销，请谨慎操作</span>
                            </div>
                        )}
                    </div>
                )}
            </div>
        </Modal>
    );
};

export default ConfirmDialog;