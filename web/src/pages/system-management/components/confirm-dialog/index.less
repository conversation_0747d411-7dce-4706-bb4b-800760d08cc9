@import '../../../../theme/vars.less';

.confirm-dialog {
    .ant-modal-header {
        border-bottom: 1px solid #f0f0f0;
        padding: 16px 24px;

        .confirm-dialog-title {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 16px;
            font-weight: 600;
            color: #262626;

            .anticon {
                font-size: 18px;
            }
        }
    }

    .ant-modal-body {
        padding: 24px;

        .confirm-dialog-content {
            .confirm-message {
                p {
                    font-size: 14px;
                    color: #595959;
                    line-height: 1.6;
                    margin-bottom: 16px;
                }

                .warning-note {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    padding: 12px;
                    background: #fff7e6;
                    border: 1px solid #ffd591;
                    border-radius: 6px;
                    font-size: 13px;
                    color: #d46b08;

                    .anticon {
                        font-size: 14px;
                        color: #faad14;
                    }
                }
            }
        }
    }

    .ant-modal-footer {
        border-top: 1px solid #f0f0f0;
        padding: 12px 24px;

        .confirm-dialog-footer {
            display: flex;
            justify-content: flex-end;

            .ant-btn {
                border-radius: 6px;
                font-weight: 500;
                min-width: 80px;

                &.ant-btn-primary {
                    background: @primary-color;
                    border-color: @primary-color;

                    &:hover:not(:disabled) {
                        background: fade(@primary-color, 80%);
                        border-color: fade(@primary-color, 80%);
                    }
                }

                &.ant-btn-dangerous {
                    &:hover:not(:disabled) {
                        background: #ff7875;
                        border-color: #ff7875;
                    }
                }
            }
        }
    }

    // 危险操作样式
    &.danger {
        .ant-modal-header {
            .confirm-dialog-title {
                color: #ff4d4f;
            }
        }

        .ant-modal-body {
            .confirm-message {
                p {
                    color: #262626;
                }
            }
        }
    }

    // 响应式设计
    @media (max-width: 768px) {
        max-width: 90vw;
        margin: 20px auto;

        .ant-modal-body {
            padding: 16px;

            .confirm-dialog-content {
                .confirm-message {
                    .warning-note {
                        padding: 8px;
                        font-size: 12px;
                    }
                }
            }
        }

        .ant-modal-footer {
            padding: 12px 16px;

            .confirm-dialog-footer {
                .ant-btn {
                    min-width: 70px;
                    font-size: 13px;
                }
            }
        }
    }

    @media (max-width: 480px) {
        max-width: 95vw;
        margin: 10px auto;

        .ant-modal-header {
            padding: 12px 16px;

            .confirm-dialog-title {
                font-size: 15px;

                .anticon {
                    font-size: 16px;
                }
            }
        }

        .ant-modal-body {
            padding: 12px 16px;

            .confirm-dialog-content {
                .confirm-message {
                    p {
                        font-size: 13px;
                        margin-bottom: 12px;
                    }

                    .warning-note {
                        padding: 6px 8px;
                        font-size: 11px;

                        .anticon {
                            font-size: 12px;
                        }
                    }
                }
            }
        }

        .ant-modal-footer {
            padding: 8px 16px;

            .confirm-dialog-footer {
                .ant-btn {
                    min-width: 60px;
                    font-size: 12px;
                    padding: 4px 12px;
                    height: auto;
                }
            }
        }
    }

    // 暗色主题支持
    &.dark {
        .ant-modal-content {
            background: #1f1f1f;
        }

        .ant-modal-header {
            background: #1f1f1f;
            border-bottom-color: #303030;

            .confirm-dialog-title {
                color: #d9d9d9;
            }
        }

        .ant-modal-body {
            background: #1f1f1f;

            .confirm-dialog-content {
                .confirm-message {
                    p {
                        color: #d9d9d9;
                    }

                    .warning-note {
                        background: #2b2111;
                        border-color: #613400;
                        color: #ffc53d;

                        .anticon {
                            color: #ffc53d;
                        }
                    }
                }
            }
        }

        .ant-modal-footer {
            background: #1f1f1f;
            border-top-color: #303030;
        }

        &.danger {
            .ant-modal-header {
                .confirm-dialog-title {
                    color: #ff7875;
                }
            }
        }
    }

    // 高对比度模式
    @media (prefers-contrast: high) {
        .ant-modal-content {
            border: 2px solid #000;
        }

        .ant-modal-header {
            border-bottom-width: 2px;
            border-bottom-color: #000;

            .confirm-dialog-title {
                color: #000;
            }
        }

        .ant-modal-body {
            .confirm-dialog-content {
                .confirm-message {
                    p {
                        color: #000;
                    }

                    .warning-note {
                        border-width: 2px;
                        border-color: #ff8c00;
                        color: #000;

                        .anticon {
                            color: #ff8c00;
                        }
                    }
                }
            }
        }

        .ant-modal-footer {
            border-top-width: 2px;
            border-top-color: #000;
        }
    }

    // 减少动画模式
    @media (prefers-reduced-motion: reduce) {

        .ant-modal-mask,
        .ant-modal-wrap {
            animation-duration: 0.01ms !important;
        }
    }
}