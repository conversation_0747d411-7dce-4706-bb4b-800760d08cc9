import React from 'react';
import { Navigate } from 'umi';
import { Result, Spin } from 'antd';
import { useFetchUserInfo } from '@/hooks/user-setting-hooks';
import { useTranslation } from 'react-i18next';

interface SuperuserAuthProps {
    children: React.ReactNode;
}

const SuperuserAuth: React.FC<SuperuserAuthProps> = ({ children }) => {
    const { data: userInfo, loading } = useFetchUserInfo();
    const { t } = useTranslation('translation', { keyPrefix: 'systemManagement' });


    if (!userInfo?.is_superuser) {
        return (
            <div style={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                height: '100%'
            }}>
                <Result
                    status="403"
                    title="403"
                    subTitle={t('accessDenied', '抱歉，您没有权限访问此页面。')}
                    extra={
                        <a href="/knowledge" style={{ color: '#1890ff' }}>
                            {t('backToHome', '返回首页')}
                        </a>
                    }
                />
            </div>
        );
    }

    return <>{children}</>;
};

export default SuperuserAuth;