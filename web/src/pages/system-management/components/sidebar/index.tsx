import React, { useMemo } from 'react';
import { Menu } from 'antd';
import { UserOutlined, DatabaseOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import type { MenuProps } from 'antd';
import styles from './index.less';

interface SidebarProps {
    activeTab: 'users' | 'knowledgebases';
    onTabChange: (tab: 'users' | 'knowledgebases') => void;
    loading?: boolean;
}

type MenuItem = Required<MenuProps>['items'][number];

const Sidebar: React.FC<SidebarProps> = ({ activeTab, onTabChange, loading = false }) => {
    const { t } = useTranslation('translation', { keyPrefix: 'systemManagement' });

    const getItem = (
        label: string,
        key: React.Key,
        icon?: React.ReactNode,
    ): MenuItem => {
        return {
            key,
            icon,
            label,
            disabled: loading,
        } as MenuItem;
    };

    const menuItems: MenuItem[] = useMemo(() => [
        getItem(t('userManagement', '用户管理'), 'users', <UserOutlined />),
        getItem(t('knowledgebaseManagement', '知识库管理'), 'knowledgebases', <DatabaseOutlined />),
    ], [t, loading]);

    const handleMenuClick: MenuProps['onClick'] = ({ key }) => {
        if (!loading) {
            onTabChange(key as 'users' | 'knowledgebases');
        }
    };

    const selectedKeys = useMemo(() => {
        return [activeTab];
    }, [activeTab]);

    return (
        <section className={styles.sideBarWrapper}>
            <Menu
                selectedKeys={selectedKeys}
                mode="inline"
                items={menuItems}
                onClick={handleMenuClick}
                style={{ width: 312 }}
            />
        </section>
    );
};

export default Sidebar;