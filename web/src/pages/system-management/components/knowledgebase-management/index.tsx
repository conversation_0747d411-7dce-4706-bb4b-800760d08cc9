import React from 'react';
import { useErrorHandler } from '../../hooks/use-error-handler';
import { useFeedback } from '../../hooks/use-feedback';
import ErrorBoundary from '../error-boundary';
import { useKnowledgebaseManagement } from './hooks/use-knowledgebase-management';
import './index.less';
import KnowledgebaseModal from './knowledgebase-modal';
import KnowledgebaseTable from './knowledgebase-table';

const KnowledgebaseManagement: React.FC = () => {
  const errorHandler = useErrorHandler({
    showNotification: true,
    autoClose: true,
    duration: 4.5,
  });
  const feedback = useFeedback();

  const {
    knowledgebases,
    loading,
    pagination,
    filters,
    selectedKnowledgebase,
    detailModalVisible,
    handleSearch,
    handleStatusFilter,
    handleCreatorFilter,
    handlePageChange,
    handleViewDetail,
    handleToggleStatus,
    handleCloseDetailModal,
    refreshKnowledgebases,
  } = useKnowledgebaseManagement();

  // 处理知识库状态切换的确认
  const handleKnowledgebaseStatusToggle = (knowledgebase: any) => {
    const action = knowledgebase.status === '1' ? '禁用' : '激活';

    feedback.showStatusChangeConfirm(action, knowledgebase.name, async () => {
      try {
        await handleToggleStatus(knowledgebase);
        feedback.operationSuccess(action, knowledgebase.name);
      } catch (error) {
        errorHandler.handleError(error);
        feedback.operationError(action, knowledgebase.name);
      }
    });
  };

  // 处理查看详情
  const handleViewKnowledgebaseDetail = async (knowledgebase: any) => {
    try {
      feedback.dataLoading('知识库详情');
      await handleViewDetail(knowledgebase);
      feedback.clearAll();
    } catch (error) {
      errorHandler.handleError(error);
      feedback.operationError('获取详情', knowledgebase.name);
    }
  };

  // 处理刷新操作
  const handleRefresh = async () => {
    try {
      feedback.dataLoading('知识库列表');
      await refreshKnowledgebases();
      feedback.clearAll();
    } catch (error) {
      errorHandler.handleError(error);
    }
  };

  return (
    <ErrorBoundary
      title="知识库管理模块错误"
      description="知识库管理功能遇到问题，请尝试刷新或联系管理员"
      onError={(error, errorInfo) => {
        errorHandler.handleError({
          type: 'UNKNOWN_ERROR' as any,
          message: '知识库管理组件错误',
          details: { error, errorInfo },
        });
      }}
    >
      <div className="knowledgebase-management">
        <div className="knowledgebase-management-content">
          <KnowledgebaseTable
            knowledgebases={knowledgebases || []}
            loading={loading || false}
            pagination={pagination || { current: 1, pageSize: 20, total: 0 }}
            filters={filters || { search: '', status: 'all', creator: '' }}
            onSearch={handleSearch}
            onStatusFilter={handleStatusFilter}
            onCreatorFilter={handleCreatorFilter}
            onPageChange={handlePageChange}
            onViewDetail={handleViewKnowledgebaseDetail}
            onToggleStatus={handleKnowledgebaseStatusToggle}
            onRefresh={handleRefresh}
          />
        </div>

        <KnowledgebaseModal
          visible={detailModalVisible}
          knowledgebase={selectedKnowledgebase}
          loading={loading}
          onClose={handleCloseDetailModal}
        />
      </div>
    </ErrorBoundary>
  );
};

export default KnowledgebaseManagement;
