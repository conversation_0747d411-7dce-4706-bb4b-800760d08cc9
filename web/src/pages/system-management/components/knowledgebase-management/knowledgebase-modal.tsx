import React from 'react';
import {
    Modal,
    Descriptions,
    Divider,
    Spin,
    Button,
    Space,
    Tag,
    Typography,
    Card,
    Row,
    Col,
    Statistic
} from 'antd';
import {
    DatabaseOutlined,
    FileTextOutlined,
    BlockOutlined,
    NumberOutlined,
    UserOutlined,
    CalendarOutlined,
    InfoCircleOutlined,
    CloseOutlined
} from '@ant-design/icons';
import { IKnowledgebaseModalProps } from '../../types';
import { MODAL_CONFIG } from '../../constants';
import {
    formatTimestamp,
    getKnowledgebaseStatusInfo,
    formatNumber,
    getKnowledgebaseDisplayName
} from '../../utils';

const { Text, Paragraph } = Typography;

const KnowledgebaseModal: React.FC<IKnowledgebaseModalProps> = ({
    visible,
    knowledgebase,
    loading
}) => {
    if (!knowledgebase) {
        return null;
    }

    const statusInfo = getKnowledgebaseStatusInfo(knowledgebase.status);

    // 渲染基本信息
    const renderBasicInfo = () => (
        <Card
            title={
                <Space>
                    <InfoCircleOutlined />
                    基本信息
                </Space>
            }
            size="small"
            className="info-card"
        >
            <Descriptions column={2} size="small">
                <Descriptions.Item label="知识库ID" span={2}>
                    <Text code copyable={{ text: knowledgebase.id }}>
                        {knowledgebase.id}
                    </Text>
                </Descriptions.Item>
                <Descriptions.Item label="名称">
                    <Space>
                        <DatabaseOutlined />
                        <Text strong>{knowledgebase.name}</Text>
                    </Space>
                </Descriptions.Item>
                <Descriptions.Item label="状态">
                    <Tag color={statusInfo.color}>
                        {statusInfo.text}
                    </Tag>
                </Descriptions.Item>
                <Descriptions.Item label="创建者">
                    <Space>
                        <UserOutlined />
                        <Text>{knowledgebase.creator_name}</Text>
                    </Space>
                </Descriptions.Item>
                <Descriptions.Item label="创建者ID">
                    <Text code copyable={{ text: knowledgebase.creator_id }}>
                        {knowledgebase.creator_id}
                    </Text>
                </Descriptions.Item>
                <Descriptions.Item label="创建时间">
                    <Space>
                        <CalendarOutlined />
                        <Text>{formatTimestamp(knowledgebase.create_time)}</Text>
                    </Space>
                </Descriptions.Item>
                <Descriptions.Item label="更新时间">
                    <Space>
                        <CalendarOutlined />
                        <Text>{formatTimestamp(knowledgebase.update_time)}</Text>
                    </Space>
                </Descriptions.Item>
                <Descriptions.Item label="描述" span={2}>
                    <Paragraph
                        ellipsis={{ rows: 3, expandable: true, symbol: '展开' }}
                        style={{ margin: 0 }}
                    >
                        {knowledgebase.description || '暂无描述'}
                    </Paragraph>
                </Descriptions.Item>
            </Descriptions>
        </Card>
    );

    // 渲染统计信息
    const renderStatistics = () => {
        const avgTokensPerDoc = knowledgebase.token_num && knowledgebase.doc_num
            ? Math.round(knowledgebase.token_num / knowledgebase.doc_num)
            : 0;

        const avgChunksPerDoc = knowledgebase.chunk_num && knowledgebase.doc_num
            ? Math.round(knowledgebase.chunk_num / knowledgebase.doc_num)
            : 0;

        return (
            <Card
                title={
                    <Space>
                        <FileTextOutlined />
                        统计信息
                    </Space>
                }
                size="small"
                className="stats-card"
            >
                <Row gutter={[16, 16]}>
                    <Col xs={12} sm={6}>
                        <Statistic
                            title="文档数量"
                            value={knowledgebase.doc_num || 0}
                            prefix={<FileTextOutlined />}
                            valueStyle={{ color: '#1890ff' }}
                        />
                    </Col>
                    <Col xs={12} sm={6}>
                        <Statistic
                            title="分块数量"
                            value={formatNumber(knowledgebase.chunk_num || 0)}
                            prefix={<BlockOutlined />}
                            valueStyle={{ color: '#52c41a' }}
                        />
                    </Col>
                    <Col xs={12} sm={6}>
                        <Statistic
                            title="Token数量"
                            value={formatNumber(knowledgebase.token_num || 0)}
                            prefix={<NumberOutlined />}
                            valueStyle={{ color: '#faad14' }}
                        />
                    </Col>
                    <Col xs={12} sm={6}>
                        <Statistic
                            title="平均Token/文档"
                            value={formatNumber(avgTokensPerDoc)}
                            valueStyle={{ color: '#722ed1' }}
                        />
                    </Col>
                </Row>

                <Divider />

                <Row gutter={[16, 16]}>
                    <Col xs={12} sm={6}>
                        <div className="additional-stat">
                            <Text type="secondary">平均分块/文档:</Text>
                            <Text strong> {formatNumber(avgChunksPerDoc)}</Text>
                        </div>
                    </Col>
                    <Col xs={12} sm={6}>
                        <div className="additional-stat">
                            <Text type="secondary">平均Token/分块:</Text>
                            <Text strong>
                                {knowledgebase.chunk_num && knowledgebase.token_num
                                    ? formatNumber(Math.round(knowledgebase.token_num / knowledgebase.chunk_num))
                                    : 0
                                }
                            </Text>
                        </div>
                    </Col>
                    <Col xs={12} sm={6}>
                        <div className="additional-stat">
                            <Text type="secondary">存储效率:</Text>
                            <Text strong>
                                {knowledgebase.doc_num > 0 ? '良好' : '无数据'}
                            </Text>
                        </div>
                    </Col>
                    <Col xs={12} sm={6}>
                        <div className="additional-stat">
                            <Text type="secondary">活跃状态:</Text>
                            <Text strong style={{ color: statusInfo.color }}>
                                {statusInfo.text}
                            </Text>
                        </div>
                    </Col>
                </Row>
            </Card>
        );
    };

    return (
        <Modal
            title={
                <Space>
                    <DatabaseOutlined />
                    知识库详情
                    <Text type="secondary">- {getKnowledgebaseDisplayName(knowledgebase)}</Text>
                </Space>
            }
            open={visible}
            onCancel={onCancel}
            width={MODAL_CONFIG.WIDTH.LARGE}
            maskClosable={MODAL_CONFIG.MASK_CLOSABLE}
            destroyOnClose={MODAL_CONFIG.DESTROY_ON_CLOSE}
            footer={[
                <Button
                    key="close"
                    icon={<CloseOutlined />}
                    onClick={onCancel}
                >
                    关闭
                </Button>,
            ]}
            className="knowledgebase-detail-modal"
        >
            {loading ? (
                <div className="loading-container">
                    <Spin size="large" tip="正在加载知识库详情..." />
                </div>
            ) : (
                <Space direction="vertical" size="large" style={{ width: '100%' }}>
                    {renderBasicInfo()}
                    {renderStatistics()}
                </Space>
            )}
        </Modal>
    );
};

export default KnowledgebaseModal;