import { useState, useCallback } from 'react';
import {
    IKnowledgebaseManagementState,
    IKnowledgebaseForAdmin,
    IUseKnowledgebaseManagementReturn
} from '../../../types';
import { DEFAULT_PAGE_SIZE } from '../../../constants';

/**
 * 知识库管理测试Hook - 使用模拟数据
 * 用于开发和测试阶段
 */
export const useKnowledgebaseManagementTest = (): IUseKnowledgebaseManagementReturn => {
    const [state, setState] = useState<IKnowledgebaseManagementState>({
        knowledgebases: [
            {
                id: 'kb1',
                name: '技术文档知识库',
                description: '包含各种技术文档和API参考',
                created_by: 'user1',
                creator_name: '张三',
                status: '1',
                create_time: Date.now() / 1000,
                update_time: Date.now() / 1000,
                create_date: new Date().toLocaleDateString(),
                update_date: new Date().toLocaleDateString(),
                document_count: 150,
                doc_num: 150,
                chunk_num: 1200,
                token_num: 50000,
            },
            {
                id: 'kb2',
                name: '产品手册知识库',
                description: '产品使用手册和常见问题解答',
                created_by: 'user2',
                creator_name: '李四',
                status: '0',
                create_time: Date.now() / 1000 - 86400,
                update_time: Date.now() / 1000 - 3600,
                create_date: new Date(Date.now() - 86400000).toLocaleDateString(),
                update_date: new Date(Date.now() - 3600000).toLocaleDateString(),
                document_count: 80,
                doc_num: 80,
                chunk_num: 600,
                token_num: 25000,
            },
            {
                id: 'kb3',
                name: '培训资料知识库',
                description: '员工培训资料和学习材料',
                created_by: 'user1',
                creator_name: '张三',
                status: '1',
                create_time: Date.now() / 1000 - 172800,
                update_time: Date.now() / 1000 - 7200,
                create_date: new Date(Date.now() - 172800000).toLocaleDateString(),
                update_date: new Date(Date.now() - 7200000).toLocaleDateString(),
                document_count: 200,
                doc_num: 200,
                chunk_num: 1800,
                token_num: 75000,
            },
        ],
        loading: false,
        pagination: {
            current: 1,
            pageSize: DEFAULT_PAGE_SIZE,
            total: 3,
        },
        filters: {
            search: '',
            status: 'all',
            creator: '',
        },
        selectedKnowledgebase: null,
        detailModalVisible: false,
    });

    const fetchKnowledgebases = useCallback(async () => {
        setState(prev => ({ ...prev, loading: true }));
        // 模拟API调用延迟
        setTimeout(() => {
            setState(prev => ({ ...prev, loading: false }));
        }, 1000);
    }, []);

    const handleSearch = useCallback((search: string) => {
        setState(prev => ({
            ...prev,
            filters: { ...prev.filters, search },
            pagination: { ...prev.pagination, current: 1 },
        }));
    }, []);

    const handleStatusFilter = useCallback((status: 'all' | '1' | '0') => {
        setState(prev => ({
            ...prev,
            filters: { ...prev.filters, status },
            pagination: { ...prev.pagination, current: 1 },
        }));
    }, []);

    const handleCreatorFilter = useCallback((creator: string) => {
        setState(prev => ({
            ...prev,
            filters: { ...prev.filters, creator },
            pagination: { ...prev.pagination, current: 1 },
        }));
    }, []);

    const handlePageChange = useCallback((page: number, pageSize?: number) => {
        setState(prev => ({
            ...prev,
            pagination: {
                ...prev.pagination,
                current: page,
                pageSize: pageSize || prev.pagination.pageSize,
            },
        }));
    }, []);

    const handleViewDetail = useCallback((knowledgebase: IKnowledgebaseForAdmin) => {
        setState(prev => ({
            ...prev,
            selectedKnowledgebase: knowledgebase,
            detailModalVisible: true,
        }));
    }, []);

    const handleToggleKnowledgebaseStatus = useCallback(async (knowledgebase: IKnowledgebaseForAdmin) => {
        const newStatus = knowledgebase.status === '1' ? '0' : '1';
        setState(prev => ({
            ...prev,
            knowledgebases: prev.knowledgebases.map(kb =>
                kb.id === knowledgebase.id
                    ? { ...kb, status: newStatus }
                    : kb
            ),
        }));
    }, []);

    const handleCloseDetailModal = useCallback(() => {
        setState(prev => ({
            ...prev,
            selectedKnowledgebase: null,
            detailModalVisible: false,
        }));
    }, []);

    const refreshKnowledgebases = useCallback(() => {
        fetchKnowledgebases();
    }, [fetchKnowledgebases]);

    const resetFilters = useCallback(() => {
        setState(prev => ({
            ...prev,
            filters: {
                search: '',
                status: 'all',
                creator: '',
            },
            pagination: { ...prev.pagination, current: 1 },
        }));
    }, []);

    return {
        knowledgebases: state.knowledgebases,
        loading: state.loading,
        pagination: state.pagination,
        filters: state.filters,
        selectedKnowledgebase: state.selectedKnowledgebase,
        detailModalVisible: state.detailModalVisible,
        fetchKnowledgebases,
        handleSearch,
        handleStatusFilter,
        handleCreatorFilter,
        handlePageChange,
        handleViewDetail,
        handleToggleKnowledgebaseStatus,
        handleCloseDetailModal,
        refreshKnowledgebases,
        resetFilters,
    };
};