import { useState, useCallback, useEffect, useRef } from 'react';
import { message } from 'antd';
import {
    IKnowledgebaseManagementState,
    IKnowledgebaseForAdmin,
    IUseKnowledgebaseManagementReturn,
    IKnowledgebaseSearchParams
} from '../../../types';
import {
    DEFAULT_PAGE_SIZE,
    SEARCH_DEBOUNCE_DELAY,
    SUCCESS_MESSAGES,
    STORAGE_KEYS
} from '../../../constants';
import { debounce, safeJsonParse, safeJsonStringify } from '../../../utils';
import { useErrorHandler } from '../../../hooks/use-error-handler';
import SystemManagementService from '@/services/system-management-service';

/**
 * 知识库管理数据获取和状态管理Hook
 */
export const useKnowledgebaseManagement = (): IUseKnowledgebaseManagementReturn => {
    const { handleError, showSuccess } = useErrorHandler();
    const [state, setState] = useState<IKnowledgebaseManagementState>({
        knowledgebases: [],
        loading: false,
        pagination: {
            current: 1,
            pageSize: DEFAULT_PAGE_SIZE,
            total: 0,
        },
        filters: {
            search: '',
            status: 'all',
            creator: '',
        },
        selectedKnowledgebase: null,
        detailModalVisible: false,
    });

    // 防抖搜索引用
    const debouncedFetchRef = useRef<(() => void) | null>(null);

    // 初始化时从本地存储恢复筛选条件
    useEffect(() => {
        const savedFilters = localStorage.getItem(STORAGE_KEYS.KNOWLEDGEBASE_MANAGEMENT_FILTERS);
        if (savedFilters) {
            const filters = safeJsonParse(savedFilters, state.filters);
            setState(prev => ({
                ...prev,
                filters,
            }));
        }
    }, []);

    // 保存筛选条件到本地存储
    const saveFiltersToStorage = useCallback((filters: IKnowledgebaseManagementState['filters']) => {
        localStorage.setItem(STORAGE_KEYS.KNOWLEDGEBASE_MANAGEMENT_FILTERS, safeJsonStringify(filters));
    }, []);

    // 获取知识库列表
    const fetchKnowledgebases = useCallback(async (customParams?: any) => {
        setState(prev => ({ ...prev, loading: true }));

        try {
            // 使用传入的参数或当前state的值
            const currentState = customParams || {
                pagination: state.pagination,
                filters: state.filters
            };

            const params: IKnowledgebaseSearchParams = {
                page: currentState.pagination?.current || state.pagination.current,
                size: currentState.pagination?.pageSize || state.pagination.pageSize,
                search: currentState.filters?.search || state.filters.search || '',
                status: ((currentState.filters?.status || state.filters.status) === 'all' ? 'all' : (currentState.filters?.status || state.filters.status)) as 'all' | '1' | '0',
                creator: currentState.filters?.creator || state.filters.creator || '',
            };
            const response = await SystemManagementService.getKnowledgebaseList(params);
            // 安全检查：确保response和knowledgebases存在
            if (!response || !Array.isArray(response.knowledgebases)) {
                console.error('Invalid knowledgebase response structure:', response);
                setState(prev => ({
                    ...prev,
                    knowledgebases: [],
                    loading: false
                }));
                return;
            }

            // 映射后端数据到前端接口
            const mappedKnowledgebases: IKnowledgebaseForAdmin[] = response.knowledgebases.map(kb => ({
                id: kb.id || '',
                name: kb.name || '',
                description: kb.description || '',
                creator_id: kb.creator_id || '',
                creator_name: kb.creator_name || '',
                status: kb.status || '0',
                create_time: kb.create_time || 0,
                update_time: kb.update_time || 0,
                doc_num: kb.doc_num || 0,
                chunk_num: kb.chunk_num || 0,
                token_num: kb.token_num || 0,
            }));

            setState(prev => ({
                ...prev,
                knowledgebases: mappedKnowledgebases,
                pagination: {
                    ...prev.pagination,
                    total: response.total || 0,
                    current: response.page || 1,
                    pageSize: response.size || prev.pagination.pageSize,
                },
                loading: false,
            }));
        } catch (error) {
            console.error('Failed to fetch knowledgebases:', error);
            handleError(error);
            setState(prev => ({
                ...prev,
                knowledgebases: [],
                loading: false
            }));
        }
    }, [handleError]);

    // 防抖获取知识库列表
    const debouncedFetchKnowledgebases = useCallback(
        debounce((params?: any) => {
            fetchKnowledgebases(params);
        }, SEARCH_DEBOUNCE_DELAY),
        [fetchKnowledgebases]
    );

    // 初始化时获取知识库列表
    useEffect(() => {
        fetchKnowledgebases();
    }, []);

    // 处理搜索
    const handleSearch = useCallback((search: string) => {
        const newFilters = { ...state.filters, search };
        const newPagination = { ...state.pagination, current: 1 };

        setState(prev => ({
            ...prev,
            filters: newFilters,
            pagination: newPagination,
        }));

        saveFiltersToStorage(newFilters);

        // 立即使用新参数获取数据
        debouncedFetchKnowledgebases({
            pagination: newPagination,
            filters: newFilters
        });
    }, [state.filters, state.pagination, saveFiltersToStorage, debouncedFetchKnowledgebases]);

    // 处理状态筛选
    const handleStatusFilter = useCallback((status: 'all' | '1' | '0') => {
        const newFilters = { ...state.filters, status };
        const newPagination = { ...state.pagination, current: 1 };

        setState(prev => ({
            ...prev,
            filters: newFilters,
            pagination: newPagination,
        }));

        saveFiltersToStorage(newFilters);

        // 立即使用新参数获取数据
        fetchKnowledgebases({
            pagination: newPagination,
            filters: newFilters
        });
    }, [state.filters, state.pagination, saveFiltersToStorage, fetchKnowledgebases]);

    // 处理创建者筛选
    const handleCreatorFilter = useCallback((creator: string) => {
        const newFilters = { ...state.filters, creator };
        const newPagination = { ...state.pagination, current: 1 };

        setState(prev => ({
            ...prev,
            filters: newFilters,
            pagination: newPagination,
        }));

        saveFiltersToStorage(newFilters);

        // 立即使用新参数获取数据
        debouncedFetchKnowledgebases({
            pagination: newPagination,
            filters: newFilters
        });
    }, [state.filters, state.pagination, saveFiltersToStorage, debouncedFetchKnowledgebases]);

    // 处理分页变化
    const handlePageChange = useCallback((page: number, pageSize?: number) => {
        const newPageSize = pageSize || state.pagination.pageSize;
        const newPage = pageSize && pageSize !== state.pagination.pageSize ? 1 : page; // 如果改变页面大小，重置到第1页

        const newPagination = {
            current: newPage,
            pageSize: newPageSize,
            total: state.pagination.total
        };

        setState(prev => ({
            ...prev,
            pagination: newPagination,
        }));

        // 立即获取数据
        fetchKnowledgebases({
            pagination: newPagination,
            filters: state.filters
        });
    }, [state.pagination, state.filters, fetchKnowledgebases]);

    // 处理查看详情
    const handleViewDetail = useCallback((knowledgebase: IKnowledgebaseForAdmin) => {
        setState(prev => ({
            ...prev,
            selectedKnowledgebase: knowledgebase,
            detailModalVisible: true,
        }));
    }, []);

    // 处理切换知识库状态
    const handleToggleKnowledgebaseStatus = useCallback(async (knowledgebase: IKnowledgebaseForAdmin) => {
        try {
            const newStatus = knowledgebase.status === '1' ? '0' : '1';
            await SystemManagementService.updateKnowledgebaseStatus(knowledgebase.id, { status: newStatus });
            showSuccess(SUCCESS_MESSAGES.KNOWLEDGEBASE_STATUS_UPDATED);
            await fetchKnowledgebases();
        } catch (error) {
            handleError(error);
        }
    }, [showSuccess, handleError, fetchKnowledgebases]);

    // 关闭详情模态框
    const handleCloseDetailModal = useCallback(() => {
        setState(prev => ({
            ...prev,
            selectedKnowledgebase: null,
            detailModalVisible: false,
        }));
    }, []);

    // 刷新知识库列表
    const refreshKnowledgebases = useCallback(async () => {
        await fetchKnowledgebases();
    }, [fetchKnowledgebases]);

    // 重置筛选条件
    const resetFilters = useCallback(() => {
        const defaultFilters = {
            search: '',
            status: 'all' as const,
            creator: '',
        };
        setState(prev => ({
            ...prev,
            filters: defaultFilters,
            pagination: { ...prev.pagination, current: 1 },
        }));
        saveFiltersToStorage(defaultFilters);
    }, [saveFiltersToStorage]);

    return {
        // 状态
        knowledgebases: state.knowledgebases,
        loading: state.loading,
        pagination: state.pagination,
        filters: state.filters,
        selectedKnowledgebase: state.selectedKnowledgebase,
        detailModalVisible: state.detailModalVisible,

        // 操作方法
        handleSearch,
        handleStatusFilter,
        handleCreatorFilter,
        handlePageChange,
        handleViewDetail,
        handleToggleStatus: handleToggleKnowledgebaseStatus,
        handleCloseDetailModal,
        refreshKnowledgebases,
        resetFilters,
    };
};