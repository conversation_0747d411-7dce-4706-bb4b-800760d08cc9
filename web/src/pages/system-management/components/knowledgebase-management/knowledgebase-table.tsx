import { <PERSON>RouteKey } from '@/constants/knowledge';
import {
  DatabaseOutlined,
  FileTextOutlined,
  FilterOutlined,
  ReloadOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import {
  Badge,
  Button,
  Input,
  Popconfirm,
  Select,
  Space,
  Table,
  Tooltip,
  Typography,
} from 'antd';
import type { ColumnsType } from 'antd/es/table';
import React, { useMemo } from 'react';
import { useNavigate } from 'umi';
import {
  CONFIRM_MESSAGES,
  KNOWLEDGEBASE_STATUS_OPTIONS,
  PLACEHOLDER_TEXTS,
  TABLE_CONFIG,
} from '../../constants';
import { IKnowledgebaseForAdmin, IPagination } from '../../types';
import {
  formatNumber,
  formatTimestamp,
  getKnowledgebaseStatusInfo,
} from '../../utils';

const { Option } = Select;
const { Text } = Typography;

interface KnowledgebaseTableProps {
  knowledgebases: IKnowledgebaseForAdmin[];
  loading: boolean;
  pagination: IPagination;
  filters: {
    search: string;
    status: 'all' | '1' | '0';
    creator: string;
  };
  onSearch: (search: string) => void;
  onStatusFilter: (status: 'all' | '1' | '0') => void;
  onCreatorFilter: (creator: string) => void;
  onPageChange: (page: number, pageSize?: number) => void;
  onViewDetail: (knowledgebase: IKnowledgebaseForAdmin) => void;
  onToggleStatus: (knowledgebase: IKnowledgebaseForAdmin) => void;
  onRefresh?: () => void;
}

const KnowledgebaseTable: React.FC<KnowledgebaseTableProps> = ({
  knowledgebases = [], // 添加默认值
  loading,
  pagination,
  filters,
  onSearch,
  onStatusFilter,
  onCreatorFilter,
  onPageChange,
  onViewDetail,
  onToggleStatus,
  onRefresh,
}) => {
  const navigate = useNavigate();
  // 确保knowledgebases是数组
  const safeKnowledgebases = Array.isArray(knowledgebases)
    ? knowledgebases
    : [];

  // 移除创建者选项，因为改为输入框

  // 表格列定义
  const columns: ColumnsType<IKnowledgebaseForAdmin> = useMemo(
    () => [
      {
        title: '序号',
        key: 'index',
        width: 80,
        align: 'center',
        fixed: 'left', // 固定序号列
        render: (_, __, index) =>
          (pagination.current - 1) * pagination.pageSize + index + 1,
      },
      {
        title: '知识库名称',
        dataIndex: 'name',
        key: 'name',
        width: 200,
        ellipsis: true,
        fixed: 'left', // 固定知识库名称列
        render: (name: string) => (
          <Space>
            <DatabaseOutlined />
            <Tooltip title={name}>
              <Text strong>{name}</Text>
            </Tooltip>
          </Space>
        ),
      },
      {
        title: '描述',
        dataIndex: 'description',
        key: 'description',
        width: 250,
        ellipsis: true,
        render: (description: string) => (
          <Tooltip title={description || '无描述'}>
            <Text type="secondary">{description || '无描述'}</Text>
          </Tooltip>
        ),
      },
      {
        title: '创建者',
        dataIndex: 'creator_name',
        key: 'creator_name',
        width: 120,
        ellipsis: true,
        render: (creator_name: string) => <Text>{creator_name}</Text>,
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        width: 100,
        align: 'center',
        render: (status: '1' | '0') => {
          const statusInfo = getKnowledgebaseStatusInfo(status);
          return (
            <Badge
              status={status === '1' ? 'success' : 'error'}
              text={statusInfo.text}
            />
          );
        },
      },
      {
        title: '统计信息',
        key: 'stats',
        width: 200,
        render: (_, record) => (
          <div className="kb-stats">
            <div className="stat-item">
              <FileTextOutlined />
              <span className="stat-label">文档:</span>
              <span className="stat-value">
                {formatNumber(record.doc_num || 0)}
              </span>
            </div>
            <div className="stat-item">
              <span className="stat-label">分块:</span>
              <span className="stat-value">
                {formatNumber(record.chunk_num || 0)}
              </span>
            </div>
            <div className="stat-item">
              <span className="stat-label">Token:</span>
              <span className="stat-value">
                {formatNumber(record.token_num || 0)}
              </span>
            </div>
          </div>
        ),
      },
      {
        title: '创建时间',
        dataIndex: 'create_time',
        key: 'create_time',
        width: 160,
        render: (create_time: number) => (
          <Tooltip title={formatTimestamp(create_time)}>
            <Text type="secondary">{formatTimestamp(create_time)}</Text>
          </Tooltip>
        ),
      },
      {
        title: '更新时间',
        dataIndex: 'update_time',
        key: 'update_time',
        width: 160,
        minWidth: 160,
        maxWidth: 160,
        ellipsis: false,
        render: (update_time: number) => (
          <Tooltip title={formatTimestamp(update_time)}>
            <Text type="secondary">{formatTimestamp(update_time)}</Text>
          </Tooltip>
        ),
      },
      {
        title: '操作',
        key: 'actions',
        width: 220,
        minWidth: 220,
        maxWidth: 220,
        fixed: 'right',
        ellipsis: false,
        render: (_, record) => (
          <Space
            size="small"
            style={{
              display: 'flex',
              justifyContent: 'flex-start',
              flexWrap: 'nowrap',
            }}
          >
            <Button
              type="link"
              size="small"
              onClick={() =>
                navigate(
                  `/knowledge/${KnowledgeRouteKey.Dataset}?id=${record.id}`,
                )
              }
              style={{ padding: '0 8px', minWidth: 'auto' }}
            >
              详情
            </Button>

            <Popconfirm
              title={
                record.status === '1'
                  ? CONFIRM_MESSAGES.DISABLE_KNOWLEDGEBASE
                  : CONFIRM_MESSAGES.ENABLE_KNOWLEDGEBASE
              }
              description={CONFIRM_MESSAGES.WARNING}
              onConfirm={() => onToggleStatus(record)}
              okText="确认"
              cancelText="取消"
              okType={record.status === '1' ? 'danger' : 'primary'}
            >
              <Button
                type="link"
                size="small"
                danger={record.status === '1'}
                style={{ padding: '0 8px', minWidth: 'auto' }}
              >
                {record.status === '1' ? '禁用' : '激活'}
              </Button>
            </Popconfirm>
          </Space>
        ),
      },
    ],
    [onViewDetail, onToggleStatus],
  );

  // 渲染搜索和筛选栏
  const renderSearchBar = () => (
    <div className="search-bar">
      <Space size="middle" wrap>
        <Input
          placeholder={PLACEHOLDER_TEXTS.SEARCH_KNOWLEDGEBASES}
          value={filters.search}
          style={{ width: 220 }}
          allowClear
          onChange={(e) => onSearch(e.target.value)}
          prefix={<SearchOutlined />}
        />

        <Select
          placeholder={PLACEHOLDER_TEXTS.SELECT_STATUS}
          value={filters.status}
          onChange={onStatusFilter}
          className="filter-select"
          style={{ width: 120 }}
          suffixIcon={<FilterOutlined />}
        >
          {KNOWLEDGEBASE_STATUS_OPTIONS?.map?.((option) => (
            <Option key={option.value} value={option.value}>
              {option.label}
            </Option>
          )) || []}
        </Select>

        <Input
          placeholder="输入创建者名称"
          value={filters.creator}
          onChange={(e) => onCreatorFilter(e.target.value)}
          allowClear
          style={{ width: 150 }}
          prefix={<SearchOutlined />}
        />

        {onRefresh && (
          <Tooltip title="刷新数据">
            <Button
              icon={<ReloadOutlined />}
              onClick={onRefresh}
              loading={loading}
            >
              刷新
            </Button>
          </Tooltip>
        )}
      </Space>
    </div>
  );

  return (
    <div
      className="knowledgebase-management-table"
      style={{ height: '100%', display: 'flex', flexDirection: 'column' }}
    >
      {renderSearchBar()}
      <div
        className="table-container"
        style={{
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          minHeight: 0,
        }}
      >
        <Table<IKnowledgebaseForAdmin>
          columns={columns}
          dataSource={safeKnowledgebases}
          loading={loading}
          rowKey={TABLE_CONFIG.ROW_KEY}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条`,
            pageSizeOptions: TABLE_CONFIG.PAGE_SIZE_OPTIONS?.map?.(String) || [
              '10',
              '20',
              '50',
              '100',
            ],
            onChange: onPageChange,
            onShowSizeChange: onPageChange,
          }}
          scroll={{
            x: 1200,
            y: 'calc(100vh - 240px)', // 调整高度计算，减少留白
          }}
          size="middle"
          bordered
          style={{ height: '100%' }}
        />
      </div>
    </div>
  );
};

export default KnowledgebaseTable;
