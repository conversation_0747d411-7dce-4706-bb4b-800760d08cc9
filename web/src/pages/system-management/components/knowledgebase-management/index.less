@import '../../../../theme/vars.less';

.knowledgebase-management {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .knowledgebase-management-content {
    width: 100%;
    flex: 1;
    padding: 16px;
    min-height: 0;

    @media screen and (min-height: 769px) {
      overflow-y: auto;
    }
  }
}

// 知识库管理表格组件样式
.knowledgebase-management-table {
  .search-bar {
    margin-bottom: 16px;
    padding: 16px;
    background: #fafafa;
    border-radius: 6px;
    border: 1px solid #d9d9d9;

    .search-input {
      .ant-input-search-button {
        border-radius: 0 6px 6px 0;
      }
    }

    .filter-select {
      .ant-select-selector {
        border-radius: 6px;
      }
    }
  }

  .table-container {
    background: #fff;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    // 强制列宽度控制
    .ant-table {
      // 更新时间列固定宽度
      .ant-table-thead > tr > th:nth-child(8),
      .ant-table-tbody > tr > td:nth-child(8) {
        width: 160px !important;
        min-width: 160px !important;
        max-width: 160px !important;
      }

      // 操作列固定宽度
      .ant-table-thead > tr > th:nth-child(9),
      .ant-table-tbody > tr > td:nth-child(9) {
        width: 220px !important;
        min-width: 220px !important;
        max-width: 220px !important;
      }
    }

    ::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    ::-webkit-scrollbar-thumb {
      background: #ccc;
      border-radius: 3px;

      &:hover {
        background: #999;
      }
    }

    ::-webkit-scrollbar-track {
      background: transparent;
    }

    .ant-table-body {
      overflow-y: overlay !important;

      &:hover {
        &::-webkit-scrollbar-thumb {
          background: #999;
        }
      }
    }
  }

  .kb-stats {
    display: flex;
    flex-direction: column;
    gap: 4px;

    .stat-item {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 12px;
      color: #8c8c8c;

      .anticon {
        font-size: 10px;
      }

      .stat-label {
        color: #8c8c8c;
      }

      .stat-value {
        font-weight: 500;
        color: #262626;
      }
    }
  }
}

// 知识库详情模态框样式
.knowledgebase-detail-modal {
  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
  }

  .info-card,
  .stats-card {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .additional-stat {
    display: flex;
    align-items: center;
    gap: 4px;
  }
}
