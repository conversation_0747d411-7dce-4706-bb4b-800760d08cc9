@import '../../../../theme/vars.less';

.loading-state {
    width: 100%;

    // 旋转加载器容器
    &.loading-spin {
        .spin-container {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 200px;
            background: #fff;
            border-radius: 8px;

            .ant-spin {
                .ant-spin-text {
                    margin-top: 12px;
                    color: #8c8c8c;
                    font-size: 14px;
                }
            }
        }
    }

    // 骨架屏容器
    &.loading-skeleton {
        .skeleton-container {
            padding: 24px;
            background: #fff;
            border-radius: 8px;
            border: 1px solid #f0f0f0;

            .ant-skeleton {
                .ant-skeleton-header {
                    .ant-skeleton-avatar {
                        background: linear-gradient(90deg, #f0f0f0 25%, #e6e6e6 50%, #f0f0f0 75%);
                        background-size: 200% 100%;
                        animation: skeleton-loading 1.5s infinite;
                    }
                }

                .ant-skeleton-content {

                    .ant-skeleton-title,
                    .ant-skeleton-paragraph>li {
                        background: linear-gradient(90deg, #f0f0f0 25%, #e6e6e6 50%, #f0f0f0 75%);
                        background-size: 200% 100%;
                        animation: skeleton-loading 1.5s infinite;
                    }
                }
            }
        }
    }

    // 卡片骨架屏
    &.loading-card_skeleton {
        .card-skeleton-container {
            .card-skeleton {
                border: 1px solid #f0f0f0;
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

                .ant-card-body {
                    padding: 24px;
                }
            }
        }
    }

    // 表格骨架屏
    &.loading-table_skeleton {
        .table-skeleton-container {
            background: #fff;
            border: 1px solid #f0f0f0;
            border-radius: 8px;
            overflow: hidden;

            .table-skeleton {
                .table-skeleton-header {
                    padding: 16px 24px;
                    background: #fafafa;
                    border-bottom: 1px solid #f0f0f0;

                    .ant-skeleton-input {
                        background: linear-gradient(90deg, #f0f0f0 25%, #e6e6e6 50%, #f0f0f0 75%);
                        background-size: 200% 100%;
                        animation: skeleton-loading 1.5s infinite;
                    }
                }

                .table-skeleton-body {
                    .table-skeleton-row {
                        padding: 12px 24px;
                        border-bottom: 1px solid #f0f0f0;

                        &:last-child {
                            border-bottom: none;
                        }

                        .ant-skeleton {
                            .ant-skeleton-content {
                                .ant-skeleton-paragraph {
                                    display: flex;
                                    gap: 16px;
                                    margin: 0;

                                    >li {
                                        flex: 1;
                                        background: linear-gradient(90deg, #f0f0f0 25%, #e6e6e6 50%, #f0f0f0 75%);
                                        background-size: 200% 100%;
                                        animation: skeleton-loading 1.5s infinite;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    // 自定义加载器
    &.loading-custom {
        .custom-loading-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background: #fff;
            border-radius: 8px;
            border: 1px solid #f0f0f0;

            .custom-loading-content {
                text-align: center;

                .loading-tip {
                    margin-top: 12px;
                    color: #8c8c8c;
                    font-size: 14px;
                }
            }
        }
    }

    // 空状态
    &.empty-state {
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 200px;
        background: #fff;
        border-radius: 8px;
        border: 1px solid #f0f0f0;

        .ant-empty {
            .ant-empty-image {
                svg {
                    opacity: 0.6;
                }
            }

            .ant-empty-description {
                color: #8c8c8c;
                font-size: 14px;
            }
        }
    }

    // 骨架屏动画
    @keyframes skeleton-loading {
        0% {
            background-position: 200% 0;
        }

        100% {
            background-position: -200% 0;
        }
    }

    // 旋转动画
    @keyframes spin {
        0% {
            transform: rotate(0deg);
        }

        100% {
            transform: rotate(360deg);
        }
    }

    // 响应式设计
    @media (max-width: 768px) {
        &.loading-spin {
            .spin-container {
                min-height: 150px;
                padding: 16px;

                .ant-spin {
                    .ant-spin-text {
                        font-size: 13px;
                    }
                }
            }
        }

        &.loading-skeleton {
            .skeleton-container {
                padding: 16px;
            }
        }

        &.loading-card_skeleton {
            .card-skeleton-container {
                .card-skeleton {
                    .ant-card-body {
                        padding: 16px;
                    }
                }
            }
        }

        &.loading-table_skeleton {
            .table-skeleton-container {
                .table-skeleton {
                    .table-skeleton-header {
                        padding: 12px 16px;
                    }

                    .table-skeleton-body {
                        .table-skeleton-row {
                            padding: 8px 16px;

                            .ant-skeleton {
                                .ant-skeleton-content {
                                    .ant-skeleton-paragraph {
                                        gap: 12px;

                                        >li {
                                            height: 16px;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        &.loading-custom {
            .custom-loading-container {
                padding: 16px;

                .custom-loading-content {
                    .loading-tip {
                        font-size: 13px;
                    }
                }
            }
        }

        &.empty-state {
            min-height: 150px;
            padding: 16px;

            .ant-empty {
                .ant-empty-description {
                    font-size: 13px;
                }
            }
        }
    }

    @media (max-width: 480px) {
        &.loading-spin {
            .spin-container {
                min-height: 120px;
                padding: 12px;

                .ant-spin {
                    .ant-spin-text {
                        font-size: 12px;
                    }
                }
            }
        }

        &.loading-skeleton {
            .skeleton-container {
                padding: 12px;
            }
        }

        &.loading-card_skeleton {
            .card-skeleton-container {
                .card-skeleton {
                    .ant-card-body {
                        padding: 12px;
                    }
                }
            }
        }

        &.loading-table_skeleton {
            .table-skeleton-container {
                .table-skeleton {
                    .table-skeleton-header {
                        padding: 8px 12px;
                    }

                    .table-skeleton-body {
                        .table-skeleton-row {
                            padding: 6px 12px;

                            .ant-skeleton {
                                .ant-skeleton-content {
                                    .ant-skeleton-paragraph {
                                        gap: 8px;

                                        >li {
                                            height: 14px;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        &.loading-custom {
            .custom-loading-container {
                padding: 12px;

                .custom-loading-content {
                    .loading-tip {
                        font-size: 12px;
                    }
                }
            }
        }

        &.empty-state {
            min-height: 120px;
            padding: 12px;

            .ant-empty {
                .ant-empty-description {
                    font-size: 12px;
                }
            }
        }
    }

    // 暗色主题支持
    &.dark {
        &.loading-spin {
            .spin-container {
                background: #1f1f1f;
            }
        }

        &.loading-skeleton {
            .skeleton-container {
                background: #1f1f1f;
                border-color: #303030;

                .ant-skeleton {
                    .ant-skeleton-header {
                        .ant-skeleton-avatar {
                            background: linear-gradient(90deg, #303030 25%, #434343 50%, #303030 75%);
                        }
                    }

                    .ant-skeleton-content {

                        .ant-skeleton-title,
                        .ant-skeleton-paragraph>li {
                            background: linear-gradient(90deg, #303030 25%, #434343 50%, #303030 75%);
                        }
                    }
                }
            }
        }

        &.loading-card_skeleton {
            .card-skeleton-container {
                .card-skeleton {
                    background: #1f1f1f;
                    border-color: #303030;
                }
            }
        }

        &.loading-table_skeleton {
            .table-skeleton-container {
                background: #1f1f1f;
                border-color: #303030;

                .table-skeleton {
                    .table-skeleton-header {
                        background: #262626;
                        border-bottom-color: #303030;

                        .ant-skeleton-input {
                            background: linear-gradient(90deg, #303030 25%, #434343 50%, #303030 75%);
                        }
                    }

                    .table-skeleton-body {
                        .table-skeleton-row {
                            border-bottom-color: #303030;

                            .ant-skeleton {
                                .ant-skeleton-content {
                                    .ant-skeleton-paragraph {
                                        >li {
                                            background: linear-gradient(90deg, #303030 25%, #434343 50%, #303030 75%);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        &.loading-custom {
            .custom-loading-container {
                background: #1f1f1f;
                border-color: #303030;

                .custom-loading-content {
                    .loading-tip {
                        color: #8c8c8c;
                    }
                }
            }
        }

        &.empty-state {
            background: #1f1f1f;
            border-color: #303030;

            .ant-empty {
                .ant-empty-description {
                    color: #8c8c8c;
                }
            }
        }
    }

    // 高对比度模式
    @media (prefers-contrast: high) {
        &.loading-spin {
            .spin-container {
                border: 2px solid #000;
            }
        }

        &.loading-skeleton {
            .skeleton-container {
                border-color: #000;
                border-width: 2px;
            }
        }

        &.loading-card_skeleton {
            .card-skeleton-container {
                .card-skeleton {
                    border-color: #000;
                    border-width: 2px;
                }
            }
        }

        &.loading-table_skeleton {
            .table-skeleton-container {
                border-color: #000;
                border-width: 2px;

                .table-skeleton {
                    .table-skeleton-header {
                        border-bottom-color: #000;
                        border-bottom-width: 2px;
                    }

                    .table-skeleton-body {
                        .table-skeleton-row {
                            border-bottom-color: #000;
                        }
                    }
                }
            }
        }

        &.loading-custom {
            .custom-loading-container {
                border-color: #000;
                border-width: 2px;
            }
        }

        &.empty-state {
            border-color: #000;
            border-width: 2px;
        }
    }

    // 减少动画模式
    @media (prefers-reduced-motion: reduce) {
        .ant-spin-dot {
            animation-duration: 0.01ms !important;
        }

        @keyframes skeleton-loading {

            0%,
            100% {
                background-position: 0 0;
            }
        }

        @keyframes spin {

            0%,
            100% {
                transform: rotate(0deg);
            }
        }
    }

    // 打印样式
    @media print {

        &.loading-spin,
        &.loading-skeleton,
        &.loading-card_skeleton,
        &.loading-table_skeleton,
        &.loading-custom {
            display: none;
        }

        &.empty-state {
            .ant-empty {
                .ant-empty-image {
                    display: none;
                }
            }
        }
    }
}