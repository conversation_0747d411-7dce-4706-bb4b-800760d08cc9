import React from 'react';
import { Spin, Skeleton, Card, Empty } from 'antd';
import { LoadingOutlined } from '@ant-design/icons';
import './index.less';

// 加载状态类型
export enum LoadingType {
    SPIN = 'spin',
    SKELETON = 'skeleton',
    CARD_SKELETON = 'card_skeleton',
    TABLE_SKELETON = 'table_skeleton',
    CUSTOM = 'custom',
}

// 加载状态属性
export interface LoadingStateProps {
    loading: boolean;
    type?: LoadingType;
    size?: 'small' | 'default' | 'large';
    tip?: string;
    delay?: number;
    children?: React.ReactNode;
    height?: number | string;
    rows?: number;
    avatar?: boolean;
    title?: boolean;
    paragraph?: boolean;
    active?: boolean;
    className?: string;
    style?: React.CSSProperties;
    emptyDescription?: string;
    showEmpty?: boolean;
}

/**
 * 自定义加载图标
 */
const CustomLoadingIcon = () => (
    <LoadingOutlined
        style={{
            fontSize: 24,
            color: '#1890ff',
            animation: 'spin 1s linear infinite'
        }}
        spin
    />
);

/**
 * 表格骨架屏
 */
const TableSkeleton: React.FC<{ rows?: number; active?: boolean }> = ({
    rows = 5,
    active = true
}) => (
    <div className="table-skeleton">
        {/* 表格头部 */}
        <div className="table-skeleton-header">
            <Skeleton.Input
                style={{ width: '100%', height: 40 }}
                active={active}
                size="default"
            />
        </div>

        {/* 表格行 */}
        <div className="table-skeleton-body">
            {Array.from({ length: rows }, (_, index) => (
                <div key={index} className="table-skeleton-row">
                    <Skeleton
                        active={active}
                        avatar={false}
                        title={false}
                        paragraph={{
                            rows: 1,
                            width: ['20%', '30%', '25%', '15%', '10%']
                        }}
                    />
                </div>
            ))}
        </div>
    </div>
);

/**
 * 卡片骨架屏
 */
const CardSkeleton: React.FC<{
    rows?: number;
    active?: boolean;
    avatar?: boolean;
    title?: boolean;
}> = ({
    rows = 3,
    active = true,
    avatar = true,
    title = true
}) => (
        <Card className="card-skeleton">
            <Skeleton
                loading={true}
                active={active}
                avatar={avatar}
                title={title}
                paragraph={{ rows }}
            />
        </Card>
    );

/**
 * 加载状态组件
 */
const LoadingState: React.FC<LoadingStateProps> = ({
    loading,
    type = LoadingType.SPIN,
    size = 'default',
    tip = '加载中...',
    delay = 0,
    children,
    height = 200,
    rows = 3,
    avatar = true,
    title = true,
    paragraph = true,
    active = true,
    className = '',
    style = {},
    emptyDescription = '暂无数据',
    showEmpty = false,
}) => {
    // 如果不在加载状态
    if (!loading) {
        // 如果有子组件，显示子组件
        if (children) {
            return <>{children}</>;
        }

        // 如果需要显示空状态
        if (showEmpty) {
            return (
                <div className={`loading-state empty-state ${className}`} style={style}>
                    <Empty
                        description={emptyDescription}
                        style={{ padding: '40px 0' }}
                    />
                </div>
            );
        }

        return null;
    }

    // 根据类型渲染不同的加载状态
    const renderLoadingContent = () => {
        switch (type) {
            case LoadingType.SKELETON:
                return (
                    <div className="skeleton-container" style={{ height }}>
                        <Skeleton
                            loading={true}
                            active={active}
                            avatar={avatar}
                            title={title}
                            paragraph={paragraph ? { rows } : false}
                        />
                    </div>
                );

            case LoadingType.CARD_SKELETON:
                return (
                    <div className="card-skeleton-container">
                        <CardSkeleton
                            rows={rows}
                            active={active}
                            avatar={avatar}
                            title={title}
                        />
                    </div>
                );

            case LoadingType.TABLE_SKELETON:
                return (
                    <div className="table-skeleton-container">
                        <TableSkeleton rows={rows} active={active} />
                    </div>
                );

            case LoadingType.CUSTOM:
                return (
                    <div
                        className="custom-loading-container"
                        style={{ height, ...style }}
                    >
                        <div className="custom-loading-content">
                            <CustomLoadingIcon />
                            {tip && <div className="loading-tip">{tip}</div>}
                        </div>
                    </div>
                );

            case LoadingType.SPIN:
            default:
                return (
                    <div
                        className="spin-container"
                        style={{ height, ...style }}
                    >
                        <Spin
                            size={size}
                            tip={tip}
                            delay={delay}
                            indicator={<CustomLoadingIcon />}
                        />
                    </div>
                );
        }
    };

    return (
        <div className={`loading-state loading-${type} ${className}`}>
            {renderLoadingContent()}
        </div>
    );
};

/**
 * 高阶组件：为组件添加加载状态
 */
export const withLoadingState = <P extends object>(
    WrappedComponent: React.ComponentType<P>,
    loadingProps?: Partial<LoadingStateProps>
) => {
    const WithLoadingStateComponent = (props: P & { loading?: boolean }) => {
        const { loading = false, ...restProps } = props;

        return (
            <LoadingState loading={loading} {...loadingProps}>
                <WrappedComponent {...(restProps as P)} />
            </LoadingState>
        );
    };

    WithLoadingStateComponent.displayName = `withLoadingState(${WrappedComponent.displayName || WrappedComponent.name})`;

    return WithLoadingStateComponent;
};

/**
 * Hook：管理加载状态
 */
export const useLoadingState = (initialLoading = false) => {
    const [loading, setLoading] = React.useState(initialLoading);
    const [error, setError] = React.useState<Error | null>(null);

    const startLoading = React.useCallback(() => {
        setLoading(true);
        setError(null);
    }, []);

    const stopLoading = React.useCallback(() => {
        setLoading(false);
    }, []);

    const setLoadingError = React.useCallback((error: Error) => {
        setLoading(false);
        setError(error);
    }, []);

    const reset = React.useCallback(() => {
        setLoading(false);
        setError(null);
    }, []);

    return {
        loading,
        error,
        startLoading,
        stopLoading,
        setLoadingError,
        reset,
    };
};

/**
 * 异步操作加载包装器
 */
export const withAsyncLoading = <T extends (...args: any[]) => Promise<any>>(
    asyncFn: T,
    setLoading: (loading: boolean) => void
): T => {
    return (async (...args: Parameters<T>) => {
        setLoading(true);
        try {
            const result = await asyncFn(...args);
            return result;
        } finally {
            setLoading(false);
        }
    }) as T;
};

export default LoadingState;