// 全局错误处理组件样式
.global-error-handler {
    position: relative;
    width: 100%;
    height: 100%;

    // 确保子组件能够正常显示
    >* {
        position: relative;
        z-index: 1;
    }
}

// 全局错误提示样式覆盖
.ant-message {
    z-index: 2000;

    .ant-message-notice {
        .ant-message-notice-content {
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

            .ant-message-custom-content {
                display: flex;
                align-items: center;
                gap: 8px;

                .anticon {
                    font-size: 16px;
                }
            }
        }

        &.ant-message-notice-error {
            .ant-message-notice-content {
                background: #fff2f0;
                border: 1px solid #ffccc7;
                color: #cf1322;
            }
        }

        &.ant-message-notice-warning {
            .ant-message-notice-content {
                background: #fffbe6;
                border: 1px solid #ffe58f;
                color: #d48806;
            }
        }

        &.ant-message-notice-success {
            .ant-message-notice-content {
                background: #f6ffed;
                border: 1px solid #b7eb8f;
                color: #389e0d;
            }
        }
    }
}

.ant-notification {
    z-index: 2000;

    .ant-notification-notice {
        border-radius: 8px;
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);

        .ant-notification-notice-icon {
            font-size: 20px;
            margin-top: 2px;
        }

        .ant-notification-notice-message {
            font-weight: 600;
            color: #262626;
            margin-bottom: 4px;
        }

        .ant-notification-notice-description {
            color: #595959;
            line-height: 1.5;
        }

        &.ant-notification-notice-error {
            border-left: 4px solid #ff4d4f;

            .ant-notification-notice-icon {
                color: #ff4d4f;
            }
        }

        &.ant-notification-notice-warning {
            border-left: 4px solid #faad14;

            .ant-notification-notice-icon {
                color: #faad14;
            }
        }

        &.ant-notification-notice-success {
            border-left: 4px solid #52c41a;

            .ant-notification-notice-icon {
                color: #52c41a;
            }
        }

        &.ant-notification-notice-info {
            border-left: 4px solid #1890ff;

            .ant-notification-notice-icon {
                color: #1890ff;
            }
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .ant-message {
        .ant-message-notice {
            .ant-message-notice-content {
                .ant-message-custom-content {
                    font-size: 13px;

                    .anticon {
                        font-size: 14px;
                    }
                }
            }
        }
    }

    .ant-notification {
        .ant-notification-notice {
            margin: 8px;
            max-width: calc(100vw - 16px);

            .ant-notification-notice-message {
                font-size: 14px;
            }

            .ant-notification-notice-description {
                font-size: 13px;
            }
        }
    }
}

@media (max-width: 480px) {
    .ant-message {
        .ant-message-notice {
            .ant-message-notice-content {
                .ant-message-custom-content {
                    font-size: 12px;

                    .anticon {
                        font-size: 13px;
                    }
                }
            }
        }
    }

    .ant-notification {
        .ant-notification-notice {
            margin: 4px;
            max-width: calc(100vw - 8px);

            .ant-notification-notice-icon {
                font-size: 18px;
            }

            .ant-notification-notice-message {
                font-size: 13px;
            }

            .ant-notification-notice-description {
                font-size: 12px;
            }
        }
    }
}

// 暗色主题支持
.dark {
    .ant-message {
        .ant-message-notice {
            &.ant-message-notice-error {
                .ant-message-notice-content {
                    background: #2a1215;
                    border-color: #58181c;
                    color: #ff7875;
                }
            }

            &.ant-message-notice-warning {
                .ant-message-notice-content {
                    background: #2b2111;
                    border-color: #613400;
                    color: #ffc53d;
                }
            }

            &.ant-message-notice-success {
                .ant-message-notice-content {
                    background: #162312;
                    border-color: #274916;
                    color: #73d13d;
                }
            }
        }
    }

    .ant-notification {
        .ant-notification-notice {
            background: #1f1f1f;
            border-color: #303030;

            .ant-notification-notice-message {
                color: #d9d9d9;
            }

            .ant-notification-notice-description {
                color: #8c8c8c;
            }

            &.ant-notification-notice-error {
                border-left-color: #ff7875;
            }

            &.ant-notification-notice-warning {
                border-left-color: #ffc53d;
            }

            &.ant-notification-notice-success {
                border-left-color: #73d13d;
            }

            &.ant-notification-notice-info {
                border-left-color: #40a9ff;
            }
        }
    }
}

// 高对比度模式
@media (prefers-contrast: high) {
    .ant-message {
        .ant-message-notice {
            .ant-message-notice-content {
                border-width: 2px;
                font-weight: 600;
            }

            &.ant-message-notice-error {
                .ant-message-notice-content {
                    background: #fff;
                    border-color: #ff0000;
                    color: #ff0000;
                }
            }

            &.ant-message-notice-warning {
                .ant-message-notice-content {
                    background: #fff;
                    border-color: #ff8c00;
                    color: #ff8c00;
                }
            }

            &.ant-message-notice-success {
                .ant-message-notice-content {
                    background: #fff;
                    border-color: #008000;
                    color: #008000;
                }
            }
        }
    }

    .ant-notification {
        .ant-notification-notice {
            border-width: 2px;

            .ant-notification-notice-message,
            .ant-notification-notice-description {
                color: #000;
                font-weight: 600;
            }
        }
    }
}

// 减少动画模式
@media (prefers-reduced-motion: reduce) {
    .ant-message {
        .ant-message-notice {
            transition: none;
            animation: none;
        }
    }

    .ant-notification {
        .ant-notification-notice {
            transition: none;
            animation: none;
        }
    }
}