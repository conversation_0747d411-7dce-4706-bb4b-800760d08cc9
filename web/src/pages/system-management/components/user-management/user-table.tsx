import {
  CrownOutlined,
  EditOutlined,
  FilterOutlined,
  ReloadOutlined,
  SearchOutlined,
  UserOutlined,
} from '@ant-design/icons';
import {
  Badge,
  Button,
  Input,
  Popconfirm,
  Select,
  Space,
  Table,
  Tag,
  Tooltip,
  Typography,
} from 'antd';
import type { ColumnsType } from 'antd/es/table';
import React, { useMemo } from 'react';
import {
  CONFIRM_MESSAGES,
  PLACEHOLDER_TEXTS,
  TABLE_CONFIG,
  USER_STATUS_OPTIONS,
} from '../../constants';
import { IPagination, IUser } from '../../types';
import { formatTimestamp, getUserStatusInfo, isSuperuser } from '../../utils';

const { Search } = Input;
const { Option } = Select;
const { Text } = Typography;

interface UserTableProps {
  users: IUser[];
  loading: boolean;
  pagination: IPagination;
  filters: {
    search: string;
    status: 'all' | '1' | '0';
  };
  onSearch: (search: string) => void;
  onStatusFilter: (status: 'all' | '1' | '0') => void;
  onPageChange: (page: number, pageSize?: number) => void;
  onEditUser: (user: IUser) => void;
  onToggleUserStatus: (user: IUser) => void;
  onViewTenantDetails: (user: IUser) => void;
  onViewTeamDetails: (user: IUser) => void;
  onRefresh?: () => void;
}

/**
 * 用户管理表格组件
 */
const UserTable: React.FC<UserTableProps> = ({
  users = [], // 添加默认值
  loading,
  pagination,
  filters,
  onSearch,
  onStatusFilter,
  onPageChange,
  onEditUser,
  onToggleUserStatus,
  onViewTenantDetails,
  onViewTeamDetails,
  onRefresh,
}) => {
  // 确保users是数组
  const safeUsers = Array.isArray(users) ? users : [];
  // 表格列定义 - 根据需求只显示指定字段
  const columns: ColumnsType<IUser> = useMemo(
    () => [
      {
        title: '序号',
        key: 'index',
        width: 80,
        align: 'center',
        fixed: 'left', // 固定序号列
        render: (_, __, index) =>
          (pagination.current - 1) * pagination.pageSize + index + 1,
      },
      {
        title: '用户名称',
        dataIndex: 'nickname',
        key: 'nickname',
        width: 150,
        ellipsis: true,
        fixed: 'left', // 固定用户名称列
        render: (nickname: string, record: IUser) => (
          <Space>
            <UserOutlined />
            <span>{nickname || '未设置'}</span>
            {isSuperuser(record) && (
              <Tooltip title="超级用户">
                <CrownOutlined style={{ color: '#faad14' }} />
              </Tooltip>
            )}
          </Space>
        ),
      },
      {
        title: '邮箱',
        dataIndex: 'email',
        key: 'email',
        width: 200,
        ellipsis: true,
        render: (email: string) => (
          <Tooltip title={email}>
            <Text copyable={{ text: email }}>{email}</Text>
          </Tooltip>
        ),
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        width: 100,
        align: 'center',
        render: (status: '1' | '0') => {
          const statusInfo = getUserStatusInfo(status);
          return (
            <Badge
              status={status === '1' ? 'success' : 'error'}
              text={statusInfo.text}
            />
          );
        },
      },
      {
        title: '超级用户',
        dataIndex: 'is_superuser',
        key: 'is_superuser',
        width: 100,
        align: 'center',
        render: (is_superuser: boolean) => (
          <Tag color={is_superuser ? 'gold' : 'default'}>
            {is_superuser ? '是' : '否'}
          </Tag>
        ),
      },
      {
        title: '创建时间',
        dataIndex: 'create_time',
        key: 'create_time',
        width: 160,
        render: (create_time: number) => (
          <Tooltip title={formatTimestamp(create_time)}>
            <Text type="secondary">{formatTimestamp(create_time)}</Text>
          </Tooltip>
        ),
      },
      {
        title: '更新时间',
        dataIndex: 'update_time',
        key: 'update_time',
        width: 160,
        minWidth: 160,
        maxWidth: 160,
        ellipsis: false,
        render: (update_time: number) => (
          <Tooltip title={formatTimestamp(update_time)}>
            <Text type="secondary">{formatTimestamp(update_time)}</Text>
          </Tooltip>
        ),
      },
      {
        title: '操作',
        key: 'actions',
        width: 220,
        minWidth: 220,
        maxWidth: 220,
        fixed: 'right',
        ellipsis: false,
        render: (_, record: IUser) => (
          <Space
            size="small"
            style={{
              display: 'flex',
              justifyContent: 'flex-start',
              flexWrap: 'nowrap',
            }}
          >
            <Tooltip title="编辑用户">
              <Button
                type="link"
                size="small"
                icon={<EditOutlined />}
                onClick={() => onEditUser(record)}
                style={{ padding: '0 4px', minWidth: 'auto' }}
              >
                编辑
              </Button>
            </Tooltip>

            <Tooltip title="租户详情">
              <Button
                type="link"
                size="small"
                onClick={() => onViewTenantDetails(record)}
                style={{ padding: '0 4px', minWidth: 'auto' }}
              >
                租户
              </Button>
            </Tooltip>

            <Tooltip title="团队详情">
              <Button
                type="link"
                size="small"
                onClick={() => onViewTeamDetails(record)}
                style={{ padding: '0 4px', minWidth: 'auto' }}
              >
                团队
              </Button>
            </Tooltip>

            <Popconfirm
              title={
                record.status === '1'
                  ? CONFIRM_MESSAGES.DISABLE_USER
                  : CONFIRM_MESSAGES.ENABLE_USER
              }
              description={CONFIRM_MESSAGES.WARNING}
              onConfirm={() => onToggleUserStatus(record)}
              okText="确认"
              cancelText="取消"
              okType={record.status === '1' ? 'danger' : 'primary'}
            >
              <Button
                type="link"
                size="small"
                danger={record.status === '1'}
                style={{ padding: '0 4px', minWidth: 'auto' }}
              >
                {record.status === '1' ? '禁用' : '激活'}
              </Button>
            </Popconfirm>
          </Space>
        ),
      },
    ],
    [onEditUser, onToggleUserStatus],
  );

  // 渲染搜索和筛选栏
  const renderSearchBar = () => (
    <div className="search-bar">
      <Space size="middle" wrap>
        <Input
          placeholder={PLACEHOLDER_TEXTS.SEARCH_USERS}
          value={filters.search}
          style={{ width: 220 }}
          allowClear
          onChange={(e) => onSearch(e.target.value)}
          prefix={<SearchOutlined />}
        />

        <Select
          placeholder={PLACEHOLDER_TEXTS.SELECT_STATUS}
          value={filters.status}
          onChange={onStatusFilter}
          className="filter-select"
          style={{ width: 120 }}
          suffixIcon={<FilterOutlined />}
        >
          {USER_STATUS_OPTIONS?.map?.((option) => (
            <Option key={option.value} value={option.value}>
              {option.label}
            </Option>
          )) || []}
        </Select>

        {onRefresh && (
          <Tooltip title="刷新数据">
            <Button
              icon={<ReloadOutlined />}
              onClick={onRefresh}
              loading={loading}
            >
              刷新
            </Button>
          </Tooltip>
        )}
      </Space>
    </div>
  );

  return (
    <div
      className="user-management-table"
      style={{ height: '100%', display: 'flex', flexDirection: 'column' }}
    >
      {renderSearchBar()}
      <div
        className="table-container"
        style={{
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          minHeight: 0,
        }}
      >
        <Table<IUser>
          columns={columns}
          dataSource={safeUsers}
          loading={loading}
          rowKey={TABLE_CONFIG.ROW_KEY}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条`,
            pageSizeOptions: TABLE_CONFIG.PAGE_SIZE_OPTIONS?.map?.(String) || [
              '10',
              '20',
              '50',
              '100',
            ],
            onChange: onPageChange,
            onShowSizeChange: onPageChange,
          }}
          scroll={{
            x: 1200,
            y: 'calc(100vh - 240px)', // 调整高度计算，减少留白
          }}
          size="middle"
          bordered
          style={{ height: '100%' }}
        />
      </div>
    </div>
  );
};

export default UserTable;
