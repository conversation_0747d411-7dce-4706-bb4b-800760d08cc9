import React, { useState, useEffect } from 'react';
import { Modal, Table, Typography, Space, Tag, Spin, message } from 'antd';
import { UserOutlined, ClockCircleOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { formatTimestamp } from '../../utils';
import SystemManagementService from '@/services/system-management-service';

const { Text } = Typography;

interface TenantDetailsModalProps {
    visible: boolean;
    user: any | null;
    onCancel: () => void;
}

interface TenantUser {
    id: string;
    tenant_id: string;
    nickname: string;
    email: string;
    status: string;
    join_time: number;
}

const TenantDetailsModal: React.FC<TenantDetailsModalProps> = ({
    visible,
    user,
    onCancel,
}) => {
    const [loading, setLoading] = useState(false);
    const [tenantUsers, setTenantUsers] = useState<TenantUser[]>([]);
    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 10,
        total: 0,
    });

    // 获取租户详情数据
    const fetchTenantDetails = async (page = 1, size = 10) => {
        if (!user?.id) return;

        setLoading(true);
        try {
            const response = await SystemManagementService.getUserTenantDetails(user.id, {
                page,
                size,
            });
            
            setTenantUsers(response.tenants || []);
            setPagination({
                current: response.page || 1,
                pageSize: response.size || 10,
                total: response.total || 0,
            });
        } catch (error) {
            message.error('获取租户详情失败');
            console.error('Failed to fetch tenant details:', error);
        } finally {
            setLoading(false);
        }
    };

    // 当模态框打开且用户信息存在时获取数据
    useEffect(() => {
        if (visible && user?.id) {
            fetchTenantDetails();
        }
    }, [visible, user?.id]);

    // 表格列定义
    const columns: ColumnsType<TenantUser> = [
        {
            title: '序号',
            key: 'index',
            width: 80,
            align: 'center',
            render: (_, __, index) => (pagination.current - 1) * pagination.pageSize + index + 1,
        },
        {
            title: '用户名称',
            dataIndex: 'nickname',
            key: 'nickname',
            width: 150,
            render: (nickname: string) => (
                <Space>
                    <UserOutlined />
                    <span>{nickname || '未设置'}</span>
                </Space>
            ),
        },
        {
            title: '邮箱',
            dataIndex: 'email',
            key: 'email',
            width: 200,
            ellipsis: true,
        },
        {
            title: '状态',
            dataIndex: 'status',
            key: 'status',
            width: 100,
            align: 'center',
            render: (status: string) => (
                <Tag color={status === '1' ? 'success' : 'error'}>
                    {status === '1' ? '激活' : '禁用'}
                </Tag>
            ),
        },
        {
            title: '加入时间',
            dataIndex: 'join_time',
            key: 'join_time',
            width: 180,
            render: (join_time: number) => (
                <Space>
                    <ClockCircleOutlined />
                    <Text type="secondary">
                        {formatTimestamp(join_time)}
                    </Text>
                </Space>
            ),
        },
    ];

    // 处理分页变化
    const handleTableChange = (page: number, pageSize?: number) => {
        fetchTenantDetails(page, pageSize || pagination.pageSize);
    };

    return (
        <Modal
            title={
                <Space>
                    <UserOutlined />
                    <span>租户详情 - {user?.nickname || user?.email}</span>
                </Space>
            }
            open={visible}
            onCancel={onCancel}
            footer={null}
            width={800}
            destroyOnClose
        >
            <div style={{ marginBottom: 16 }}>
                <Text type="secondary">
                    显示该用户作为租户owner时，其租户团队下的所有成员信息
                </Text>
            </div>
            
            <Spin spinning={loading}>
                <Table
                    columns={columns}
                    dataSource={tenantUsers}
                    rowKey="id"
                    pagination={{
                        current: pagination.current,
                        pageSize: pagination.pageSize,
                        total: pagination.total,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                        onChange: handleTableChange,
                        onShowSizeChange: handleTableChange,
                    }}
                    size="small"
                    locale={{
                        emptyText: '该用户的租户团队中暂无其他成员',
                    }}
                />
            </Spin>
        </Modal>
    );
};

export default TenantDetailsModal; 