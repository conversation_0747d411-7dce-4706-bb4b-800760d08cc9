import React, { useState, useEffect } from 'react';
import { Modal, Table, Typography, Space, Tag, Spin, message } from 'antd';
import { TeamOutlined, UserOutlined, ClockCircleOutlined, CrownOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { formatTimestamp } from '../../utils';
import SystemManagementService from '@/services/system-management-service';

const { Text } = Typography;

interface TeamDetailsModalProps {
    visible: boolean;
    user: any | null;
    onCancel: () => void;
}

interface TeamInfo {
    team_name: string;
    role: string;
    team_member_count: number;
    team_create_time: number;
    join_time: number;
    tenant_id: string;
}

const TeamDetailsModal: React.FC<TeamDetailsModalProps> = ({
    visible,
    user,
    onCancel,
}) => {
    const [loading, setLoading] = useState(false);
    const [teams, setTeams] = useState<TeamInfo[]>([]);
    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 10,
        total: 0,
    });

    // 获取团队详情数据
    const fetchTeamDetails = async (page = 1, size = 10) => {
        if (!user?.id) return;

        setLoading(true);
        try {
            const response = await SystemManagementService.getUserTeamDetails(user.id, {
                page,
                size,
            });
            
            setTeams(response.teams || []);
            setPagination({
                current: response.page || 1,
                pageSize: response.size || 10,
                total: response.total || 0,
            });
        } catch (error) {
            message.error('获取团队详情失败');
            console.error('Failed to fetch team details:', error);
        } finally {
            setLoading(false);
        }
    };

    // 当模态框打开且用户信息存在时获取数据
    useEffect(() => {
        if (visible && user?.id) {
            fetchTeamDetails();
        }
    }, [visible, user?.id]);

    // 角色映射
    const getRoleTag = (role: string) => {
        const roleMap: Record<string, { text: string; color: string }> = {
            'owner': { text: '拥有者', color: 'gold' },
            'admin': { text: '管理员', color: 'blue' },
            'normal': { text: '普通成员', color: 'default' },
        };
        
        const roleInfo = roleMap[role] || { text: role, color: 'default' };
        
        return (
            <Tag color={roleInfo.color} icon={role === 'owner' ? <CrownOutlined /> : <UserOutlined />}>
                {roleInfo.text}
            </Tag>
        );
    };

    // 表格列定义
    const columns: ColumnsType<TeamInfo> = [
        {
            title: '序号',
            key: 'index',
            width: 80,
            align: 'center',
            render: (_, __, index) => (pagination.current - 1) * pagination.pageSize + index + 1,
        },
        {
            title: '团队名称',
            dataIndex: 'team_name',
            key: 'team_name',
            width: 200,
            render: (team_name: string) => (
                <Space>
                    <TeamOutlined />
                    <span>{team_name || '未命名团队'}</span>
                </Space>
            ),
        },
        {
            title: '角色',
            dataIndex: 'role',
            key: 'role',
            width: 120,
            align: 'center',
            render: (role: string) => getRoleTag(role),
        },
        {
            title: '团队总人数',
            dataIndex: 'team_member_count',
            key: 'team_member_count',
            width: 120,
            align: 'center',
            render: (count: number) => (
                <Space>
                    <UserOutlined />
                    <span>{count || 0} 人</span>
                </Space>
            ),
        },
        {
            title: '团队创建时间',
            dataIndex: 'team_create_time',
            key: 'team_create_time',
            width: 180,
            render: (create_time: number) => (
                <Space>
                    <ClockCircleOutlined />
                    <Text type="secondary">
                        {formatTimestamp(create_time)}
                    </Text>
                </Space>
            ),
        },
        {
            title: '加入时间',
            dataIndex: 'join_time',
            key: 'join_time',
            width: 180,
            render: (join_time: number) => (
                <Space>
                    <ClockCircleOutlined />
                    <Text type="secondary">
                        {formatTimestamp(join_time)}
                    </Text>
                </Space>
            ),
        },
    ];

    // 处理分页变化
    const handleTableChange = (page: number, pageSize?: number) => {
        fetchTeamDetails(page, pageSize || pagination.pageSize);
    };

    return (
        <Modal
            title={
                <Space>
                    <TeamOutlined />
                    <span>团队详情 - {user?.nickname || user?.email}</span>
                </Space>
            }
            open={visible}
            onCancel={onCancel}
            footer={null}
            width={1000}
            destroyOnClose
        >
            <div style={{ marginBottom: 16 }}>
                <Text type="secondary">
                    显示该用户作为成员加入的所有团队信息（不包括自己作为owner的团队）
                </Text>
            </div>
            
            <Spin spinning={loading}>
                <Table
                    columns={columns}
                    dataSource={teams}
                    rowKey="tenant_id"
                    pagination={{
                        current: pagination.current,
                        pageSize: pagination.pageSize,
                        total: pagination.total,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条`,
                        onChange: handleTableChange,
                        onShowSizeChange: handleTableChange,
                    }}
                    size="small"
                    locale={{
                        emptyText: '该用户暂未加入任何团队',
                    }}
                />
            </Spin>
        </Modal>
    );
};

export default TeamDetailsModal; 