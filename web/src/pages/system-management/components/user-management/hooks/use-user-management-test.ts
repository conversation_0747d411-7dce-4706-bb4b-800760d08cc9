import { useState, useCallback } from 'react';
import {
    IUserManagementState,
    IUser,
    IUpdateUserRequest,
    IUseUserManagementReturn
} from '../../../types';
import { DEFAULT_PAGE_SIZE } from '../../../constants';

/**
 * 用户管理测试Hook - 使用模拟数据
 * 用于开发和测试阶段
 */
export const useUserManagementTest = (): IUseUserManagementReturn => {
    const [state, setState] = useState<IUserManagementState>({
        users: [
            {
                id: '1',
                nickname: '测试用户1',
                email: '<EMAIL>',
                status: '1',
                is_superuser: false,
                create_time: Date.now() / 1000,
                update_time: Date.now() / 1000,
                create_date: new Date().toLocaleDateString(),
                update_date: new Date().toLocaleDateString(),
            },
            {
                id: '2',
                nickname: '测试用户2',
                email: '<EMAIL>',
                status: '0',
                is_superuser: true,
                create_time: Date.now() / 1000,
                update_time: Date.now() / 1000,
                create_date: new Date().toLocaleDateString(),
                update_date: new Date().toLocaleDateString(),
            },
        ],
        loading: false,
        pagination: {
            current: 1,
            pageSize: DEFAULT_PAGE_SIZE,
            total: 2,
        },
        filters: {
            search: '',
            status: 'all',
        },
        selectedUser: null,
        editModalVisible: false,
    });

    const fetchUsers = useCallback(async () => {
        setState(prev => ({ ...prev, loading: true }));
        // 模拟API调用延迟
        setTimeout(() => {
            setState(prev => ({ ...prev, loading: false }));
        }, 1000);
    }, []);

    const handleSearch = useCallback((search: string) => {
        setState(prev => ({
            ...prev,
            filters: { ...prev.filters, search },
            pagination: { ...prev.pagination, current: 1 },
        }));
    }, []);

    const handleStatusFilter = useCallback((status: 'all' | '1' | '0') => {
        setState(prev => ({
            ...prev,
            filters: { ...prev.filters, status },
            pagination: { ...prev.pagination, current: 1 },
        }));
    }, []);

    const handlePageChange = useCallback((page: number, pageSize?: number) => {
        setState(prev => ({
            ...prev,
            pagination: {
                ...prev.pagination,
                current: page,
                pageSize: pageSize || prev.pagination.pageSize,
            },
        }));
    }, []);

    const handleEditUser = useCallback((user: IUser) => {
        setState(prev => ({
            ...prev,
            selectedUser: user,
            editModalVisible: true,
        }));
    }, []);

    const handleUpdateUser = useCallback(async (values: IUpdateUserRequest) => {
        if (!state.selectedUser) return;

        // 模拟API调用
        setTimeout(() => {
            setState(prev => ({
                ...prev,
                users: prev.users.map(user =>
                    user.id === prev.selectedUser?.id
                        ? { ...user, ...values }
                        : user
                ),
                selectedUser: null,
                editModalVisible: false,
            }));
        }, 500);
    }, [state.selectedUser]);

    const handleToggleUserStatus = useCallback(async (user: IUser) => {
        const newStatus = user.status === '1' ? '0' : '1';
        setState(prev => ({
            ...prev,
            users: prev.users.map(u =>
                u.id === user.id
                    ? { ...u, status: newStatus }
                    : u
            ),
        }));
    }, []);

    const handleCloseEditModal = useCallback(() => {
        setState(prev => ({
            ...prev,
            selectedUser: null,
            editModalVisible: false,
        }));
    }, []);

    const refreshUsers = useCallback(() => {
        fetchUsers();
    }, [fetchUsers]);

    const resetFilters = useCallback(() => {
        setState(prev => ({
            ...prev,
            filters: {
                search: '',
                status: 'all',
            },
            pagination: { ...prev.pagination, current: 1 },
        }));
    }, []);

    return {
        users: state.users,
        loading: state.loading,
        pagination: state.pagination,
        filters: state.filters,
        selectedUser: state.selectedUser,
        editModalVisible: state.editModalVisible,
        fetchUsers,
        handleSearch,
        handleStatusFilter,
        handlePageChange,
        handleEditUser,
        handleUpdateUser,
        handleToggleUserStatus,
        handleCloseEditModal,
        refreshUsers,
        resetFilters,
    };
};