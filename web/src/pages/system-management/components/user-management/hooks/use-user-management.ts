import { useState, useCallback, useEffect, useRef } from 'react';
import { message } from 'antd';
import {
    IUserManagementState,
    IUser,
    IUpdateUserRequest,
    IUseUserManagementReturn,
    IUserSearchParams
} from '../../../types';
import {
    DEFAULT_PAGE_SIZE,
    SEARCH_DEBOUNCE_DELAY,
    SUCCESS_MESSAGES,
    STORAGE_KEYS
} from '../../../constants';
import { debounce, safeJsonParse, safeJsonStringify } from '../../../utils';
import { useErrorHandler } from '../../../hooks/use-error-handler';
import SystemManagementService from '@/services/system-management-service';

/**
 * 用户管理数据获取和状态管理Hook
 */
export const useUserManagement = (): IUseUserManagementReturn => {
    const { handleError, showSuccess } = useErrorHandler();
    const [state, setState] = useState<IUserManagementState>({
        users: [],
        loading: false,
        pagination: {
            current: 1,
            pageSize: DEFAULT_PAGE_SIZE,
            total: 0,
        },
        filters: {
            search: '',
            status: 'all',
        },
        selectedUser: null,
        editModalVisible: false,
    });

    // 防抖搜索引用
    const debouncedFetchRef = useRef<(() => void) | null>(null);

    // 初始化时从本地存储恢复筛选条件
    useEffect(() => {
        const savedFilters = localStorage.getItem(STORAGE_KEYS.USER_MANAGEMENT_FILTERS);
        if (savedFilters) {
            const filters = safeJsonParse(savedFilters, state.filters);
            setState(prev => ({
                ...prev,
                filters,
            }));
        }
    }, []);

    // 保存筛选条件到本地存储
    const saveFiltersToStorage = useCallback((filters: IUserManagementState['filters']) => {
        localStorage.setItem(STORAGE_KEYS.USER_MANAGEMENT_FILTERS, safeJsonStringify(filters));
    }, []);

    // 获取用户列表
    const fetchUsers = useCallback(async (customParams?: Partial<IUserSearchParams>) => {
        setState(prev => ({ ...prev, loading: true }));

        try {
            // 使用当前状态或传入的自定义参数
            const currentPagination = customParams?.page ?
                { current: customParams.page, pageSize: customParams.size || state.pagination.pageSize } :
                state.pagination;
            const currentFilters = {
                search: customParams?.search !== undefined ? customParams.search : state.filters.search,
                status: customParams?.status !== undefined ? customParams.status : state.filters.status,
            };

            const params = {
                page: currentPagination.current,
                size: currentPagination.pageSize,
                search: currentFilters.search,
                status: currentFilters.status,
            };

            console.log('🔍 [fetchUsers] 请求参数:', params);

            const response = await SystemManagementService.getUserList(params);

            console.log('🔍 [fetchUsers] API响应:', response);
            console.log('🔍 [fetchUsers] 用户数量:', response?.users?.length || 0);

            // 安全检查：确保response和users存在
            if (!response || !Array.isArray(response.users)) {
                console.error('❌ [fetchUsers] Invalid response structure:', response);
                setState(prev => ({
                    ...prev,
                    users: [],
                    loading: false
                }));
                return;
            }

            console.log('✅ [fetchUsers] 开始映射用户数据，用户数量:', response.users.length);

            // 映射后端数据到前端接口
            const mappedUsers: IUser[] = response.users.map((user) => {
                return {
                    id: user.id || '',
                    nickname: user.nickname || '',
                    email: user.email || '',
                    status: user.status || '0',
                    is_superuser: Boolean(user.is_superuser),
                    create_time: user.create_time || 0,
                    update_time: user.update_time || 0,
                    create_date: user.create_date || '',
                    update_date: user.update_date || '',
                };
            });

            console.log('✅ [fetchUsers] 映射完成，最终用户数量:', mappedUsers.length);

            setState(prev => ({
                ...prev,
                users: mappedUsers,
                pagination: {
                    ...prev.pagination,
                    total: response.total || 0,
                    current: response.page || currentPagination.current,
                    pageSize: response.size || currentPagination.pageSize,
                },
                loading: false,
            }));

            console.log('✅ [fetchUsers] 状态更新完成');
        } catch (error) {
            console.error('❌ [fetchUsers] Failed to fetch users:', error);
            handleError(error);
            setState(prev => ({
                ...prev,
                users: [],
                loading: false
            }));
        }
    }, [state.pagination, state.filters, handleError]);

    // 防抖获取用户列表
    const debouncedFetchUsers = useCallback(
        debounce((params?: Partial<IUserSearchParams>) => {
            fetchUsers(params);
        }, SEARCH_DEBOUNCE_DELAY),
        [fetchUsers]
    );

    // 初始化时获取用户列表
    useEffect(() => {
        fetchUsers();
    }, []);

    // 分页变化时获取数据
    useEffect(() => {
        fetchUsers();
    }, [state.pagination.current, state.pagination.pageSize]);

    // 处理搜索
    const handleSearch = useCallback((search: string) => {
        const newFilters = { ...state.filters, search };
        const newPagination = { ...state.pagination, current: 1 };

        setState(prev => ({
            ...prev,
            filters: newFilters,
            pagination: newPagination,
        }));

        saveFiltersToStorage(newFilters);

        // 立即使用新参数获取数据
        debouncedFetchUsers({
            page: 1,
            size: state.pagination.pageSize,
            search: search,
            status: state.filters.status,
        });
    }, [state.filters, state.pagination.pageSize, saveFiltersToStorage, debouncedFetchUsers]);

    // 处理状态筛选
    const handleStatusFilter = useCallback((status: 'all' | '1' | '0') => {
        const newFilters = { ...state.filters, status };
        const newPagination = { ...state.pagination, current: 1 };

        setState(prev => ({
            ...prev,
            filters: newFilters,
            pagination: newPagination,
        }));

        saveFiltersToStorage(newFilters);

        // 立即使用新参数获取数据
        fetchUsers({
            page: 1,
            size: state.pagination.pageSize,
            search: state.filters.search,
            status: status,
        });
    }, [state.filters, state.pagination.pageSize, saveFiltersToStorage, fetchUsers]);

    // 处理分页变化
    const handlePageChange = useCallback((page: number, pageSize?: number) => {
        const newPageSize = pageSize || state.pagination.pageSize;
        const newPage = pageSize && pageSize !== state.pagination.pageSize ? 1 : page; // 如果改变页面大小，重置到第1页

        setState(prev => ({
            ...prev,
            pagination: {
                ...prev.pagination,
                current: newPage,
                pageSize: newPageSize,
            },
        }));

        // 立即获取数据，避免等待useEffect触发
        setTimeout(() => {
            fetchUsers({
                page: newPage,
                size: newPageSize,
                search: state.filters.search,
                status: state.filters.status,
            });
        }, 0);
    }, [state.pagination.pageSize, state.filters, fetchUsers]);

    // 处理编辑用户
    const handleEditUser = useCallback((user: IUser) => {
        setState(prev => ({
            ...prev,
            selectedUser: user,
            editModalVisible: true,
        }));
    }, []);

    // 处理更新用户信息
    const handleUpdateUser = useCallback(async (values: IUpdateUserRequest) => {
        if (!state.selectedUser) return;

        console.log('🔧 [useUserManagement] 开始更新用户:', {
            userId: state.selectedUser.id,
            values
        });

        try {
            await SystemManagementService.updateUser(state.selectedUser.id, values);

            console.log('🔧 [useUserManagement] 用户更新成功');

            // 只有在成功时才执行这些操作
            // 关闭模态框并刷新列表
            setState(prev => ({
                ...prev,
                selectedUser: null,
                editModalVisible: false,
            }));

            await fetchUsers();
        } catch (error) {
            console.log('🔧 [useUserManagement] 用户更新失败:', error);
            // 不要在这里调用handleError，让页面组件处理
            // handleError(error);
            throw error; // 重新抛出错误让页面组件处理
        }
    }, [state.selectedUser, fetchUsers]);

    // 处理切换用户状态
    const handleToggleUserStatus = useCallback(async (user: IUser) => {
        try {
            const newStatus = user.status === '1' ? '0' : '1';

            // 确保API调用正确传递status参数
            console.log('🔄 [handleToggleUserStatus] 准备更新用户状态:', {
                userId: user.id,
                currentStatus: user.status,
                newStatus: newStatus,
                requestData: { status: newStatus }
            });

            await SystemManagementService.updateUserStatus(user.id, { status: newStatus });
            showSuccess(SUCCESS_MESSAGES.USER_STATUS_UPDATED);
            await fetchUsers();
        } catch (error) {
            console.error('状态切换失败:', error);
            handleError(error);
            throw error; // 重新抛出错误让调用方处理
        }
    }, [showSuccess, handleError, fetchUsers]);

    // 关闭编辑模态框
    const handleCloseEditModal = useCallback(() => {
        setState(prev => ({
            ...prev,
            selectedUser: null,
            editModalVisible: false,
        }));
    }, []);

    // 刷新用户列表
    const refreshUsers = useCallback(async () => {
        await fetchUsers();
    }, [fetchUsers]);

    // 重置筛选条件
    const resetFilters = useCallback(() => {
        const defaultFilters = {
            search: '',
            status: 'all' as const,
        };
        setState(prev => ({
            ...prev,
            filters: defaultFilters,
            pagination: { ...prev.pagination, current: 1 },
        }));
        saveFiltersToStorage(defaultFilters);
    }, [saveFiltersToStorage]);

    return {
        // 状态
        users: state.users,
        loading: state.loading,
        pagination: state.pagination,
        filters: state.filters,
        selectedUser: state.selectedUser,
        editModalVisible: state.editModalVisible,

        // 操作方法
        handleSearch,
        handleStatusFilter,
        handlePageChange,
        handleEditUser,
        handleUpdateUser,
        handleToggleUserStatus,
        handleCloseEditModal,
        refreshUsers,
        resetFilters,
    };
};