import React, { useEffect, useState } from 'react';
import {
    Modal,
    Form,
    Input,
    Button,
    Space,
    Alert,
    Select
} from 'antd';
import {
    UserOutlined,
    MailOutlined,
    CrownOutlined,
    SaveOutlined,
    CloseOutlined,
    CheckCircleOutlined,
    StopOutlined
} from '@ant-design/icons';
import { IUser, IUpdateUserRequest, IUserModalProps } from '../../types';
import { FORM_RULES, MODAL_CONFIG, FORM_CONFIG } from '../../constants';
import { isSuperuser } from '../../utils';

const { Option } = Select;

/**
 * 用户编辑模态框组件
 */
const UserModal: React.FC<IUserModalProps> = ({
    visible,
    user,
    loading,
    onOk,
    onCancel,
}) => {
    const [form] = Form.useForm();
    const [submitLoading, setSubmitLoading] = useState(false);

    // 当用户数据变化时，更新表单
    useEffect(() => {
        // 确保模态框可见、用户数据存在且包含必要字段
        if (visible && user && user.id) {
            console.log('🔧 [UserModal] 设置表单数据:', {
                userId: user.id,
                nickname: user.nickname,
                status: user.status,
                email: user.email,
                emailType: typeof user.email,
                emailLength: user.email ? user.email.length : 0
            });

            // 先重置表单，确保干净的状态
            form.resetFields();

            // 设置表单字段值（邮箱字段现在由Input组件直接管理）
            const formValues = {
                nickname: user.nickname || '',
                status: user.status || '1',
            };

            // 使用 setFieldsValue 设置表单管理的字段
            form.setFieldsValue(formValues);

            // 验证表单值是否设置成功（调试用）
            setTimeout(() => {
            }, 50);
        }
    }, [user, visible, form]);

    // 重置表单
    const resetForm = () => {
        form.resetFields();
        setSubmitLoading(false);
    };

    // 处理模态框关闭
    const handleCancel = () => {
        resetForm();
        onCancel();
    };

    // 处理表单提交
    const handleSubmit = async () => {
        try {
            const values = await form.validateFields();
            setSubmitLoading(true);

            // 只提交允许编辑的字段（昵称和状态），不包含邮箱
            const updateData = {
                nickname: values.nickname,
                status: values.status,
            };
            await onOk(updateData as IUpdateUserRequest);
            // 成功后立即重置状态和关闭模态框
            setSubmitLoading(false);
            resetForm();
        } catch (error) {
            setSubmitLoading(false);
        }
    };

    return (
        <Modal
            title={
                <Space>
                    <UserOutlined />
                    编辑用户信息
                    {user && isSuperuser(user) && (
                        <CrownOutlined style={{ color: '#faad14' }} />
                    )}
                </Space>
            }
            open={visible}
            onCancel={handleCancel}
            width={MODAL_CONFIG.WIDTH.MEDIUM}
            maskClosable={MODAL_CONFIG.MASK_CLOSABLE}
            destroyOnClose={true}
            footer={[
                <Button
                    key="cancel"
                    icon={<CloseOutlined />}
                    onClick={handleCancel}
                >
                    取消
                </Button>,
                <Button
                    key="submit"
                    type="primary"
                    icon={<SaveOutlined />}
                    loading={submitLoading || loading}
                    onClick={handleSubmit}
                >
                    保存
                </Button>,
            ]}
            className="user-edit-modal"
        >
            <div className="user-edit-form">
                {isSuperuser(user) && (
                    <Alert
                        message="注意"
                        description="您正在编辑超级用户的信息，请谨慎操作。"
                        type="warning"
                        showIcon
                        style={{ marginBottom: 16 }}
                    />
                )}

                <Form
                    form={form}
                    layout={FORM_CONFIG.LAYOUT}
                    labelCol={FORM_CONFIG.LABEL_COL}
                    wrapperCol={FORM_CONFIG.WRAPPER_COL}
                    validateTrigger={FORM_CONFIG.VALIDATE_TRIGGER}
                >
                    <Form.Item
                        label="昵称"
                        name="nickname"
                        rules={[...FORM_RULES.NICKNAME]}
                    >
                        <Input
                            prefix={<UserOutlined />}
                            placeholder="请输入用户昵称"
                            maxLength={32}
                            showCount
                        />
                    </Form.Item>

                    <Form.Item
                        label="账户状态"
                        name="status"
                        rules={[{ required: true, message: '请选择账户状态' }]}
                    >
                        <Select
                            placeholder="请选择账户状态"
                        >
                            <Option value="1">
                                <Space>
                                    <CheckCircleOutlined style={{ color: '#52c41a' }} />
                                    激活
                                </Space>
                            </Option>
                            <Option value="0">
                                <Space>
                                    <StopOutlined style={{ color: '#f5222d' }} />
                                    禁用
                                </Space>
                            </Option>
                        </Select>
                    </Form.Item>

                    <Form.Item
                        label="邮箱"
                    >
                        <Input
                            prefix={<MailOutlined />}
                            readOnly
                            style={{
                                backgroundColor: '#f5f5f5 !important',
                                color: '#666666',
                                cursor: 'not-allowed'
                            }}
                            className="readonly-email-input"
                            placeholder="邮箱地址"
                            value={user?.email || ''}
                        />
                        <div style={{ marginTop: 4, color: '#8c8c8c', fontSize: 12 }}>
                            邮箱地址不支持修改
                        </div>
                    </Form.Item>
                </Form>
            </div>
        </Modal>
    );
};

export default UserModal;