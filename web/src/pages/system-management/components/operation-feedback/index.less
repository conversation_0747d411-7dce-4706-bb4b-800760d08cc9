.operation-feedback-modal {
    .ant-modal-header {
        border-bottom: 1px solid #f0f0f0;
        padding: 16px 24px;
    }

    .ant-modal-body {
        padding: 24px;
    }

    .ant-modal-footer {
        border-top: 1px solid #f0f0f0;
        padding: 16px 24px;
    }

    &.loading {
        .ant-modal-close {
            display: none;
        }
    }

    &.success {
        .ant-modal-header {
            background: rgba(82, 196, 26, 0.05);
            border-bottom-color: rgba(82, 196, 26, 0.2);
        }
    }

    &.error {
        .ant-modal-header {
            background: rgba(255, 77, 79, 0.05);
            border-bottom-color: rgba(255, 77, 79, 0.2);
        }
    }
}

.operation-feedback-content {
    .status-section {
        display: flex;
        align-items: center;
        margin-bottom: 16px;

        .status-icon {
            margin-right: 12px;
            font-size: 24px;
            display: flex;
            align-items: center;
        }

        .status-text {
            font-size: 16px;
        }
    }

    .description-section {
        margin-bottom: 16px;
        padding-left: 36px;
    }

    .progress-section {
        margin-bottom: 16px;
        padding-left: 36px;
    }

    .error-section {
        margin-bottom: 16px;
        padding: 12px;
        background: rgba(255, 77, 79, 0.05);
        border: 1px solid rgba(255, 77, 79, 0.2);
        border-radius: 6px;
    }

    .auto-close-section {
        margin-top: 16px;
        text-align: center;
        padding: 8px;
        background: rgba(0, 0, 0, 0.02);
        border-radius: 4px;
    }
}

.operation-feedback-inline {
    padding: 16px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    background: #fafafa;
    margin-bottom: 16px;

    .operation-feedback-content {
        .status-section {
            margin-bottom: 12px;
        }

        .description-section,
        .progress-section {
            padding-left: 0;
            margin-bottom: 12px;
        }
    }
}