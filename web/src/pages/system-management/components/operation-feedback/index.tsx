import React, { useState, useEffect } from 'react';
import { Modal, Progress, Result, Button, Space, Typography } from 'antd';
import {
    LoadingOutlined,
    CheckCircleOutlined,
    ExclamationCircleOutlined,
    CloseCircleOutlined,
    ReloadOutlined,
} from '@ant-design/icons';
import { useFeedback } from '../../hooks/use-feedback';
import './index.less';

const { Text } = Typography;

// 操作状态枚举
export enum OperationStatus {
    IDLE = 'idle',
    LOADING = 'loading',
    SUCCESS = 'success',
    ERROR = 'error',
}

// 操作反馈配置
export interface OperationFeedbackConfig {
    title: string;
    description?: string;
    showProgress?: boolean;
    showModal?: boolean;
    autoClose?: boolean;
    autoCloseDelay?: number;
    onRetry?: () => void;
    onCancel?: () => void;
    onComplete?: () => void;
}

// 操作反馈属性
interface OperationFeedbackProps {
    status: OperationStatus;
    config: OperationFeedbackConfig;
    progress?: number;
    error?: string;
    visible?: boolean;
    onClose?: () => void;
}

/**
 * 操作反馈组件
 * 提供统一的操作进度和结果反馈
 */
const OperationFeedback: React.FC<OperationFeedbackProps> = ({
    status,
    config,
    progress = 0,
    error,
    visible = true,
    onClose,
}) => {
    const [autoCloseTimer, setAutoCloseTimer] = useState<NodeJS.Timeout | null>(null);
    const feedback = useFeedback();

    const {
        title,
        description,
        showProgress = false,
        showModal = true,
        autoClose = true,
        autoCloseDelay = 3000,
        onRetry,
        onCancel,
        onComplete,
    } = config;

    // 自动关闭处理
    useEffect(() => {
        if (status === OperationStatus.SUCCESS && autoClose) {
            const timer = setTimeout(() => {
                if (onComplete) {
                    onComplete();
                }
                if (onClose) {
                    onClose();
                }
            }, autoCloseDelay);

            setAutoCloseTimer(timer);

            return () => {
                if (timer) {
                    clearTimeout(timer);
                }
            };
        }

        return () => {
            if (autoCloseTimer) {
                clearTimeout(autoCloseTimer);
                setAutoCloseTimer(null);
            }
        };
    }, [status, autoClose, autoCloseDelay, onComplete, onClose]);

    const handleRetry = () => {
        if (onRetry) {
            onRetry();
        }
    };

    const handleCancel = () => {
        if (autoCloseTimer) {
            clearTimeout(autoCloseTimer);
            setAutoCloseTimer(null);
        }

        if (onCancel) {
            onCancel();
        }

        if (onClose) {
            onClose();
        }
    };

    const getStatusIcon = () => {
        switch (status) {
            case OperationStatus.LOADING:
                return <LoadingOutlined style={{ color: '#1890ff' }} />;
            case OperationStatus.SUCCESS:
                return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
            case OperationStatus.ERROR:
                return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />;
            default:
                return <ExclamationCircleOutlined style={{ color: '#faad14' }} />;
        }
    };

    const getStatusText = () => {
        switch (status) {
            case OperationStatus.LOADING:
                return '处理中...';
            case OperationStatus.SUCCESS:
                return '操作成功';
            case OperationStatus.ERROR:
                return '操作失败';
            default:
                return '准备中...';
        }
    };

    const renderContent = () => {
        return (
            <div className="operation-feedback-content">
                <div className="status-section">
                    <div className="status-icon">
                        {getStatusIcon()}
                    </div>
                    <div className="status-text">
                        <Text strong>{getStatusText()}</Text>
                    </div>
                </div>

                {description && (
                    <div className="description-section">
                        <Text type="secondary">{description}</Text>
                    </div>
                )}

                {showProgress && status === OperationStatus.LOADING && (
                    <div className="progress-section">
                        <Progress
                            percent={progress}
                            status={status === OperationStatus.ERROR ? 'exception' : 'active'}
                            showInfo={true}
                        />
                    </div>
                )}

                {status === OperationStatus.ERROR && error && (
                    <div className="error-section">
                        <Text type="danger">{error}</Text>
                    </div>
                )}

                {status === OperationStatus.SUCCESS && autoClose && (
                    <div className="auto-close-section">
                        <Text type="secondary">
                            {Math.ceil(autoCloseDelay / 1000)} 秒后自动关闭
                        </Text>
                    </div>
                )}
            </div>
        );
    };

    const renderFooter = () => {
        const buttons: React.ReactNode[] = [];

        if (status === OperationStatus.LOADING && onCancel) {
            buttons.push(
                <Button key="cancel" onClick={handleCancel}>
                    取消
                </Button>
            );
        }

        if (status === OperationStatus.ERROR) {
            if (onRetry) {
                buttons.push(
                    <Button
                        key="retry"
                        type="primary"
                        icon={<ReloadOutlined />}
                        onClick={handleRetry}
                    >
                        重试
                    </Button>
                );
            }

            buttons.push(
                <Button key="close" onClick={handleCancel}>
                    关闭
                </Button>
            );
        }

        if (status === OperationStatus.SUCCESS) {
            buttons.push(
                <Button key="close" type="primary" onClick={handleCancel}>
                    确定
                </Button>
            );
        }

        return buttons.length > 0 ? (
            <Space>
                {buttons}
            </Space>
        ) : null;
    };

    if (!visible || status === OperationStatus.IDLE) {
        return null;
    }

    if (!showModal) {
        // 非模态框形式的反馈
        return (
            <div className="operation-feedback-inline">
                {renderContent()}
            </div>
        );
    }

    return (
        <Modal
            title={title}
            open={visible}
            onCancel={handleCancel}
            footer={renderFooter()}
            className={`operation-feedback-modal ${status}`}
            width={480}
            centered
            closable={status !== OperationStatus.LOADING}
            maskClosable={status !== OperationStatus.LOADING}
        >
            {renderContent()}
        </Modal>
    );
};

/**
 * 操作反馈Hook
 * 提供便捷的操作反馈管理
 */
export const useOperationFeedback = () => {
    const [status, setStatus] = useState<OperationStatus>(OperationStatus.IDLE);
    const [config, setConfig] = useState<OperationFeedbackConfig>({ title: '' });
    const [progress, setProgress] = useState(0);
    const [error, setError] = useState<string>('');
    const [visible, setVisible] = useState(false);

    const startOperation = (operationConfig: OperationFeedbackConfig) => {
        setConfig(operationConfig);
        setStatus(OperationStatus.LOADING);
        setProgress(0);
        setError('');
        setVisible(true);
    };

    const updateProgress = (newProgress: number) => {
        setProgress(Math.min(100, Math.max(0, newProgress)));
    };

    const completeOperation = () => {
        setStatus(OperationStatus.SUCCESS);
        setProgress(100);
        setError('');
    };

    const failOperation = (errorMessage: string) => {
        setStatus(OperationStatus.ERROR);
        setError(errorMessage);
    };

    const closeOperation = () => {
        setStatus(OperationStatus.IDLE);
        setVisible(false);
        setProgress(0);
        setError('');
    };

    const resetOperation = () => {
        setStatus(OperationStatus.IDLE);
        setProgress(0);
        setError('');
    };

    return {
        status,
        config,
        progress,
        error,
        visible,
        startOperation,
        updateProgress,
        completeOperation,
        failOperation,
        closeOperation,
        resetOperation,
        OperationFeedback: (props: Partial<OperationFeedbackProps>) => (
            <OperationFeedback
                status={status}
                config={config}
                progress={progress}
                error={error}
                visible={visible}
                onClose={closeOperation}
                {...props}
            />
        ),
    };
};

export default OperationFeedback;