@import '../../../../theme/vars.less';

.error-boundary {
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 24px;

    .error-card {
        max-width: 800px;
        width: 100%;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        border-radius: 8px;

        .ant-card-body {
            padding: 32px;
        }

        .ant-result {
            .ant-result-icon {
                .anticon {
                    color: #ff4d4f;
                    font-size: 48px;
                }
            }

            .ant-result-title {
                color: #262626;
                font-size: 20px;
                font-weight: 600;
                margin-bottom: 8px;
            }

            .ant-result-subtitle {
                color: #8c8c8c;
                font-size: 14px;
                line-height: 1.6;
                margin-bottom: 24px;
            }

            .ant-result-extra {
                .ant-btn {
                    border-radius: 6px;
                    font-weight: 500;
                    margin: 0 8px;

                    &.ant-btn-primary {
                        background: @primary-color;
                        border-color: @primary-color;

                        &:hover {
                            background: fade(@primary-color, 80%);
                            border-color: fade(@primary-color, 80%);
                        }
                    }
                }
            }
        }

        .error-details {
            margin-top: 24px;
            border-top: 1px solid #f0f0f0;
            padding-top: 24px;

            .error-details-header {
                display: flex;
                align-items: center;
                gap: 8px;
                color: #8c8c8c;
                font-size: 14px;

                .anticon {
                    color: #faad14;
                }
            }

            .error-info {
                .error-section {
                    margin-bottom: 16px;

                    &:last-child {
                        margin-bottom: 0;
                    }

                    .ant-typography {
                        margin-bottom: 8px;

                        &:last-child {
                            margin-bottom: 0;
                        }
                    }

                    .error-stack {
                        background: #f5f5f5;
                        border: 1px solid #d9d9d9;
                        border-radius: 4px;
                        padding: 12px;
                        font-size: 12px;
                        line-height: 1.4;
                        color: #595959;
                        max-height: 200px;
                        overflow-y: auto;
                        white-space: pre-wrap;
                        word-break: break-all;
                    }
                }

                .error-actions {
                    margin-top: 16px;
                    padding-top: 16px;
                    border-top: 1px solid #f0f0f0;

                    .ant-btn {
                        border-radius: 4px;
                        font-size: 12px;
                    }
                }
            }
        }

        .error-help {
            margin-top: 16px;
            padding-top: 16px;
            border-top: 1px solid #f0f0f0;
            text-align: center;

            .ant-typography {
                font-size: 12px;
                color: #8c8c8c;
            }
        }
    }

    // 响应式设计
    @media (max-width: 768px) {
        padding: 16px;
        min-height: 300px;

        .error-card {
            .ant-card-body {
                padding: 24px 16px;
            }

            .ant-result {
                .ant-result-icon {
                    .anticon {
                        font-size: 36px;
                    }
                }

                .ant-result-title {
                    font-size: 18px;
                }

                .ant-result-subtitle {
                    font-size: 13px;
                }

                .ant-result-extra {
                    .ant-btn {
                        margin: 4px;
                        font-size: 13px;
                    }
                }
            }

            .error-details {
                margin-top: 16px;
                padding-top: 16px;

                .error-info {
                    .error-section {
                        margin-bottom: 12px;

                        .error-stack {
                            padding: 8px;
                            font-size: 11px;
                            max-height: 150px;
                        }
                    }

                    .error-actions {
                        margin-top: 12px;
                        padding-top: 12px;

                        .ant-btn {
                            font-size: 11px;
                        }
                    }
                }
            }

            .error-help {
                margin-top: 12px;
                padding-top: 12px;

                .ant-typography {
                    font-size: 11px;
                }
            }
        }
    }

    @media (max-width: 480px) {
        padding: 12px;
        min-height: 250px;

        .error-card {
            .ant-card-body {
                padding: 16px 12px;
            }

            .ant-result {
                .ant-result-icon {
                    .anticon {
                        font-size: 32px;
                    }
                }

                .ant-result-title {
                    font-size: 16px;
                }

                .ant-result-subtitle {
                    font-size: 12px;
                }

                .ant-result-extra {
                    .ant-btn {
                        width: 100%;
                        margin: 4px 0;
                        font-size: 12px;
                    }
                }
            }

            .error-details {
                .error-details-header {
                    font-size: 13px;
                }

                .error-info {
                    .error-section {
                        .error-stack {
                            font-size: 10px;
                            max-height: 120px;
                        }
                    }
                }
            }
        }
    }

    // 暗色主题支持
    &.dark {
        .error-card {
            background: #1f1f1f;
            border-color: #303030;

            .ant-result {
                .ant-result-title {
                    color: #d9d9d9;
                }

                .ant-result-subtitle {
                    color: #8c8c8c;
                }
            }

            .error-details {
                border-top-color: #303030;

                .error-details-header {
                    color: #8c8c8c;
                }

                .error-info {
                    .error-section {
                        .error-stack {
                            background: #262626;
                            border-color: #434343;
                            color: #d9d9d9;
                        }
                    }

                    .error-actions {
                        border-top-color: #303030;
                    }
                }
            }

            .error-help {
                border-top-color: #303030;

                .ant-typography {
                    color: #8c8c8c;
                }
            }
        }
    }

    // 高对比度模式
    @media (prefers-contrast: high) {
        .error-card {
            border: 2px solid #000;

            .ant-result {
                .ant-result-icon {
                    .anticon {
                        color: #ff0000;
                    }
                }

                .ant-result-title {
                    color: #000;
                }

                .ant-result-subtitle {
                    color: #000;
                }
            }

            .error-details {
                border-top-color: #000;

                .error-details-header {
                    color: #000;

                    .anticon {
                        color: #ff8c00;
                    }
                }

                .error-info {
                    .error-section {
                        .error-stack {
                            background: #fff;
                            border-color: #000;
                            color: #000;
                        }
                    }

                    .error-actions {
                        border-top-color: #000;
                    }
                }
            }

            .error-help {
                border-top-color: #000;

                .ant-typography {
                    color: #000;
                }
            }
        }
    }

    // 减少动画模式
    @media (prefers-reduced-motion: reduce) {
        .error-card {
            transition: none;
        }

        .ant-result-icon {
            animation: none;
        }
    }

    // 打印样式
    @media print {
        .error-card {
            box-shadow: none;
            border: 1px solid #000;

            .ant-result-extra {
                display: none;
            }

            .error-details {
                .error-actions {
                    display: none;
                }
            }
        }
    }
}