import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Resul<PERSON>, <PERSON><PERSON>, Card, Collapse, Typography } from 'antd';
import {
    ExclamationCircleOutlined,
    ReloadOutlined,
    BugOutlined,
    InfoCircleOutlined
} from '@ant-design/icons';
import './index.less';

const { Panel } = Collapse;
const { Paragraph, Text } = Typography;

// 错误边界状态接口
interface ErrorBoundaryState {
    hasError: boolean;
    error: Error | null;
    errorInfo: ErrorInfo | null;
    errorId: string;
}

// 错误边界属性接口
interface ErrorBoundaryProps {
    children: ReactNode;
    fallback?: ReactNode;
    onError?: (error: Error, errorInfo: ErrorInfo) => void;
    showDetails?: boolean;
    showReload?: boolean;
    title?: string;
    description?: string;
}

/**
 * 错误边界组件
 * 捕获子组件中的JavaScript错误，记录错误并显示备用UI
 */
class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
    constructor(props: ErrorBoundaryProps) {
        super(props);

        this.state = {
            hasError: false,
            error: null,
            errorInfo: null,
            errorId: '',
        };
    }

    static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
        // 更新状态以显示错误UI
        return {
            hasError: true,
            error,
            errorId: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        };
    }

    componentDidCatch(error: Error, errorInfo: ErrorInfo) {
        // 记录错误信息
        this.setState({
            error,
            errorInfo,
        });

        // 调用错误处理回调
        if (this.props.onError) {
            this.props.onError(error, errorInfo);
        }

        // 在开发环境下打印详细错误信息
        if (process.env.NODE_ENV === 'development') {
            console.group('🚨 Error Boundary Caught an Error');
            console.error('Error:', error);
            console.error('Error Info:', errorInfo);
            console.error('Component Stack:', errorInfo.componentStack);
            console.groupEnd();
        }

        // 在生产环境下发送错误报告（可选）
        if (process.env.NODE_ENV === 'production') {
            this.reportError(error, errorInfo);
        }
    }

    /**
     * 报告错误到监控系统（示例）
     */
    private reportError = (error: Error, errorInfo: ErrorInfo) => {
        // 这里可以集成错误监控服务，如 Sentry、Bugsnag 等
        const errorReport = {
            message: error.message,
            stack: error.stack,
            componentStack: errorInfo.componentStack,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            url: window.location.href,
            errorId: this.state.errorId,
        };

        // 示例：发送到错误监控服务
        // errorMonitoringService.captureException(errorReport);

        console.warn('Error reported:', errorReport);
    };

    /**
     * 重新加载页面
     */
    private handleReload = () => {
        window.location.reload();
    };

    /**
     * 重置错误状态
     */
    private handleReset = () => {
        this.setState({
            hasError: false,
            error: null,
            errorInfo: null,
            errorId: '',
        });
    };

    /**
     * 复制错误信息到剪贴板
     */
    private handleCopyError = async () => {
        const { error, errorInfo, errorId } = this.state;

        const errorText = `
错误ID: ${errorId}
时间: ${new Date().toLocaleString()}
错误信息: ${error?.message || 'Unknown error'}
错误堆栈: ${error?.stack || 'No stack trace'}
组件堆栈: ${errorInfo?.componentStack || 'No component stack'}
页面URL: ${window.location.href}
用户代理: ${navigator.userAgent}
    `.trim();

        try {
            await navigator.clipboard.writeText(errorText);
            // 这里可以显示复制成功的提示
            console.log('Error info copied to clipboard');
        } catch (err) {
            console.error('Failed to copy error info:', err);
        }
    };

    render() {
        const {
            children,
            fallback,
            showDetails = true,
            showReload = true,
            title = '页面出现错误',
            description = '抱歉，页面遇到了一个错误。您可以尝试刷新页面或联系技术支持。'
        } = this.props;

        const { hasError, error, errorInfo, errorId } = this.state;

        if (hasError) {
            // 如果提供了自定义fallback，使用它
            if (fallback) {
                return fallback;
            }

            // 默认错误UI
            return (
                <div className="error-boundary">
                    <Card className="error-card">
                        <Result
                            status="error"
                            icon={<ExclamationCircleOutlined />}
                            title={title}
                            subTitle={description}
                            extra={[
                                showReload && (
                                    <Button
                                        key="reload"
                                        type="primary"
                                        icon={<ReloadOutlined />}
                                        onClick={this.handleReload}
                                    >
                                        刷新页面
                                    </Button>
                                ),
                                <Button
                                    key="reset"
                                    onClick={this.handleReset}
                                >
                                    重试
                                </Button>,
                            ].filter(Boolean)}
                        />

                        {showDetails && process.env.NODE_ENV === 'development' && error && (
                            <div className="error-details">
                                <Collapse ghost>
                                    <Panel
                                        header={
                                            <div className="error-details-header">
                                                <BugOutlined />
                                                <span>错误详情 (仅开发环境显示)</span>
                                            </div>
                                        }
                                        key="error-details"
                                    >
                                        <div className="error-info">
                                            <div className="error-section">
                                                <Text strong>错误ID:</Text>
                                                <Paragraph copyable={{ text: errorId }}>
                                                    <Text code>{errorId}</Text>
                                                </Paragraph>
                                            </div>

                                            <div className="error-section">
                                                <Text strong>错误信息:</Text>
                                                <Paragraph copyable={{ text: error.message }}>
                                                    <Text code>{error.message}</Text>
                                                </Paragraph>
                                            </div>

                                            {error.stack && (
                                                <div className="error-section">
                                                    <Text strong>错误堆栈:</Text>
                                                    <Paragraph copyable={{ text: error.stack }}>
                                                        <pre className="error-stack">{error.stack}</pre>
                                                    </Paragraph>
                                                </div>
                                            )}

                                            {errorInfo?.componentStack && (
                                                <div className="error-section">
                                                    <Text strong>组件堆栈:</Text>
                                                    <Paragraph copyable={{ text: errorInfo.componentStack }}>
                                                        <pre className="error-stack">{errorInfo.componentStack}</pre>
                                                    </Paragraph>
                                                </div>
                                            )}

                                            <div className="error-actions">
                                                <Button
                                                    size="small"
                                                    icon={<InfoCircleOutlined />}
                                                    onClick={this.handleCopyError}
                                                >
                                                    复制完整错误信息
                                                </Button>
                                            </div>
                                        </div>
                                    </Panel>
                                </Collapse>
                            </div>
                        )}

                        {showDetails && process.env.NODE_ENV === 'production' && (
                            <div className="error-help">
                                <Text type="secondary">
                                    错误ID: {errorId} - 如需技术支持，请提供此错误ID
                                </Text>
                            </div>
                        )}
                    </Card>
                </div>
            );
        }

        return children;
    }
}

/**
 * 高阶组件：为组件添加错误边界
 */
export const withErrorBoundary = <P extends object>(
    WrappedComponent: React.ComponentType<P>,
    errorBoundaryProps?: Omit<ErrorBoundaryProps, 'children'>
) => {
    const WithErrorBoundaryComponent = (props: P) => (
        <ErrorBoundary {...errorBoundaryProps}>
            <WrappedComponent {...props} />
        </ErrorBoundary>
    );

    WithErrorBoundaryComponent.displayName = `withErrorBoundary(${WrappedComponent.displayName || WrappedComponent.name})`;

    return WithErrorBoundaryComponent;
};

/**
 * Hook：在函数组件中使用错误边界
 */
export const useErrorHandler = () => {
    const [error, setError] = React.useState<Error | null>(null);

    React.useEffect(() => {
        if (error) {
            throw error;
        }
    }, [error]);

    const throwError = React.useCallback((error: Error) => {
        setError(error);
    }, []);

    const clearError = React.useCallback(() => {
        setError(null);
    }, []);

    return { throwError, clearError };
};

export default ErrorBoundary;