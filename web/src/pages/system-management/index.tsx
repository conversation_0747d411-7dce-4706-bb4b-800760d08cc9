import React, { useState } from 'react';
import { Layout, Spin, Alert, Flex } from 'antd';
import { useTranslation } from 'react-i18next';
import Sidebar from './components/sidebar';
import UserManagement from './components/user-management';
import KnowledgebaseManagement from './components/knowledgebase-management';
import SuperuserAuth from './components/superuser-auth';
import ErrorBoundary from './components/error-boundary';
import GlobalErrorHandler from './components/global-error-handler';
import { useGlobalErrorHandler } from './hooks/use-global-error-handler';
import { useFeedback } from './hooks/use-feedback';
import styles from './index.less';

type TabType = 'users' | 'knowledgebases';

const SystemManagement: React.FC = () => {
    const [activeTab, setActiveTab] = useState<TabType>('users');
    const [loading, setLoading] = useState(false);
    const { t } = useTranslation('translation', { keyPrefix: 'systemManagement' });

    // 初始化全局错误处理和反馈
    const { errorHandler, feedback: globalFeedback, checkNetworkStatus, safeExecute } = useGlobalErrorHandler();
    const feedback = useFeedback();

    const handleTabChange = async (tab: TabType) => {
        if (!checkNetworkStatus()) {
            feedback.warning('网络连接异常，请检查网络后重试');
            return;
        }

        setLoading(true);
        setActiveTab(tab);
        setLoading(false);
    };

    const renderContent = () => {
        switch (activeTab) {
            case 'users':
                return <UserManagement />;
            case 'knowledgebases':
                return <KnowledgebaseManagement />;
            default:
                return <UserManagement />;
        }
    };

    const handleGlobalError = (error: Error, errorInfo: any) => {
        errorHandler.handleError({
            type: 'UNKNOWN_ERROR' as any,
            message: '系统管理页面组件错误',
            details: { error, errorInfo },
        });

        // 显示用户友好的错误提示
        feedback.error('页面出现异常，已自动记录错误信息', {
            type: 'notification',
            duration: 0,
        });
    };

    return (
        <SuperuserAuth>
            <GlobalErrorHandler showNetworkIndicator={false}>
                <ErrorBoundary
                    title="系统管理页面出现错误"
                    description="系统管理功能遇到了问题，请尝试刷新页面或联系管理员"
                    onError={handleGlobalError}
                    showDetails={process.env.NODE_ENV === 'development'}
                >
                    <div className={styles.systemManagementWrapper}>
                        {/* 网络状态提示 */}
                        {!navigator.onLine && (
                            <Alert
                                message="网络连接异常"
                                description="当前网络不可用，部分功能可能无法正常使用"
                                type="warning"
                                showIcon
                                closable
                                style={{ marginBottom: 16 }}
                            />
                        )}

                        <Flex className={styles.systemManagement}>
                            <Sidebar
                                activeTab={activeTab}
                                onTabChange={handleTabChange}
                                loading={loading}
                            />
                            {loading ? (
                                <Flex flex={1} className={styles.loadingWrapper}>
                                    <div className={styles.loading}>
                                        <Spin size="large" tip="正在加载..." />
                                    </div>
                                </Flex>
                            ) : (
                                <Flex flex={1} className={styles.contentWrapper}>
                                    <ErrorBoundary
                                        title="内容加载错误"
                                        description="页面内容加载失败，请重试"
                                        showReload={false}
                                        onError={(error, errorInfo) => {
                                            errorHandler.handleError({
                                                type: 'UNKNOWN_ERROR' as any,
                                                message: '内容区域组件错误',
                                                details: { error, errorInfo, activeTab },
                                            });
                                        }}
                                    >
                                        {renderContent()}
                                    </ErrorBoundary>
                                </Flex>
                            )}
                        </Flex>
                    </div>
                </ErrorBoundary>
            </GlobalErrorHandler>
        </SuperuserAuth>
    );
};

export default SystemManagement;