import React from 'react';
import { Spin, Result, Button } from 'antd';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { usePermissionCheck } from '@/pages/system-management/hooks/use-permission-check';

interface SuperuserAuthProps {
    children: React.ReactNode;
}

/**
 * 超级用户权限包装器组件
 * 只有超级用户才能访问被包装的组件
 */
const SuperuserAuth: React.FC<SuperuserAuthProps> = ({ children }) => {
    const { isSuperuser, loading, error } = usePermissionCheck();

    // 加载中状态
    if (loading) {
        return (
            <div style={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                height: '100vh',
                flexDirection: 'column'
            }}>
                <Spin size="large" />
                <div style={{ marginTop: 16, color: '#666' }}>
                    正在验证权限...
                </div>
            </div>
        );
    }

    // 权限不足或验证失败
    if (!isSuperuser) {
        return (
            <Result
                status="403"
                title="权限不足"
                subTitle={error || "抱歉，您需要超级用户权限才能访问此页面。"}
                icon={<ExclamationCircleOutlined />}
                extra={[
                    <Button
                        type="primary"
                        key="back"
                        onClick={() => window.location.href = '/knowledge'}
                    >
                        返回知识库
                    </Button>,
                    <Button
                        key="refresh"
                        onClick={() => window.location.reload()}
                    >
                        重新验证
                    </Button>
                ]}
            />
        );
    }

    // 权限验证通过，渲染子组件
    return <>{children}</>;
};

export default SuperuserAuth;