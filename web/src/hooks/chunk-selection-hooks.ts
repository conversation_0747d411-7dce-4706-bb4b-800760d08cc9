import { message } from 'antd';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

/**
 * Chunk选择状态管理Hook
 * 提供完整的chunk选择状态管理功能
 */
export const useChunkSelection = (chunks: any[] = []) => {
  const { t } = useTranslation();
  const [selectedChunkIds, setSelectedChunkIds] = useState<string[]>([]);

  // 当chunk列表变化时，清理无效的选中项
  useEffect(() => {
    const validChunkIds = chunks.map((chunk) => chunk.chunk_id);
    setSelectedChunkIds((prev) =>
      prev.filter((id) => validChunkIds.includes(id)),
    );
  }, [chunks]);

  // 选择所有chunk
  const selectAllChunks = useCallback(
    (checked: boolean) => {
      if (checked) {
        const allChunkIds = chunks.map((chunk) => chunk.chunk_id);
        setSelectedChunkIds(allChunkIds);
      } else {
        setSelectedChunkIds([]);
      }
    },
    [chunks],
  );

  // 切换单个chunk的选择状态
  const toggleChunkSelection = useCallback(
    (chunkId: string, checked: boolean) => {
      setSelectedChunkIds((prev) => {
        const index = prev.indexOf(chunkId);
        const newSelection = [...prev];

        if (checked && index === -1) {
          newSelection.push(chunkId);
        } else if (!checked && index !== -1) {
          newSelection.splice(index, 1);
        }

        return newSelection;
      });
    },
    [],
  );

  // 批量选择chunk
  const selectChunks = useCallback(
    (chunkIds: string[]) => {
      const validChunkIds = chunkIds.filter((id) =>
        chunks.some((chunk) => chunk.chunk_id === id),
      );
      setSelectedChunkIds((prev) => {
        const newSelection = new Set([...prev, ...validChunkIds]);
        return Array.from(newSelection);
      });
    },
    [chunks],
  );

  // 批量取消选择chunk
  const deselectChunks = useCallback((chunkIds: string[]) => {
    setSelectedChunkIds((prev) => prev.filter((id) => !chunkIds.includes(id)));
  }, []);

  // 清空选择
  const clearSelection = useCallback(() => {
    setSelectedChunkIds([]);
  }, []);

  // 反选
  const invertSelection = useCallback(() => {
    const allChunkIds = chunks.map((chunk) => chunk.chunk_id);
    const unselectedIds = allChunkIds.filter(
      (id) => !selectedChunkIds.includes(id),
    );
    setSelectedChunkIds(unselectedIds);
  }, [chunks, selectedChunkIds]);

  // 根据条件选择chunk
  const selectChunksByCondition = useCallback(
    (condition: (chunk: any) => boolean) => {
      const matchingChunkIds = chunks
        .filter(condition)
        .map((chunk) => chunk.chunk_id);
      setSelectedChunkIds(matchingChunkIds);
    },
    [chunks],
  );

  // 计算选择状态统计
  const selectionStats = useMemo(() => {
    const totalCount = chunks.length;
    const selectedCount = selectedChunkIds.length;
    const isAllSelected = totalCount > 0 && selectedCount === totalCount;
    const isPartialSelected = selectedCount > 0 && selectedCount < totalCount;
    const isNoneSelected = selectedCount === 0;

    return {
      totalCount,
      selectedCount,
      unselectedCount: totalCount - selectedCount,
      isAllSelected,
      isPartialSelected,
      isNoneSelected,
      selectionPercentage:
        totalCount > 0 ? Math.round((selectedCount / totalCount) * 100) : 0,
    };
  }, [chunks.length, selectedChunkIds.length]);

  // 获取选中的chunk对象
  const selectedChunks = useMemo(() => {
    return chunks.filter((chunk) => selectedChunkIds.includes(chunk.chunk_id));
  }, [chunks, selectedChunkIds]);

  // 获取未选中的chunk对象
  const unselectedChunks = useMemo(() => {
    return chunks.filter((chunk) => !selectedChunkIds.includes(chunk.chunk_id));
  }, [chunks, selectedChunkIds]);

  // 检查特定chunk是否被选中
  const isChunkSelected = useCallback(
    (chunkId: string) => {
      return selectedChunkIds.includes(chunkId);
    },
    [selectedChunkIds],
  );

  // 验证选择状态（用于批量操作前的检查）
  const validateSelection = useCallback(
    (minCount: number = 1, maxCount?: number) => {
      const selectedCount = selectedChunkIds.length;

      if (selectedCount < minCount) {
        message.warning(
          t('chunk.validation.minSelectionRequired', { count: minCount }),
        );
        return false;
      }

      if (maxCount && selectedCount > maxCount) {
        message.warning(
          t('chunk.validation.maxSelectionExceeded', { count: maxCount }),
        );
        return false;
      }

      return true;
    },
    [selectedChunkIds.length, t],
  );

  // 显示选择警告
  const showSelectionWarning = useCallback(
    (message?: string) => {
      const defaultMessage = t('message.pleaseSelectChunk');
      message.warning(message || defaultMessage);
    },
    [t],
  );

  return {
    // 状态
    selectedChunkIds,
    selectedChunks,
    unselectedChunks,
    selectionStats,

    // 操作方法
    selectAllChunks,
    toggleChunkSelection,
    selectChunks,
    deselectChunks,
    clearSelection,
    invertSelection,
    selectChunksByCondition,

    // 查询方法
    isChunkSelected,
    validateSelection,
    showSelectionWarning,

    // 直接设置（谨慎使用）
    setSelectedChunkIds,
  };
};

/**
 * 批量操作状态管理Hook
 * 配合chunk选择状态使用，提供批量操作的状态管理
 */
export const useBatchOperations = (selectedChunkIds: string[]) => {
  const { t } = useTranslation();
  const [isProcessing, setIsProcessing] = useState(false);
  const [operationType, setOperationType] = useState<string | null>(null);

  // 开始批量操作
  const startBatchOperation = useCallback(
    (type: string) => {
      if (selectedChunkIds.length === 0) {
        message.warning(t('message.pleaseSelectChunk'));
        return false;
      }

      setIsProcessing(true);
      setOperationType(type);
      return true;
    },
    [selectedChunkIds.length, t],
  );

  // 结束批量操作
  const endBatchOperation = useCallback(() => {
    setIsProcessing(false);
    setOperationType(null);
  }, []);

  // 批量操作状态
  const batchOperationState = useMemo(
    () => ({
      isProcessing,
      operationType,
      selectedCount: selectedChunkIds.length,
      canOperate: selectedChunkIds.length > 0 && !isProcessing,
    }),
    [isProcessing, operationType, selectedChunkIds.length],
  );

  return {
    batchOperationState,
    startBatchOperation,
    endBatchOperation,
  };
};

/**
 * Chunk选择持久化Hook
 * 在页面刷新或导航时保持选择状态
 */
export const useChunkSelectionPersistence = (
  key: string,
  selectedChunkIds: string[],
  setSelectedChunkIds: (ids: string[]) => void,
) => {
  // 保存选择状态到localStorage
  const saveSelection = useCallback(() => {
    try {
      localStorage.setItem(
        `chunk_selection_${key}`,
        JSON.stringify(selectedChunkIds),
      );
    } catch (error) {
      console.warn('Failed to save chunk selection:', error);
    }
  }, [key, selectedChunkIds]);

  // 从localStorage恢复选择状态
  const restoreSelection = useCallback(() => {
    try {
      const saved = localStorage.getItem(`chunk_selection_${key}`);
      if (saved) {
        const parsedIds = JSON.parse(saved);
        if (Array.isArray(parsedIds)) {
          setSelectedChunkIds(parsedIds);
        }
      }
    } catch (error) {
      console.warn('Failed to restore chunk selection:', error);
    }
  }, [key, setSelectedChunkIds]);

  // 清除保存的选择状态
  const clearSavedSelection = useCallback(() => {
    try {
      localStorage.removeItem(`chunk_selection_${key}`);
    } catch (error) {
      console.warn('Failed to clear saved chunk selection:', error);
    }
  }, [key]);

  return {
    saveSelection,
    restoreSelection,
    clearSavedSelection,
  };
};

/**
 * 智能选择Hook
 * 提供基于条件的智能选择功能
 */
export const useSmartChunkSelection = (
  chunks: any[],
  selectChunksByCondition: (condition: (chunk: any) => boolean) => void,
) => {
  const { t } = useTranslation();

  // 选择启用的chunk
  const selectEnabledChunks = useCallback(() => {
    selectChunksByCondition((chunk) => chunk.available_int === 1);
    message.success(t('chunk.selectedEnabledChunks'));
  }, [selectChunksByCondition, t]);

  // 选择禁用的chunk
  const selectDisabledChunks = useCallback(() => {
    selectChunksByCondition((chunk) => chunk.available_int === 0);
    message.success(t('chunk.selectedDisabledChunks'));
  }, [selectChunksByCondition, t]);

  // 选择包含特定关键词的chunk
  const selectChunksWithKeywords = useCallback(
    (keywords: string[]) => {
      selectChunksByCondition((chunk) => {
        const chunkKeywords = chunk.important_kwd || [];
        return keywords.some((keyword) =>
          chunkKeywords.some((chunkKeyword: string) =>
            chunkKeyword.toLowerCase().includes(keyword.toLowerCase()),
          ),
        );
      });
      message.success(
        t('chunk.selectedChunksWithKeywords', { count: keywords.length }),
      );
    },
    [selectChunksByCondition, t],
  );

  // 选择没有关键词的chunk
  const selectChunksWithoutKeywords = useCallback(() => {
    selectChunksByCondition((chunk) => {
      const chunkKeywords = chunk.important_kwd || [];
      return chunkKeywords.length === 0;
    });
    message.success(t('chunk.selectedChunksWithoutKeywords'));
  }, [selectChunksByCondition, t]);

  // 选择长度在指定范围内的chunk
  const selectChunksByLength = useCallback(
    (minLength: number, maxLength?: number) => {
      selectChunksByCondition((chunk) => {
        const content = chunk.content_with_weight || '';
        const length = content.length;
        return (
          length >= minLength &&
          (maxLength === undefined || length <= maxLength)
        );
      });
      message.success(t('chunk.selectedChunksByLength'));
    },
    [selectChunksByCondition, t],
  );

  return {
    selectEnabledChunks,
    selectDisabledChunks,
    selectChunksWithKeywords,
    selectChunksWithoutKeywords,
    selectChunksByLength,
  };
};
