import kbService from '@/services/knowledge-service';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { message } from 'antd';
import { useTranslate } from './common-hooks';

export interface BatchKeywordParams {
  doc_id: string;
  chunk_ids: string[];
  keywords: string[];
  preview?: boolean;
}

export interface BatchKeywordResult {
  failed_chunks: Array<{
    chunk_id: string;
    error_message: string;
  }>;
  failed_count: number;
  success_count: number;
  total_chunks: number;
  preview_data?: Record<string, any>;
}

export interface BatchKeywordResponse {
  code: number;
  data: BatchKeywordResult;
  message: string;
}

export const useBatchKeywords = () => {
  const { t } = useTranslate('chunk');
  const queryClient = useQueryClient();

  const previewMutation = useMutation({
    mutationFn: async (
      params: BatchKeywordParams,
    ): Promise<BatchKeywordResult> => {
      const response = await kbService.batchAppendKeywords({
        ...params,
        preview: true,
      });

      // 统一检查 code 字段
      if (response.code !== 0) {
        throw new Error(response.message || 'Preview failed');
      }

      return response.data;
    },
  });

  const executeMutation = useMutation({
    mutationFn: async (
      params: BatchKeywordParams,
    ): Promise<BatchKeywordResult> => {
      const response = await kbService.batchAppendKeywords({
        ...params,
        preview: false,
      });

      // 统一检查 code 字段
      if (response.code !== 0) {
        throw new Error(response.message || 'Batch operation failed');
      }

      return response.data;
    },
    onSuccess: (data: BatchKeywordResult) => {
      const { success_count, failed_count } = data;
      if (failed_count === 0) {
        message.success(t('batchKeywordSuccess', { count: success_count }));
      } else {
        message.warning(
          t('batchKeywordPartialSuccess', {
            success: success_count,
            failed: failed_count,
          }),
        );
      }

      // 刷新相关查询
      queryClient.invalidateQueries({
        queryKey: ['fetchChunkList'],
      });
      queryClient.invalidateQueries({
        queryKey: ['chunk'],
      });
    },
    onError: (error: Error) => {
      console.error('Execute error:', error);
      // 注释掉自动错误提示，让组件层完全控制UI反馈
      // message.error(t('batchKeywordFailed', { error: error.message }));
    },
  });

  return {
    preview: previewMutation.mutateAsync,
    execute: executeMutation.mutateAsync,
    isPreviewLoading: previewMutation.isPending,
    isExecuteLoading: executeMutation.isPending,
    previewError: previewMutation.error,
    executeError: executeMutation.error,
  };
};
