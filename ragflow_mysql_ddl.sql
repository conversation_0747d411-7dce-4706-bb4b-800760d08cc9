-- RAGFlow 数据库表结构 DDL 脚本
-- 适用于 MySQL 8.0+
-- 创建时间: 2025-01-15
-- 说明: 本脚本包含 RAGFlow 系统所有数据表的创建语句

-- 设置字符集和排序规则
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ================================
-- 1. 用户管理模块
nicode_ci = utf8mb4_uation-servercoll4
--  = utf8mbet-serveracter-s:
-- char- 字符集设置际并发需求调整

-根据实ons = ax_connecti除)
-- m已移ySQL 8.0ype = OFF (My_cache_t2
-- quer= mit g_at_trx_comlonodb_flush_6M
-- in = 25og_file_size-- innodb_lble RAM
f availa= 70% oe fer_pool_sizdb_bufnno数:
-- i议的MySQL配置参==

-- 建========================= ===== 数据库配置建议
--========
--========================;

-- LUE
-- ) MAXVAS LESS THANfuture VALUEION p_PARTIT26),
--   (20LESS THAN ALUES 5 Vp202ITION -   PART25),
-N (20ESS THA4 VALUES LRTITION p202
--   PA)) (reate_timeGE (YEAR(cITION BY RANRT-- PAersation` 
4_convTABLE `api_TER 
-- AL区
-- 示例:虑按时间分ersation)可以考ion, convnversat类表(如api_4_co-- 对于日志=======

========================- =)
-建议(适用于大数据量场景
-- 分区表================================;

-- reate_time`)`c_id`, `user`, alog_id (`ditime`r_ialog_usex_dNDEX `idation` ADD Ii_4_convers`apER TABLE 
-- ALTer_id`);id`, `usr` (`dialog_og_use`idx_dialX NDEtion` ADD IrsaconveR TABLE `TEus`);
-- AL, `stat (`kb_id`s`x_kb_statu`idINDEX ument` ADD E `docTABL
-- ALTER ;_id`)tenantd`, `(`user_ier_tenant` DEX `idx_ust` ADD IN `user_tenanER TABLELT A--的字段创建复合索引
- 为经常一起查询====

-===================- ========= 索引优化建议
-=
--==============================
-- =ECKS = 1;
EY_CH FOREIGN_K恢复外键检查
SET- 配置表';

-搜索MMENT='nicode_ci COTE=utf8mb4_umb4 COLLA=utf8LT CHARSETFAUnnoDB DEENGINE=Itus`)
) statatus` (`EY `idx_s`),
  Keated_by_by` (`createdcridx_ `),
  KEYme` (`name``idx_na
  KEY nt_id`),` (`tenaant_idKEY `idx_ten  `id`),
 KEY (
  PRIMARYT '更新时间',COMMENMP MESTANT_TI CURRE UPDATETAMP ONNT_TIMESEFAULT CURREL DNOT NUL datetime e`date_tim间',
  `up建时MENT '创MP COMRENT_TIMESTAURDEFAULT C NULL atetime NOT_time` d
  `create弃, 1:有效)',态(0:废'搜索应用状T  '1' COMMENULThar(1) DEFArcatus` va `st参数',
 MMENT '搜索配置
  )) COalse fry_mindmap',    'que,
h', falsearclated_se
    'realse,b_search', f 'wee,
   word', fals'keyse,
    falght', li
    'highrray(),es', json_aguags_lanos
    'cr4
    ),nalty', 0.pence_se   'pre
   alty', 0.7,equency_pen
      'frop_p', 0.3,  't 0.1,
    erature',
      'tempson_object(, j_setting',
    'llm_id', ''
    'chatse, falsummary',,
    '024 'top_k', 1 '',
   rank_id',se,
    'rese_kg', fal 'u.3,
   ht', 0arity_weig_similvector '
    0.0,old',_thresh'similarityray(),
    ds', json_ar_i'doc,
    array()on_b_ids', js    'kct(
jeULT (json_obT NULL DEFAon NOh_config` jsarc
  `se',MMENT '创建者IDNULL CO(32) NOT charar` vd_byreate,
  `c用描述'搜索应t COMMENT 'ion` tex `descript索应用名称',
 T '搜ULL COMMENr(128) NOT Nrcha
  `name` vaT '租户ID',ULL COMMEN NNOTr(32) d` varcha`tenant_i)',
  se64字符串像(baMENT '搜索应用头 COMatar` text,
  `av'搜索配置唯一标识' COMMENT 2) NOT NULLd` varchar(3ch` (
  `isearTE TABLE `
CREA `search`;STSXIIF EE 息
DROP TABL存储搜索应用的配置信 - 

-- 搜索配置表===============================理模块
-- =
-- 9. 搜索管================================务器表';

-- MMENT='MCP服i CO_c4_unicodeutf8mbTE=OLLA4 CSET=utf8mbFAULT CHARnnoDB DEE=I
) ENGINnant_id`)te(`` _idnantidx_te
  KEY ``id`), (RIMARY KEY',
  P新时间'更 COMMENT ESTAMPTIM CURRENT_ON UPDATETIMESTAMP ENT_CURR DEFAULT  NULLetime NOTe_time` dat',
  `updatMENT '创建时间IMESTAMP COMLT CURRENT_T NULL DEFAUime NOT datettime`e_eat',
  `crCP服务器请求头配置ENT 'MMM()) COson_objectEFAULT (jers` json D`head配置',
  MCP服务器变量COMMENT ') ()json_objectULT (n DEFAsoables` j',
  `vari器描述'MCP服务MMENT n` text COescriptio',
  `d类型MCP服务器OMMENT 'OT NULL Carchar(32) N vr_type`
  `serveP服务器URL',NT 'MCMET NULL COMr(2048) NOha varc`url`
  T '租户ID',MMENLL CO NOT NU32)ar(nt_id` varch  `tena',
器名称 'MCP服务NTMENOT NULL COM55) rchar(2`name` va  ,
一标识'务器唯'MCP服ENT  NULL COMMOTarchar(32) N` v
  `idver` ( `mcp_serABLE;
CREATE Tr``mcp_serveEXISTS OP TABLE IF 配置信息
DR务器l)服toco Context Proel储MCP(ModP服务器表 - 存=

-- MC==========================
-- =====模块器管理-- 8. MCP服务=======
========================
-- =用户画布版本表';
ENT='de_ci COMMb4_unicoTE=utf8mb4 COLLAHARSET=utf8mB DEFAULT CNGINE=InnoD) Eanvas_id`)
d` (`user_canvas_i `idx_user_c`),
  KEYKEY (`idRIMARY 间',
  P '更新时 COMMENTT_TIMESTAMPDATE CURRENP ON UPMESTAMT CURRENT_TINULL DEFAULOT datetime Ndate_time` 时间',
  `upENT '创建MESTAMP COMMENT_TILT CURRNULL DEFAUNOT tetime ate_time` dareL配置',
  `cENT '版本DS COMM_object())T (jsonFAUL` json DE
  `dsl'版本描述',ext COMMENT on` tescripti  `d题',
COMMENT '版本标EFAULT NULL ar(255) Dvarch  `title` ,
户画布ID'OMMENT '用NULL COT (255) Narchas_id` var`user_canv标识',
  画布版本唯一COMMENT 'L ) NOT NUL(32rcharva
  `id` sion` (_vervascanser_ `uTE TABLEion`;
CREA_canvas_versEXISTS `userF BLE IOP TA户画布的版本历史
DR- 存储用 用户画布版本表 

--NT='画布模板表';_ci COMME_unicodeTE=utf8mb4tf8mb4 COLLARSET=uDEFAULT CHANE=InnoDB pe`)
) ENGIs_ty(`canvas_type`  `idx_canva`),
  KEYidIMARY KEY (`时间',
  PRCOMMENT '更新ESTAMP T_TIMTE CURRENP ON UPDAT_TIMESTAMLT CURRENDEFAUL  NUL NOTdatetimepdate_time` 
  `u间',T '创建时 COMMEN_TIMESTAMPCURRENTAULT L DEFme NOT NUL` dateticreate_time  `DSL配置',
COMMENT '模板ject()) (json_obLT DEFAUon dsl` js
  `'画布类型',COMMENT AULT NULL ar(32) DEFtype` varch
  `canvas_'模板描述',COMMENT t texn` ioscript板标题',
  `deMENT '模COMLL DEFAULT NUr(255) e` varcha `titl4字符串)',
 像(base6T '模板头OMMEN` text C  `avatar
画布模板唯一标识',NT 'COMME NULL OTrchar(32) N
  `id` valate` (vas_temp `canCREATE TABLE
emplate`;TS `canvas_tXISBLE IF EOP TA供的画布模板
DR系统提 - 存储

-- 画布模板表布表';OMMENT='用户画unicode_ci C8mb4_COLLATE=utfSET=utf8mb4 DEFAULT CHARB GINE=InnoDpe`)
) ENanvas_type` (`cs_tycanvax_KEY `idsion`),
  rmission` (`peisdx_perm
  KEY `i`),`user_id_user_id` (
  KEY `idx(`id`),Y KEY RIMAR时间',
  PNT '更新ESTAMP COMMET_TIMCURRENTE AMP ON UPDARRENT_TIMESTL DEFAULT CUNOT NULtetime daate_time` 时间',
  `upd COMMENT '创建STAMPURRENT_TIME CLL DEFAULTT NUdatetime NOe_time` 
  `creat'画布DSL配置',)) COMMENT t(son_objecAULT (jl` json DEF `ds类型',
  '画布NTOMMENULL C32) DEFAULT  varchar(canvas_type`',
  `T '画布描述 text COMMENiption`  `descrm)',
tea设置(me| '权限me' COMMENTFAULT 'NOT NULL DE16) rchar(n` vaiopermiss `,
 题'ENT '画布标 NULL COMMFAULThar(255) DEtitle` varc ` '用户ID',
 LL COMMENTNOT NUarchar(255) user_id` v  `4字符串)',
ase6MENT '画布头像(bt COM`avatar` tex标识',
  MENT '用户画布唯一NOT NULL COMvarchar(32) id` vas` (
  ``user_canBLE REATE TAnvas`;
C `user_caSTSLE IF EXIDROP TAB
户创建的画布信息存储用画布表 - 
-- 用户=========
=====================布管理模块
-- ===
-- 7. 画=============================
-- ==';
对话记录表NT='API COMMEcode_cib4_uniOLLATE=utf8m=utf8mb4 CAULT CHARSETInnoDB DEF) ENGINE=b_up`)
p` (`thumhumb_u_t`idxKEY d`),
  ounx_round` (`r,
  KEY `id`)on (`duratiuration`  KEY `idx_durce`),
source` (`soKEY `idx_),
  _id`erus` (`er_id_us KEY `idx
 ),og_id`(`dial` x_dialog_id`idY d`),
  KE (`iRY KEY间',
  PRIMACOMMENT '更新时TAMP ESURRENT_TIMTE C UPDAMESTAMP ONRENT_TIDEFAULT CURNOT NULL time e` datete_tim,
  `upda建时间'COMMENT '创TAMP RRENT_TIMESDEFAULT CUme NOT NULL datetime` e_ti',
  `creatMENT '点赞数 COMAULT '0'ULL DEFT N_up` int NOumb
  `thNT '对话轮次',MMET '0' CO DEFAULNULLNOT und` int ',
  `roENT '对话耗时 COMMDEFAULT '0'at NOT NULL ion` flo `durat 'DSL配置',
 t()) COMMENTjson_objecn DEFAULT (`dsl` jsoialog)',
  t|done|agenT '对话来源(nMENCOMNULL DEFAULT rchar(16) ` vaurce',
  `soen数量MENT '消耗的tokT '0' COMEFAUL NULL Dens` int NOT
  `tokMENT '引用信息',y()) COMson_arraEFAULT (json Derence` j `ref,
 消息内容''对话NT MEL COMNULT AULe` json DEF `messag',
 OMMENT '用户IDNULL CT 255) NOarchar(user_id` v用ID',
  `T '对话应LL COMMEN(32) NOT NUchard` var_idialog识',
  ` 'API对话记录唯一标ENTULL COMM Nhar(32) NOT` varc `idion` (
 4_conversatBLE `api_E TAon`;
CREATtii_4_conversaapXISTS ` IF E
DROP TABLE信息的对话记录和统计- 存储通过API进行表 API对话记录- 

-I令牌表';T='AP_ci COMMEN8mb4_unicodeCOLLATE=utfSET=utf8mb4 T CHARDB DEFAULINE=Inno)
) ENG(`beta`ta` _be `idx),
  KEY`source`source` (
  KEY `idx_g_id`),id` (`dialoialog_ KEY `idx_doken`),
 x_token` (`tY `id),
  KE(`tenant_id`id` dx_tenant_KEY `iken`),
  id`, `totenant_(` KEY MARY
  PRI新时间', COMMENT '更AMPENT_TIMEST UPDATE CURRTIMESTAMP ONCURRENT_T  NULL DEFAULNOT datetime e`date_tim
  `upMENT '创建时间',COMMESTAMP  CURRENT_TILTT NULL DEFAUime NO` datet_timeeate
  `cr,T 'Beta标识'ULL COMMENEFAULT Nrchar(255) D
  `beta` va',gent|dialog)ne|a '令牌来源(noCOMMENTNULL  DEFAULT rchar(16)rce` vaou,
  `s对话应用ID'L COMMENT 'FAULT NUL(32) DEvarcharog_id` ',
  `dial'API令牌 COMMENT NOT NULL5) (25harn` varcD',
  `tokeCOMMENT '租户I) NOT NULL  varchar(32_id`tenant
  `token` (E `api_
CREATE TABLken`;_to`apiSTS LE IF EXIOP TAB令牌信息
DR访问API令牌表 - 存储
-- API======
==================
-- ========6. API管理模块=======
-- ============== ===========';

--NT='对话记录表ode_ci COMMEb4_unicE=utf8mb4 COLLAT8mSET=utfCHART FAULDB DEnoIn
) ENGINE=er_id`)_id` (`usY `idx_user  KE(`name`),
name`  `idx_  KEY
),`dialog_id`g_id` (lox_diaid),
  KEY `Y (`id`KERY  PRIMA新时间',
 P COMMENT '更STAMNT_TIMETE CURREUPDAON TAMP ES CURRENT_TIMDEFAULTOT NULL  datetime Nte_time`间',
  `updaENT '创建时MESTAMP COMM CURRENT_TI DEFAULTme NOT NULLateti_time` d`create  D',
用户IMMENT 'ULT NULL CO) DEFArchar(255user_id` va
  `信息',COMMENT '引用n_array()) (jsoLT  json DEFAUence`  `refer
'对话消息内容',L COMMENT ULT NULjson DEFAmessage` ',
  ` '对话名称OMMENTL CDEFAULT NULchar(255) ` var
  `nameT '对话应用ID',LL COMMENNOT NUarchar(32) dialog_id` v',
  `话记录唯一标识L COMMENT '对NUL(32) NOT varchar
  `id`  (ersation` TABLE `convATE;
CREion`atS `conversBLE IF EXIST TAOP记录
DR与对话应用的对话话记录表 - 存储用户;

-- 对话应用表'i COMMENT='对unicode_c=utf8mb4_4 COLLATESET=utf8mbDEFAULT CHARB INE=InnoD`)
) ENGus` (`status `idx_state`),
  KEY(`prompt_typmpt_type`  `idx_pro  KEYuage`),
ngnguage` (`laY `idx_lame`),
  KE`nae` (`idx_namY KE  nant_id`),
`teant_id` (ten`idx_EY ,
  K KEY (`id`)
  PRIMARY更新时间',MP COMMENT 'RENT_TIMESTA CURUPDATEMP ON RENT_TIMESTALT CUR DEFAU NOT NULLdatetimete_time` pda',
  `uMENT '创建时间STAMP COMENT_TIME CURRAULT NULL DEFtime NOTatetime` d
  `create_1:有效)',:废弃, 应用状态(0 '对话'1' COMMENTT ULFA(1) DEtus` varchar
  `sta识库ID列表',NT '关联的知ME COMay())son_arr (jFAULTULL DET Nids` json NO  `kb_重排序模型ID',
LL COMMENT 'T NUchar(128) NOd` var
  `rerank_i引',NT '是否插入引用索' COMMEEFAULT '1NOT NULL D(1) charer` var,
  `do_ref索候选数量'MMENT '检LT '1024' COL DEFAUNOT NULt k` intop_量',
  `'返回结果数NT OMMEFAULT '6' CLL DEt NOT NUp_n` in',
  `to量相似度权重' COMMENT '向LT '0.3L DEFAU NOT NULatght` flolarity_weiector_simi',
  `v度阈值 '相似.2' COMMENT '0LTL DEFAUT NULfloat NOold` ty_thresh`similari置',
  MENT '提示词配ase!')) COMnowledge b in the knd was fountentrelevant coy! No ', 'Sorrresponse, 'empty_rray()', json_aameters, 'parr you?' can I do foatant, whur assist'm yo, 'Hi! I\ue' 'prologtem', '','sysjson_object(AULT (ULL DEFOT Nig` json Npt_confom)',
  `prancedimple|advT '提示词类型(sMEN COMimple' DEFAULT 'sT NULLr(16) NOype` varchaprompt_t  `
模型参数配置',语言OMMENT '大, 512)) Ckens', 'max_to.4', 0nce_penaltyse'pre', 0.7, ncy_penalty, 'frequep_p', 0.3, 0.1, 'totemperature'object('son_T (jDEFAULOT NULL n N` jsoetting`llm_s
  语言模型ID', '大 COMMENT8) NOT NULL` varchar(12  `llm_idse)',
|ChineEnglish'语言设置(' COMMENT ChineseAULT ' DEFarchar(32)nguage` v`la,
  )'字符串应用图标(base64T ' text COMMENon`  `ic',
T '对话应用描述MMENxt COription` teesc`d用名称',
  OMMENT '对话应 CDEFAULT NULLrchar(255) ame` va `nID',
 租户MENT 'NOT NULL COMrchar(32) va_id` `tenant用唯一标识',
  NT '对话应MMEOT NULL COr(32) N` varcha  `idg` (
loia`d TABLE `;
CREATE `dialogLE IF EXISTS TABOP信息
DR用的配置用表 - 存储对话应 对话应--=

==========================-- =====块
话管理模5. 对- ======
-======================== ==';

--任务表MMENT='i COde_c_unicoLATE=utf8mb48mb4 COLCHARSET=utfT B DEFAULINE=InnoD`)
) ENGressog(`prx_progress` idEY ``),
  K` (`begin_ategin_atdx_b`i  KEY oc_id`),
c_id` (`dKEY `idx_doid`),
  KEY (`IMARY   PR '更新时间',
ENTMMTIMESTAMP COCURRENT_UPDATE TAMP ON RRENT_TIMESLT CUEFAUULL D Nime NOT_time` datetdate,
  `up创建时间'MMENT 'IMESTAMP COT_TREN CUR DEFAULT NULL NOTe` datetimee_tim
  `creat文档块ID列表',OMMENT ''' CT xt DEFAULds` longtenk_i要',
  `chuNT '任务摘ULT '' COMMEt DEFAgest` tex',
  `diT '重试次数OMMENLT '0' CEFAUt NOT NULL Dount` in
  `retry_c'进度消息',COMMENT AULT '' EFg` text Dgress_ms  `pro',
任务进度0' COMMENT 'DEFAULT 'L T NUL NOfloatrogress` `p处理耗时',
   COMMENT 'DEFAULT '0'at NOT NULL  flouration`process_d  `始时间',
ENT '任务开COMML DEFAULT NULime n_at` datet `begi任务优先级',
 MMENT 'COULT '0' EFANULL DNOT  int priority`务类型',
  `OMMENT '任T '' C NULL DEFAUL32) NOTe` varchar(sk_typta,
  `NT '结束页码'COMME'100000000' ULL DEFAULT nt NOT Nage` i
  `to_p '起始页码',ENTULT '0' COMMOT NULL DEFAge` int Nfrom_pa'文档ID',
  `NT OMMEL C NOT NULrchar(32)_id` va',
  `docT '任务唯一标识LL COMMEN NU2) NOT(3ar `id` varch`task` (
 ABLE REATE T `task`;
CTSABLE IF EXISROP T文档处理任务信息
D任务表 - 存储- 关联表';

-MMENT='文件文档code_ci COutf8mb4_uniLATE=utf8mb4 COLRSET=ULT CHAnoDB DEFA ENGINE=Inid`)
)cument_nt_id` (`doumedocY `idx_id`),
  KEile_file_id` (`f`idx_  KEY 
id`),Y KEY (`MAR
  PRI时间', COMMENT '更新ESTAMPIM CURRENT_TDATEN UPTAMP ORRENT_TIMESFAULT CU DEime NOT NULLtet daupdate_time` `建时间',
  '创ENTMESTAMP COMMT_TILT CURRENT NULL DEFAUNOtetime me` dae_ti  `creatID',
MENT '文档L COMFAULT NULr(32) DE varchament_id`ocu件ID',
  `dMMENT '文LT NULL COEFAUhar(32) Did` varc
  `file_',一标识联关系唯MENT '关 COM) NOT NULL(32varchar
  `id` cument` (e2do TABLE `fil
CREATEocument`;e2dEXISTS `fil TABLE IF 关联关系
DROP储文件与文档的 - 存档关联表- 文件文';

-NT='文件表i COMMEnicode_cb4_uutf8m COLLATE=T=utf8mb4ARSEDEFAULT CHnnoDB INE=INGe`)
) E`source_typ (source_type` KEY `idx_,
 (`type`)type` KEY `idx_
   (`size`),_size` KEY `idx
 on`), (`location`tidx_locaY `ime`),
  KEname` (`na
  KEY `idx_`),ed_by`creatreated_by` (Y `idx_c_id`),
  KEenant(`tt_id` tenan `idx_
  KEYparent_id`), (`rent_id``idx_pa),
  KEY Y (`id`KE
  PRIMARY ENT '更新时间',MMAMP CO_TIMESTNTE CURREDATAMP ON UPIMESTLT CURRENT_TNULL DEFAUatetime NOT te_time` d,
  `updaENT '创建时间'P COMM_TIMESTAMAULT CURRENTL DEFe NOT NULatetimtime` de_  `creat件来源类型',
ENT '文MMLT '' COFAUOT NULL DEar(128) Nch varource_type`件类型',
  `sOMMENT '文L C2) NOT NULarchar(3type` v
  `小',COMMENT '文件大0' LT ' DEFAU NOT NULLe` int置',
  `sizCOMMENT '存储位L AULT NUL) DEF255har(ation` varc
  `loc',NT '文件或文件夹名称ULL COMMENOT Nr(255) rchame` va `na建者ID',
 COMMENT '创 NULL OTr(32) N varchad_by`reate
  `cMENT '租户ID',L COM NUL32) NOTar(rchd` vaant_iten `ID',
 MENT '父文件夹NULL COMT 32) NOrchar(vaparent_id` 唯一标识',
  `OMMENT '文件LL COT NUr(32) N` varcha`id` (
  TABLE `fileATE ;
CRE`file`F EXISTS LE IP TAB文件夹信息
DRO的文件和表 - 存储系统中
-- 文件=
============================
-- ===管理模块-- 4. 文件=========
================== =====
--ENT='文档表';
 COMMnicode_ci_u=utf8mb4LATEOL=utf8mb4 CRSETFAULT CHA=InnoDB DE
) ENGINEtus`)us` (`staEY `idx_statn`),
  Kn` (`ru`idx_ru  KEY `suffix`),
dx_suffix` (  KEY `i_at`),
egincess_b` (`progin_ats_beesproc
  KEY `idx_s`),s` (`progresogres `idx_prm`),
  KEY` (`chunk_nu_chunk_numidx ` KEY_num`),
 tokennum` (``idx_token_Y ,
  KEsize`)` (`dx_size,
  KEY `ication`)on` (`lo `idx_locatiKEYname`),
  idx_name` (` ` KEYd_by`),
 teea(`cr_by` eatedEY `idx_crype`),
  K(`tidx_type` ),
  KEY `ource_type`e` (`ssource_typ
  KEY `idx_er_id`),r_id` (`parsrse `idx_paKEYid`),
  (`kb_`idx_kb_id` Y `id`),
  KERY KEY (RIMA时间',
  P'更新P COMMENT NT_TIMESTAMUPDATE CURREMESTAMP ON  CURRENT_TIULL DEFAULTetime NOT Ntime` date_',
  `updatNT '创建时间P COMME_TIMESTAMAULT CURRENTULL DEFme NOT Ntetiime` daate_t
  `cre 1:有效)','文档状态(0:废弃,'1' COMMENT DEFAULT char(1) vartatus` 
  `s行, 2:取消)',停止, 1:运 '运行状态(0: COMMENTAULT '0'har(1) DEFun` varc扩展名',
  `rENT '真实文件OT NULL COMM) Nhar(32ix` varcuff  `s据字段',
ENT '元数()) COMMobjectULT (json_FADEelds` json ta_fi
  `meT '处理耗时',COMMENDEFAULT '0' t NOT NULL ation` floaprocess_dur `理开始时间',
 OMMENT '处L CULEFAULT N` datetime Datess_begin_`proc消息',
  处理NT 'MMECO' FAULT 'sg` text DErogress_m `p处理进度',
 MENT ' '0' COMFAULT DEULL float NOT Nress`',
  `progT '文档块数量COMMENFAULT '0'  NULL DEnt NOT` iunk_num',
  `ch'token数量 COMMENT ULT '0'EFANULL Dnt NOT en_num` i',
  `tokMENT '文件大小 COMAULT '0' NULL DEFint NOTize` 置',
  `s '存储位 COMMENTLT NULL55) DEFAU` varchar(2location,
  `'文件名'COMMENT ULL DEFAULT N55) rchar(2name` va
  `建者ID',MMENT '创OT NULL COarchar(32) N_by` v  `created,
MENT '文件扩展名'NULL COMOT ) N` varchar(32,
  `type源类型'ENT '文档来ocal' COMMFAULT 'lT NULL DEar(128) NOtype` varchrce_置',
  `sou解析器配)) COMMENT '00)), 10000on_array(1son_array(js jges',_object('pa (jsonFAULTLL DEson NOT NU` jr_configse
  `par'解析器ID',L COMMENT (32) NOT NULrchar vad`ser_i',
  `parIDNT '知识库COMMET NULL ) NOchar(256kb_id` var字符串)',
  `图(base64MENT '文档缩略COM` text  `thumbnail'文档唯一标识',
 ENT MMOT NULL COchar(32) N `id` varcument` (
 LE `do TAB;
CREATEt`mencuEXISTS `doTABLE IF P 信息
DRO存储知识库中的文档 文档表 - 
--库表';
COMMENT='知识_ci b4_unicodeLLATE=utf8mCO=utf8mb4 LT CHARSETDB DEFAUNGINE=Inno) Estatus`)
tus` (``idx_sta
  KEY ser_id`), (`parer_id``idx_parst`),
  KEY weighlarity_`vector_simiy_weight` (r_similarittoY `idx_vec
  KEold`),rity_thresh` (`similaholdy_threslaritmidx_si`i,
  KEY num`)hunk_(`cm` _chunk_nu KEY `idx`),
  (`token_numoken_num` KEY `idx_t
 `), (`doc_numidx_doc_num``),
  KEY `eated_bycrted_by` (`dx_crea KEY `i,
 rmission`)pe` (`sionx_permis`id
  KEY (`embd_id`),d_id` KEY `idx_embge`),
  languaanguage` (` `idx_l
  KEY` (`name`),EY `idx_name_id`),
  K` (`tenanttenant_iddx_`i  KEY d`),
`iY KEY (PRIMAR  更新时间',
 COMMENT 'IMESTAMPCURRENT_T UPDATE ONAMP _TIMESTAULT CURRENTOT NULL DEFetime Nime` dat `update_t,
 ENT '创建时间'MMAMP COESTIMNT_T CURREL DEFAULTT NULetime NO` dateate_time `cr效)',
 1:有识库状态(0:废弃,  '知1' COMMENTULT 'r(1) DEFAarcha` vstatus名',
  `OMMENT '页面排LT '0' CAUOT NULL DEFk` int N
  `pageran',ENT '解析器配置MM000)))) CO00, 10json_array(1ay(n_arrjso', 'pagesobject(son_EFAULT (j NULL Dn NOTg` jsoarser_confi
  `pID',OMMENT '解析器naive' CFAULT 'T NULL DE32) NOchar(` var`parser_id
  向量相似度权重',COMMENT '.3'  DEFAULT '0 NOT NULLeight` floatimilarity_wr_s`vecto',
   '相似度阈值 COMMENTAULT '0.2'NOT NULL DEFt old` floareshty_th `similari',
 '文档块数量MMENT '0' COLL DEFAULT  int NOT NU`chunk_num`,
  数'oken总't COMMENT DEFAULT '0'NOT NULL _num` int ',
  `tokenT '文档数量T '0' COMMENULL DEFAUL int NOT Nnum`
  `doc_',NT '创建者IDL COMMENUL2) NOT archar(3ated_by` v `cre',
 e|team)ENT '权限设置(mCOMMULT 'me' T NULL DEFA(16) NOion` varcharmiss`per入模型ID',
  '嵌LL COMMENT 128) NOT NUid` varchar(
  `embd_T '知识库描述', COMMENtext` description
  `Chinese)',(English|设置语言ENT 'ese' COMMAULT 'Chin) DEF32har(` varcguage `lan称',
 '知识库名OMMENT  NULL C) NOTrchar(128 va`name`  ID',
ENT '租户OT NULL COMMhar(32) Nant_id` varc`ten',
  字符串)base64MENT '知识库头像( COMxtte  `avatar` 库唯一标识',
OMMENT '知识ULL C(32) NOT Nrchar vad`(
  `iase` gebnowledABLE `k;
CREATE Te`knowledgebasTS `ABLE IF EXIS
DROP T信息和配置 - 存储知识库的基本-- 知识库表

===============================
-- = 3. 知识库管理模块===
--=======================

-- ======ngfuse配置表';ENT='租户Lai COMMb4_unicode_cutf8mATE=b4 COLLT=utf8mARSEEFAULT CHB DoDGINE=Inn) EN` (`host`)
dx_host,
  KEY `i(255))key`c_key` (`publiublic__pEY `idx55)),
  Kcret_key`(2` (`set_keyx_secre `idid`),
  KEY`tenant_EY ( K
  PRIMARY,MENT '更新时间' COM_TIMESTAMPRENTPDATE CURP ON UTIMESTAMT_ CURRENFAULTLL DET NUatetime NO dtime`date_时间',
  `upOMMENT '创建 CTIMESTAMPURRENT_T CDEFAULT NULL  datetime NOeate_time``cre主机地址',
  T 'LangfusMENL COMULT N128) NOar(ost` varch`h,
  'Langfuse公钥'LL COMMENT  NU2048) NOTar(y` varchpublic_kese密钥',
  `LangfuENT 'NULL COMM) NOT r(2048 varchasecret_key`D',
  `NT '租户IT NULL COMMEhar(32) NO` varc`tenant_id  ` (
_langfuseenantE `t TABL`;
CREATEgfuselant_TS `tenanISF EXOP TABLE I
DRse监控配置储租户的Langfu置表 - 存ngfuse配
-- 租户La
='租户模型配置表';_ci COMMENT_unicodeLATE=utf8mb4tf8mb4 COL=uAULT CHARSET=InnoDB DEF ENGINE
)ens`)okused_t (`s`tokendx_used_`is`),
  KEY tokenmax__tokens` (` `idx_maxEY5)),
  Kapi_key`(25key` (`EY `idx_api_ame`),
  Klm_name` (`l_llm_nY `idx,
  KEel_type`) (`modpe`model_ty KEY `idx_y`),
 (`llm_factor_factory` KEY `idx_llm  _id`),
enantnant_id` (`tY `idx_te KE,
 _name`)y`, `llmor `llm_factenant_id`,Y (`t PRIMARY KE
 MENT '更新时间',IMESTAMP COMRENT_TATE CURPDON UMESTAMP _TIRENTDEFAULT CURULL e NOT Netime` datate_tim,
  `upd间''创建时NT MP COMMETIMESTAULT CURRENT_OT NULL DEFAme N datetiate_time`ren数',
  `c'已使用tokeT  COMMENLT '0'FAUOT NULL DEt Nns` in`used_toketoken数',
  MENT '最大2' COMULT '819LL DEFAint NOT NU` okensmax_t `RL',
 T 'API基础U COMMENFAULT NULLhar(255) DE varci_base`I密钥',
  `apAPNT 'LL COMMEULT NU8) DEFA varchar(204`api_key`  NT '模型名称',
 COMMEDEFAULT ''8) rchar(12m_name` va)',
  `llt, ASRage2Texg, ImbeddinLM, Text Em'模型类型(LL COMMENT ULT NUL(128) DEFAar_type` varchmodel商名称',
  `模型厂MMENT ' NOT NULL COarchar(128)ry` vllm_factoID',
  `'租户MENT ULL COMNOT N32) d` varchar(t_ienan
  `tenant_llm` ( `t TABLECREATEt_llm`;
S `tenanF EXISTDROP TABLE I型配置信息
表 - 存储租户的模户模型配置表';

-- 租模型OMMENT='大语言_ci Cde4_unicotf8mbLATE=ub4 COLT=utf8m CHARSEoDB DEFAULTE=InnIN
) ENGtus`)us` (`staidx_stat),
  KEY `gs`` (`taY `idx_tagsKEid`),
  fid` (`f `idx_,
  KEYodel_type`)l_type` (`midx_mode  KEY `,
m_name`) (`llme` `idx_llm_na
  KEYlm_name`),id`, `lMARY KEY (`f时间',
  PRIT '更新MMENAMP CORENT_TIMESTUR ON UPDATE CTIMESTAMPULT CURRENT_OT NULL DEFAatetime Nme` ddate_ti`up建时间',
  T '创 COMMENTIMESTAMPNT_ULT CURRE DEFAT NULL NOe` datetimee_timreat`c,
  :有效)', 1型状态(0:废弃OMMENT '模' CDEFAULT '1r(1) archatus` v',
  `sta支持工具调用NT '是否OMMEULT '0' CT NULL DEFA) NO` tinyint(1`is_tools',
  k...)hat, 32t, C, Image2Texddingext Embe模型标签(LLM, TENT 'MMULL CONOT N5)  varchar(25gs`  `ta
en数',MMENT '最大tok' CODEFAULT '0LL nt NOT NUtokens` i  `max_ID',
 COMMENT '厂商LL) NOT NU(128` varchar)',
  `fidSRxt, AImage2Tebedding, , Text Em类型(LLMMMENT '模型NOT NULL CO8) archar(12l_type` v称',
  `mode型名 '模COMMENTLL  NOT NUarchar(128) v  `llm_name`lm` (
ABLE `lATE T
CRE`; EXISTS `llmLE IFAB
DROP T言模型信息型表 - 存储具体的大语 大语言模
--
语言模型厂商表';i COMMENT='大_cb4_unicode8mCOLLATE=utfSET=utf8mb4 ARCHLT AUEFoDB DGINE=Inn`)
) ENtatusus` (`s `idx_statgs`),
  KEYtas` (` `idx_tag`),
  KEYameRY KEY (`n
  PRIMANT '更新时间',MP COMMEENT_TIMESTA UPDATE CURRMP ONRENT_TIMESTAUREFAULT CULL Dtime NOT Ntete_time` dadaup间',
  `MENT '创建时STAMP COMURRENT_TIME DEFAULT Ce NOT NULLetime_time` dat
  `creat废弃, 1:有效)',(0: '厂商状态MENT' COMT '1DEFAULvarchar(1) `status` ,
  t, ASR)'Image2Texng, beddiEm Text (LLM,型类型标签T '支持的模OMMEN NOT NULL Cchar(255)ags` var串)',
  `tbase64字符ENT '厂商Logo(t COMM`logo` tex'厂商名称',
  MENT  NULL COMhar(128) NOTname` varcies` (
  `ctor `llm_faE TABLE
CREATes`;actori`llm_fSTS XIABLE IF E商信息
DROP T 存储支持的大语言模型厂 大语言模型厂商表 -=

--======================== =======型管理模块
--===
-- 2. 模================== ===========邀请码表';

-- COMMENT='ode_ci4_unicE=utf8mb4 COLLATSET=utf8mbARDEFAULT CHINE=InnoDB s`)
) ENG` (`statuidx_status
  KEY `d`),(`tenant_int_id` x_tena  KEY `idd`),
ser_id` (`user_iY `idx_u KEt_time`),
  (`visime`ti`idx_visit_KEY 
  ode`),_code` (`c `idxKEY`id`),
  EY (PRIMARY K更新时间',
  OMMENT 'MP CSTAT_TIMECURRENPDATE N UESTAMP O_TIMRENTDEFAULT CURNOT NULL time ime` datee_t
  `updat间',创建时MENT ' COMESTAMPURRENT_TIMLL DEFAULT C NUOT Nme` datetimeate_ti `cre1:有效)',
 请码状态(0:废弃, '邀MMENT CO1' ULT 'r(1) DEFAtus` varcha
  `sta的租户ID',OMMENT '关联NULL CLT AUEFrchar(32) D_id` vaant
  `ten,用邀请码的用户ID' COMMENT '使LL NUEFAULT D varchar(32)`user_id`  问时间',
COMMENT '访NULL ime DEFAULT atett_time` d`visiT '邀请码',
  NULL COMMENar(32) NOT varch `code` 请码唯一标识',
 MMENT '邀NULL COar(32) NOT ` varchide` (
  `codnvitation_ TABLE `i;
CREATE_code`ioninvitat `IF EXISTSDROP TABLE 系统邀请码信息
表 - 存储
-- 邀请码用户租户关联表';
ENT='ci COMMb4_unicode_f8mLLATE=utmb4 COHARSET=utf8 CB DEFAULTENGINE=InnoD`)
) atusst_status` (`EY `idx`),
  Kinvited_by` (`vited_byindx_  KEY `i
 (`role`),role`EY `idx_d`),
  Knt_id` (`tenax_tenant_i KEY `id_id`),
 `user` (dx_user_id`i
  KEY Y (`id`),MARY KEPRI'更新时间',
  T STAMP COMMEN_TIMEURRENT ON UPDATE CMPRENT_TIMESTAT CURL DEFAUL NOT NULe` datetimedate_tim',
  `upNT '创建时间ESTAMP COMMECURRENT_TIML DEFAULT  NULatetime NOTeate_time` d
  `cr废弃, 1:有效)',(0:MMENT '关联状态1' CO '(1) DEFAULTs` varchar  `statu '邀请人ID',
LL COMMENT) NOT NUar(32d_by` varch `invite租户中的角色',
  '用户在L COMMENT NOT NULr(32)archale` v,
  `ro '租户ID'ULL COMMENTOT Nchar(32) Nvartenant_id` ,
  ` '用户ID'ENTMMCOL 32) NOT NULr(cha_id` var
  `user标识',系唯一MMENT '关联关L COUL(32) NOT N`id` varchar  enant` (
_t `userABLECREATE Tnt`;
 `user_tena EXISTSIFOP TABLE 联关系和角色信息
DR 存储用户与租户的关租户关联表 -
-- 用户ENT='租户表';
code_ci COMM8mb4_unitfATE=utf8mb4 COLLET=uCHARS DEFAULT INE=InnoDB
) ENGus`)atus` (`statdx_st
  KEY `i),(`credit`idx_credit` KEY `ds`),
  parser_iser_ids` (``idx_parY ,
  KE(`tts_id`)_tts_id` Y `idxid`),
  KE` (`rerank__rerank_id `idx),
  KEY2txt_id` (`imgimg2txt_id``idx_d`),
  KEY asr_ir_id` (`dx_as KEY `i
 embd_id`),embd_id` (`  KEY `idx_
lm_id`),(`lidx_llm_id` ),
  KEY `ic_key``publ (_public_key`idxEY `me`),
  Kame` (`na`idx_n KEY `),
 Y KEY (`id
  PRIMART '更新时间',STAMP COMMEN_TIME CURRENT ON UPDATEMESTAMPRENT_TICURLL DEFAULT time NOT NUdatepdate_time` 
  `u建时间',MMENT '创IMESTAMP COT CURRENT_T DEFAULOT NULLime Ne` datet `create_tim 1:有效)',
 租户状态(0:废弃,1' COMMENT 'T ' DEFAULvarchar(1)`status` 分余额',
  NT '积ME'512' COMDEFAULT  NOT NULL redit` int
  `c表', '文档解析器ID列NULL COMMENT NOT har(256)ds` varc`parser_i
  文本转语音模型ID', '默认L COMMENTT NUL56) DEFAULrchar(2`tts_id` vaD',
  T '默认重排序模型ICOMMEN8) NOT NULL ` varchar(12`rerank_idD',
  文本模型IMMENT '默认图像转OT NULL COchar(128) Nt_id` varimg2tx型ID',
  `识别模MMENT '默认语音LL CO) NOT NU(128hard` varc
  `asr_i认嵌入模型ID',MENT '默OT NULL COM28) Nvarchar(1id` ,
  `embd_认大语言模型ID'OMMENT '默ULL C) NOT Nhar(128llm_id` varc',
  `ENT '公钥COMMLT NULL AUar(255) DEFy` varchke
  `public_'租户名称', COMMENT AULT NULL) DEF varchar(100',
  `name`租户唯一标识L COMMENT ' NULr(32) NOTd` varcha
  `i `tenant` ( TABLETE`;
CREAS `tenantF EXISTTABLE IP 默认配置
DRO本信息和储租户(组织)的基-- 租户表 - 存户表';

'用OMMENT=ci C4_unicode_TE=utf8mbOLLAT=utf8mb4 CCHARSEEFAULT nnoDB D
) ENGINE=Iruser`)s_supeperuser` (`iidx_is_su KEY `s`),
 s` (`statu`idx_statu),
  KEY nel`chanin_` (`log_channeldx_login
  KEY `inymous`), (`is_anoanonymous`_is_ `idx
  KEYtive`),`is_active` (Y `idx_is_ac`),
  KEcatednti`is_autheticated` (hens_autdx_i`iEY   Ktime`),
ast_login_n_time` (`lt_logiidx_las`),
  KEY ` (`timezonezone`timeidx_,
  KEY `ema`)_sch (`colorolor_schema` KEY `idx_c`),
 ge (`language`uadx_lang`iEY l`),
  Kai_email` (`em
  KEY `idx`), (`passwordrd` `idx_passwo),
  KEYame`kn(`nicx_nickname` 
  KEY `ids_token`),ccesen` (`ass_tokY `idx_acceid`),
  KEEY (`  PRIMARY K
更新时间',MENT 'MESTAMP COMTIT_E CURRENMP ON UPDATTIMESTAT CURRENT_L DEFAUL NOT NULimedatete_time` at `upd
 MENT '创建时间',STAMP COMT_TIMERENAULT CUROT NULL DEFime N datet_time`create `',
 超级用户T '是否'0' COMMENAULT EF1) Dtinyint(ser`  `is_superu
 ',(0:废弃, 1:有效)NT '用户状态 '1' COMMELTr(1) DEFAU varchastatus`录渠道',
  ` COMMENT '登LLLT NU55) DEFAUchar(2annel` varn_ch  `logi '是否匿名用户',
MENT '0' COMAULTLL DEFar(1) NOT NUymous` varch_anon',
  `isOMMENT '是否激活T '1' CULOT NULL DEFAvarchar(1) N` ctives_a',
  `i否已认证MMENT '是ULT '1' COOT NULL DEFAhar(1) Ned` varcnticatauthes_,
  `i后登录时间''最LL COMMENT e DEFAULT NUimetme` datast_login_ti时区设置',
  `lENT 'ghai' COMM\tAsia/ShanC+8ULT 'UT DEFAarchar(64) vezone`k)',
  `timht|Dar主题(BrigMENT '颜色ight' COMEFAULT 'Br) Dar(32rcha` va`color_schem  e)',
sh|ChinesgliEn '语言设置( COMMENTLT 'Chinese'2) DEFAUarchar(3` v`language符串)',
  头像(base64字MENT '用户` text COMvatar
  `a邮箱', '用户ULL COMMENT NOT Nchar(255)` var,
  `emailMENT '用户密码'LT NULL COMFAU DEr(255)` varcha `password昵称',
 '用户 COMMENT ULL0) NOT N` varchar(10  `nickname',
问令牌'访ULL COMMENT ) DEFAULT Nrchar(255ss_token` va
  `acce标识', '用户唯一COMMENTT NULL (32) NO varchar` (
  `id`LE `userREATE TAB `user`;
C IF EXISTSTABLEP 认证相关数据
DRO基本信息和- 存储系统用户的
-- 用户表 
============================-- ====