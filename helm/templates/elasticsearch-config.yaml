{{- if eq .Values.env.DOC_ENGINE "elasticsearch" -}}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "ragflow.fullname" . }}-es-config
data:
  node.name: "es01"
  bootstrap.memory_lock: "false"
  discovery.type: "single-node"
  xpack.security.enabled: "true"
  xpack.security.http.ssl.enabled: "false"
  xpack.security.transport.ssl.enabled: "false"
  cluster.routing.allocation.disk.watermark.low: 5gb
  cluster.routing.allocation.disk.watermark.high: 3gb
  cluster.routing.allocation.disk.watermark.flood_stage: 2gb
  TZ: {{ .Values.env.TIMEZONE }}
{{- end -}}
