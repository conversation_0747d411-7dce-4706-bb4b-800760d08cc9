"""
批量关键词处理工具类

提供关键词合并、验证和批量操作结果处理的核心功能
"""
import logging
from dataclasses import dataclass
from typing import Any, Dict, List, Optional, Set, Union

logger = logging.getLogger(__name__)


@dataclass
class BatchResult:
    """批量操作结果数据类"""
    success_count: int
    failed_count: int
    failed_chunks: List[Dict[str, str]]  # {"chunk_id": "error_message"}
    
    def add_success(self):
        """增加成功计数"""
        self.success_count += 1
    
    def add_failure(self, chunk_id: str, error_message: str):
        """添加失败记录"""
        self.failed_count += 1
        self.failed_chunks.append({"chunk_id": chunk_id, "error": error_message})


@dataclass
class PreviewResult:
    """预览结果数据类"""
    preview_data: Dict[str, Dict[str, List[str]]]  # chunk_id -> keyword comparison
    
    def add_preview(self, chunk_id: str, original_keywords: List[str], merged_keywords: List[str]):
        """添加预览数据"""
        self.preview_data[chunk_id] = {
            "original_keywords": original_keywords,
            "merged_keywords": merged_keywords
        }


class KeywordMerger:
    """关键词合并器"""
    
    @staticmethod
    def validate_keywords(keywords: Any) -> bool:
        """
        验证关键词列表格式
        
        Args:
            keywords: 关键词列表
            
        Returns:
            bool: 验证结果
        """
        if not isinstance(keywords, list):
            return False
        
        for keyword in keywords:
            if not isinstance(keyword, str):
                return False
            if not keyword.strip():  # 空字符串或只有空格
                return False
        
        return True
    
    @staticmethod
    def merge_keywords(existing_keywords: Union[List[str], None], new_keywords: Union[List[str], None]) -> List[str]:
        """
        合并关键词列表，自动去重
        
        Args:
            existing_keywords: 现有关键词列表
            new_keywords: 新增关键词列表
            
        Returns:
            List[str]: 合并后的关键词列表
        """
        # 确保输入是列表
        if not isinstance(existing_keywords, list):
            existing_keywords = []
        if not isinstance(new_keywords, list):
            new_keywords = []
        
        # 清理关键词：去除空白字符
        existing_clean = [kw.strip() for kw in existing_keywords if isinstance(kw, str) and kw.strip()]
        new_clean = [kw.strip() for kw in new_keywords if isinstance(kw, str) and kw.strip()]
        
        # 使用集合去重，保持顺序
        seen: Set[str] = set()
        merged = []
        
        # 先添加现有关键词
        for keyword in existing_clean:
            if keyword not in seen:
                seen.add(keyword)
                merged.append(keyword)
        
        # 再添加新关键词
        for keyword in new_clean:
            if keyword not in seen:
                seen.add(keyword)
                merged.append(keyword)
        
        return merged
    
    @staticmethod
    def get_new_keywords(existing_keywords: List[str], merged_keywords: List[str]) -> List[str]:
        """
        获取新增的关键词列表
        
        Args:
            existing_keywords: 现有关键词列表
            merged_keywords: 合并后的关键词列表
            
        Returns:
            List[str]: 新增的关键词列表
        """
        existing_set = set(existing_keywords) if existing_keywords else set()
        merged_set = set(merged_keywords) if merged_keywords else set()
        
        return list(merged_set - existing_set)


class ChunkDataAccessor:
    """Chunk数据访问器"""
    
    def __init__(self, settings, search_module, rag_tokenizer):
        """
        初始化数据访问器
        
        Args:
            settings: 系统设置对象
            search_module: 搜索模块
            rag_tokenizer: 分词器
        """
        self.settings = settings
        self.search = search_module
        self.rag_tokenizer = rag_tokenizer
    
    def get_chunks_data(self, chunk_ids: List[str], tenant_id: str, kb_ids: List[str]) -> Dict[str, Dict]:
        """
        获取多个chunk的现有数据
        
        Args:
            chunk_ids: chunk ID列表
            tenant_id: 租户ID
            kb_ids: 知识库ID列表
            
        Returns:
            Dict[str, Dict]: chunk_id -> chunk_data的映射
        """
        chunks_data = {}
        
        for chunk_id in chunk_ids:
            try:
                chunk = self.settings.docStoreConn.get(
                    chunk_id, 
                    self.search.index_name(tenant_id), 
                    kb_ids
                )
                if chunk:
                    chunks_data[chunk_id] = chunk
                else:
                    logger.warning(f"Chunk not found: {chunk_id}")
            except Exception as e:
                logger.error(f"Error getting chunk {chunk_id}: {str(e)}")
        
        return chunks_data
    
    def update_chunk_keywords(self, chunk_id: str, merged_keywords: List[str], 
                            tenant_id: str, kb_id: str) -> bool:
        """
        更新单个chunk的关键词
        
        Args:
            chunk_id: chunk ID
            merged_keywords: 合并后的关键词列表
            tenant_id: 租户ID
            kb_id: 知识库ID
            
        Returns:
            bool: 更新是否成功
        """
        try:
            update_data = {
                "important_kwd": merged_keywords,
                "important_tks": self.rag_tokenizer.tokenize(" ".join(merged_keywords))
            }
            
            success = self.settings.docStoreConn.update(
                {"id": chunk_id}, 
                update_data, 
                self.search.index_name(tenant_id), 
                kb_id
            )
            
            if success:
                logger.info(f"Successfully updated keywords for chunk {chunk_id}")
            else:
                logger.error(f"Failed to update keywords for chunk {chunk_id}")
            
            return success
        except Exception as e:
            logger.error(f"Error updating chunk {chunk_id}: {str(e)}")
            return False


class BatchProcessor:
    """批量处理器"""
    
    def __init__(self, chunk_accessor: Optional[ChunkDataAccessor] = None):
        self.keyword_merger = KeywordMerger()
        self.chunk_accessor = chunk_accessor
    
    def create_batch_result(self) -> BatchResult:
        """创建批量操作结果对象"""
        return BatchResult(
            success_count=0,
            failed_count=0,
            failed_chunks=[]
        )
    
    def create_preview_result(self) -> PreviewResult:
        """创建预览结果对象"""
        return PreviewResult(preview_data={})
    
    def validate_batch_request(self, chunk_ids: Any, keywords: Any) -> Optional[str]:
        """
        验证批量请求参数
        
        Args:
            chunk_ids: chunk ID列表
            keywords: 关键词列表
            
        Returns:
            Optional[str]: 错误信息，None表示验证通过
        """
        if not isinstance(chunk_ids, list) or not chunk_ids:
            return "chunk_ids必须是非空列表"
        
        if not isinstance(keywords, list) or not keywords:
            return "keywords必须是非空列表"
        
        if not self.keyword_merger.validate_keywords(keywords):
            return "keywords格式无效，必须是字符串列表且不能包含空字符串"
        
        # 检查chunk_ids数量限制
        if len(chunk_ids) > 1000:
            return "单次批量操作的chunk数量不能超过1000个"
        
        # 检查关键词数量限制
        if len(keywords) > 100:
            return "单次添加的关键词数量不能超过100个"
        
        return None
    
    def preview_batch_keywords(self, chunk_ids: List[str], new_keywords: List[str], 
                             tenant_id: str, kb_ids: List[str]) -> PreviewResult:
        """
        预览批量关键词合并效果
        
        Args:
            chunk_ids: chunk ID列表
            new_keywords: 新增关键词列表
            tenant_id: 租户ID
            kb_ids: 知识库ID列表
            
        Returns:
            PreviewResult: 预览结果
        """
        preview_result = self.create_preview_result()
        
        if not self.chunk_accessor:
            logger.error("ChunkDataAccessor not initialized")
            return preview_result
        
        # 获取现有chunk数据
        chunks_data = self.chunk_accessor.get_chunks_data(chunk_ids, tenant_id, kb_ids)
        
        for chunk_id in chunk_ids:
            chunk_data = chunks_data.get(chunk_id, {})
            existing_keywords = chunk_data.get("important_kwd", [])
            merged_keywords = self.keyword_merger.merge_keywords(existing_keywords, new_keywords)
            
            preview_result.add_preview(chunk_id, existing_keywords, merged_keywords)
        
        return preview_result
    
    def process_batch_keywords(self, chunk_ids: List[str], new_keywords: List[str], 
                             tenant_id: str, kb_ids: List[str], kb_id: str) -> BatchResult:
        """
        执行批量关键词添加
        
        Args:
            chunk_ids: chunk ID列表
            new_keywords: 新增关键词列表
            tenant_id: 租户ID
            kb_ids: 知识库ID列表
            kb_id: 知识库ID（用于更新）
            
        Returns:
            BatchResult: 批量操作结果
        """
        batch_result = self.create_batch_result()
        
        if not self.chunk_accessor:
            logger.error("ChunkDataAccessor not initialized")
            for chunk_id in chunk_ids:
                batch_result.add_failure(chunk_id, "数据访问器未初始化")
            return batch_result
        
        # 获取现有chunk数据
        chunks_data = self.chunk_accessor.get_chunks_data(chunk_ids, tenant_id, kb_ids)
        
        for chunk_id in chunk_ids:
            try:
                chunk_data = chunks_data.get(chunk_id, {})
                existing_keywords = chunk_data.get("important_kwd", [])
                merged_keywords = self.keyword_merger.merge_keywords(existing_keywords, new_keywords)
                
                # 更新chunk关键词
                success = self.chunk_accessor.update_chunk_keywords(chunk_id, merged_keywords, tenant_id, kb_id)
                
                if success:
                    batch_result.add_success()
                    logger.info(f"Successfully processed chunk {chunk_id}")
                else:
                    batch_result.add_failure(chunk_id, "数据库更新失败")
                    
            except Exception as e:
                error_msg = f"处理chunk时发生错误: {str(e)}"
                batch_result.add_failure(chunk_id, error_msg)
                logger.error(f"Error processing chunk {chunk_id}: {str(e)}")
        
        return batch_result

