#
#  Copyright 2024 The InfiniFlow Authors. All Rights Reserved.
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License
#
import json
import logging
from datetime import datetime
from functools import wraps
from timeit import default_timer as timer
from flask import request
from flask_login import current_user, login_required

from api import settings
from api.db.db_models import APIToken
from api.db.services.api_service import APITokenService
from api.db.services.knowledgebase_service import KnowledgebaseService
from api.db.services.user_service import UserService, UserTenantService
from api.utils import current_timestamp, datetime_format
from api.utils.api_utils import (
    generate_confirmation_token,
    get_data_error_result,
    get_json_result,
    server_error_response,
    validate_request,
)
from api.versions import get_ragflow_version
from rag.utils.redis_conn import REDIS_CONN
from rag.utils.storage_factory import STORAGE_IMPL, STORAGE_IMPL_TYPE


@manager.route("/version", methods=["GET"])  # noqa: F821
@login_required
def version():
    """
    Get the current version of the application.
    ---
    tags:
      - System
    security:
      - ApiKeyAuth: []
    responses:
      200:
        description: Version retrieved successfully.
        schema:
          type: object
          properties:
            version:
              type: string
              description: Version number.
    """
    return get_json_result(data=get_ragflow_version())


@manager.route("/status", methods=["GET"])  # noqa: F821
@login_required
def status():
    """
    Get the system status.
    ---
    tags:
      - System
    security:
      - ApiKeyAuth: []
    responses:
      200:
        description: System is operational.
        schema:
          type: object
          properties:
            es:
              type: object
              description: Elasticsearch status.
            storage:
              type: object
              description: Storage status.
            database:
              type: object
              description: Database status.
      503:
        description: Service unavailable.
        schema:
          type: object
          properties:
            error:
              type: string
              description: Error message.
    """
    res = {}
    st = timer()
    try:
        res["doc_engine"] = settings.docStoreConn.health()
        res["doc_engine"]["elapsed"] = "{:.1f}".format((timer() - st) * 1000.0)
    except Exception as e:
        res["doc_engine"] = {
            "type": "unknown",
            "status": "red",
            "elapsed": "{:.1f}".format((timer() - st) * 1000.0),
            "error": str(e),
        }

    st = timer()
    try:
        STORAGE_IMPL.health()
        res["storage"] = {
            "storage": STORAGE_IMPL_TYPE.lower(),
            "status": "green",
            "elapsed": "{:.1f}".format((timer() - st) * 1000.0),
        }
    except Exception as e:
        res["storage"] = {
            "storage": STORAGE_IMPL_TYPE.lower(),
            "status": "red",
            "elapsed": "{:.1f}".format((timer() - st) * 1000.0),
            "error": str(e),
        }

    st = timer()
    try:
        KnowledgebaseService.get_by_id("x")
        res["database"] = {
            "database": settings.DATABASE_TYPE.lower(),
            "status": "green",
            "elapsed": "{:.1f}".format((timer() - st) * 1000.0),
        }
    except Exception as e:
        res["database"] = {
            "database": settings.DATABASE_TYPE.lower(),
            "status": "red",
            "elapsed": "{:.1f}".format((timer() - st) * 1000.0),
            "error": str(e),
        }

    st = timer()
    try:
        if not REDIS_CONN.health():
            raise Exception("Lost connection!")
        res["redis"] = {
            "status": "green",
            "elapsed": "{:.1f}".format((timer() - st) * 1000.0),
        }
    except Exception as e:
        res["redis"] = {
            "status": "red",
            "elapsed": "{:.1f}".format((timer() - st) * 1000.0),
            "error": str(e),
        }

    task_executor_heartbeats = {}
    try:
        task_executors = REDIS_CONN.smembers("TASKEXE")
        now = datetime.now().timestamp()
        for task_executor_id in task_executors:
            heartbeats = REDIS_CONN.zrangebyscore(task_executor_id, now - 60 * 30, now)
            heartbeats = [json.loads(heartbeat) for heartbeat in heartbeats]
            task_executor_heartbeats[task_executor_id] = heartbeats
    except Exception:
        logging.exception("get task executor heartbeats failed!")
    res["task_executor_heartbeats"] = task_executor_heartbeats

    return get_json_result(data=res)


@manager.route("/new_token", methods=["POST"])  # noqa: F821
@login_required
def new_token():
    """
    Generate a new API token.
    ---
    tags:
      - API Tokens
    security:
      - ApiKeyAuth: []
    parameters:
      - in: query
        name: name
        type: string
        required: false
        description: Name of the token.
    responses:
      200:
        description: Token generated successfully.
        schema:
          type: object
          properties:
            token:
              type: string
              description: The generated API token.
    """
    try:
        tenants = UserTenantService.query(user_id=current_user.id)
        if not tenants:
            return get_data_error_result(message="租户不存在！")

        tenant_id = [tenant for tenant in tenants if tenant.role == "owner"][0].tenant_id
        obj = {
            "tenant_id": tenant_id,
            "token": generate_confirmation_token(tenant_id),
            "beta": generate_confirmation_token(generate_confirmation_token(tenant_id)).replace("ragflow-", "")[:32],
            "create_time": current_timestamp(),
            "create_date": datetime_format(datetime.now()),
            "update_time": None,
            "update_date": None,
        }

        if not APITokenService.save(**obj):
            return get_data_error_result(message="新建对话助手失败！")

        return get_json_result(data=obj)
    except Exception as e:
        return server_error_response(e)


@manager.route("/token_list", methods=["GET"])  # noqa: F821
@login_required
def token_list():
    """
    List all API tokens for the current user.
    ---
    tags:
      - API Tokens
    security:
      - ApiKeyAuth: []
    responses:
      200:
        description: List of API tokens.
        schema:
          type: object
          properties:
            tokens:
              type: array
              items:
                type: object
                properties:
                  token:
                    type: string
                    description: The API token.
                  name:
                    type: string
                    description: Name of the token.
                  create_time:
                    type: string
                    description: Token creation time.
    """
    try:
        tenants = UserTenantService.query(user_id=current_user.id)
        if not tenants:
            return get_data_error_result(message="租户不存在！")

        tenant_id = [tenant for tenant in tenants if tenant.role == "owner"][0].tenant_id
        objs = APITokenService.query(tenant_id=tenant_id)
        objs = [o.to_dict() for o in objs]
        for o in objs:
            if not o["beta"]:
                o["beta"] = generate_confirmation_token(generate_confirmation_token(tenants[0].tenant_id)).replace("ragflow-", "")[:32]
                APITokenService.filter_update([APIToken.tenant_id == tenant_id, APIToken.token == o["token"]], o)
        return get_json_result(data=objs)
    except Exception as e:
        return server_error_response(e)


@manager.route("/token/<token>", methods=["DELETE"])  # noqa: F821
@login_required
def rm(token):
    """
    Remove an API token.
    ---
    tags:
      - API Tokens
    security:
      - ApiKeyAuth: []
    parameters:
      - in: path
        name: token
        type: string
        required: true
        description: The API token to remove.
    responses:
      200:
        description: Token removed successfully.
        schema:
          type: object
          properties:
            success:
              type: boolean
              description: Deletion status.
    """
    APITokenService.filter_delete([APIToken.tenant_id == current_user.id, APIToken.token == token])
    return get_json_result(data=True)


@manager.route("/config", methods=["GET"])  # noqa: F821
def get_config():
    """
    Get system configuration.
    ---
    tags:
        - System
    responses:
        200:
            description: Return system configuration
            schema:
                type: object
                properties:
                    registerEnable:
                        type: integer 0 means disabled, 1 means enabled
                        description: Whether user registration is enabled
    """
    return get_json_result(data={"registerEnabled": settings.REGISTER_ENABLED})


# 超级用户权限验证装饰器
def superuser_required(f):
    """
    装饰器：验证当前用户是否为超级用户
    """

    @wraps(f)
    @login_required
    def decorated_function(*args, **kwargs):
        if not current_user.is_superuser:
            return get_json_result(code=settings.RetCode.PERMISSION_ERROR, message="超级用户权限不足")
        return f(*args, **kwargs)

    return decorated_function


# 系统管理相关API端点
@manager.route("/users", methods=["GET"])  # noqa: F821
@superuser_required
def list_users():
    """
    获取用户列表 - 仅限超级用户
    仅返回（主键ID、租户ID、用户名称、邮箱、状态、是否超级用户、创建时间、更新时间）
    ---
    tags:
      - System Management
    security:
      - ApiKeyAuth: []
    parameters:
      - in: query
        name: page
        type: integer
        default: 1
        description: 页码
      - in: query
        name: size
        type: integer
        default: 20
        description: 每页大小
      - in: query
        name: search
        type: string
        description: 搜索关键词 (昵称、邮箱或ID)
      - in: query
        name: status
        type: string
        enum: ['1', '0', 'all']
        default: 'all'
        description: 状态筛选
    responses:
      200:
        description: 用户列表获取成功
        schema:
          type: object
          properties:
            data:
              type: object
              properties:
                users:
                  type: array
                  items:
                    type: object
                    properties:
                      id:
                        type: string
                        description: 主键ID
                      tenant_id:
                        type: string
                        description: 租户ID
                      nickname:
                        type: string
                        description: 用户名称
                      email:
                        type: string
                        description: 邮箱
                      status:
                        type: string
                        description: 状态
                      is_superuser:
                        type: boolean
                        description: 是否超级用户
                      create_time:
                        type: integer
                        description: 创建时间戳
                      update_time:
                        type: integer
                        description: 更新时间戳
                total:
                  type: integer
                page:
                  type: integer
                size:
                  type: integer
    """
    try:
        # 参数验证和处理
        try:
            page = max(1, int(request.args.get("page", 1)))
            size = max(1, min(100, int(request.args.get("size", 20))))  # 限制最大每页100条
        except ValueError:
            return get_json_result(code=settings.RetCode.ARGUMENT_ERROR, message="页码和每页大小必须为正整数")

        search = request.args.get("search", "").strip()
        status = request.args.get("status", "all")

        # 状态参数验证
        if status not in ["1", "0", "all"]:
            return get_json_result(code=settings.RetCode.ARGUMENT_ERROR, message="状态参数必须为 '1', '0' 或 'all'")

        users, total = UserService.get_users_for_admin(page, size, search, status)

        # 构建返回数据，只包含指定字段
        user_list = []
        for user in users:
            user_dict = user.__dict__["__data__"]  # 获取原始数据
            user_data = {
                "id": user_dict.get("id"),
                "tenant_id": user_dict.get("tenant_id"),  # 租户ID就是用户ID
                "nickname": user_dict.get("nickname"),
                "email": user_dict.get("email"),
                "status": user_dict.get("status"),
                "is_superuser": user_dict.get("is_superuser"),
                "create_time": user_dict.get("create_time"),
                "update_time": user_dict.get("update_time"),
            }
            user_list.append(user_data)

        return get_json_result(
            data={
                "users": user_list,
                "total": total,
                "page": page,
                "size": size,
                "total_pages": (total + size - 1) // size,  # 计算总页数
            }
        )
    except Exception as e:
        return server_error_response(e)


@manager.route("/users/<user_id>", methods=["PUT"])  # noqa: F821
@superuser_required
@validate_request("nickname")
def update_user(user_id):
    """
    更新用户信息 - 仅限超级用户
    ---
    tags:
      - System Management
    security:
      - ApiKeyAuth: []
    parameters:
      - in: path
        name: user_id
        type: string
        required: true
        description: 用户ID
      - in: body
        name: body
        description: 用户信息
        required: true
        schema:
          type: object
          properties:
            nickname:
              type: string
              description: 昵称
              minLength: 1
              maxLength: 100
            status:
              type: string
              enum: ['1', '0']
              description: 状态 (1: 激活, 0: 禁用)
    responses:
      200:
        description: 用户信息更新成功
      400:
        description: 参数错误
      404:
        description: 用户不存在
    """
    try:
        data = request.get_json()

        # 参数验证
        nickname = data.get("nickname", "").strip()
        status = data.get("status")

        if not nickname or len(nickname) > 100:
            return get_json_result(code=settings.RetCode.ARGUMENT_ERROR, message="昵称不能为空且长度不能超过100个字符")

        # 状态参数验证（如果提供了状态）
        if status is not None and status not in ["1", "0"]:
            return get_json_result(code=settings.RetCode.ARGUMENT_ERROR, message="状态参数必须为 '1' (激活) 或 '0' (禁用)")

        # 检查用户是否存在
        existing_user = UserService.filter_by_id(user_id)
        if not existing_user:
            return get_json_result(code=settings.RetCode.DATA_ERROR, message="用户不存在")

        # 防止超级用户禁用自己
        if user_id == current_user.id and status == "0":
            return get_json_result(code=settings.RetCode.PERMISSION_ERROR, message="不能禁用自己的账户")

        # 防止禁用其他超级用户（可选的安全措施）
        if existing_user.is_superuser and status == "0" and user_id != current_user.id:
            return get_json_result(code=settings.RetCode.PERMISSION_ERROR, message="不能禁用其他超级用户账户")

        # 准备更新数据
        update_data = {"nickname": nickname}
        if status is not None:
            update_data["status"] = status

        UserService.update_user(user_id, update_data)
        return get_json_result(message="用户信息更新成功")
    except Exception as e:
        logging.error(f"更新用户信息失败: {e}")
        return get_json_result(code=settings.RetCode.SERVER_ERROR, message="更新失败，请稍后重试")


@manager.route("/users/<user_id>/status", methods=["PUT"])  # noqa: F821
@superuser_required
@validate_request("status")
def update_user_status(user_id):
    """
    更新用户状态 - 仅限超级用户
    ---
    tags:
      - System Management
    security:
      - ApiKeyAuth: []
    parameters:
      - in: path
        name: user_id
        type: string
        required: true
        description: 用户ID
      - in: body
        name: body
        description: 状态信息
        required: true
        schema:
          type: object
          properties:
            status:
              type: string
              enum: ['1', '0']
              description: 状态 (1: 激活, 0: 禁用)
    responses:
      200:
        description: 用户状态更新成功
      400:
        description: 参数错误
      404:
        description: 用户不存在
      403:
        description: 权限不足
    """
    try:
        data = request.get_json()
        status = data.get("status")

        # 状态参数验证
        if status not in ["1", "0"]:
            return get_json_result(code=settings.RetCode.ARGUMENT_ERROR, message="状态参数必须为 '1' (激活) 或 '0' (禁用)")

        # 检查用户是否存在
        target_user = UserService.filter_by_id(user_id)
        if not target_user:
            return get_json_result(code=settings.RetCode.DATA_ERROR, message="用户不存在")

        # 防止超级用户禁用自己
        if user_id == current_user.id and status == "0":
            return get_json_result(code=settings.RetCode.PERMISSION_ERROR, message="不能禁用自己的账户")

        # 防止禁用其他超级用户（可选的安全措施）
        if target_user.is_superuser and status == "0" and user_id != current_user.id:
            return get_json_result(code=settings.RetCode.PERMISSION_ERROR, message="不能禁用其他超级用户账户")

        result = UserService.update_user(user_id, {"status": status})
        if result:
            status_text = "激活" if status == "1" else "禁用"
            return get_json_result(message=f"用户状态已更新为{status_text}")
        else:
            return get_json_result(code=settings.RetCode.SERVER_ERROR, message="状态更新失败，请稍后重试")
    except Exception as e:
        return server_error_response(e)


@manager.route("/knowledgebases", methods=["GET"])  # noqa: F821
@superuser_required
def list_knowledgebases():
    """
    获取知识库列表 - 仅限超级用户
    ---
    tags:
      - System Management
    security:
      - ApiKeyAuth: []
    parameters:
      - in: query
        name: page
        type: integer
        default: 1
        description: 页码
      - in: query
        name: size
        type: integer
        default: 20
        description: 每页大小
      - in: query
        name: search
        type: string
        description: 搜索关键词 (名称或描述)
      - in: query
        name: status
        type: string
        enum: ['1', '0', 'all']
        default: 'all'
        description: 状态筛选
      - in: query
        name: creator
        type: string
        description: 创建者筛选
    responses:
      200:
        description: 知识库列表获取成功
        schema:
          type: object
          properties:
            data:
              type: object
              properties:
                knowledgebases:
                  type: array
                  items:
                    type: object
                total:
                  type: integer
                page:
                  type: integer
                size:
                  type: integer
    """
    try:
        # 参数验证和处理
        try:
            page = max(1, int(request.args.get("page", 1)))
            size = max(1, min(100, int(request.args.get("size", 20))))  # 限制最大每页100条
        except ValueError:
            return get_json_result(code=settings.RetCode.ARGUMENT_ERROR, message="页码和每页大小必须为正整数")

        search = request.args.get("search", "").strip()
        status = request.args.get("status", "all")
        creator = request.args.get("creator", "").strip()

        # 状态参数验证
        if status not in ["1", "0", "all"]:
            return get_json_result(code=settings.RetCode.ARGUMENT_ERROR, message="状态参数必须为 '1', '0' 或 'all'")

        kbs, total = KnowledgebaseService.get_knowledgebases_for_admin(page, size, search, status, creator)

        # 处理知识库数据，添加额外信息
        kb_list = []
        for kb in kbs:
            kb_dict = kb.to_dict()
            # 添加创建者昵称
            kb_dict["creator_name"] = kb.user.nickname
            kb_list.append(kb_dict)

        return get_json_result(
            data={
                "knowledgebases": kb_list,
                "total": total,
                "page": page,
                "size": size,
                "total_pages": (total + size - 1) // size,  # 计算总页数
            }
        )
    except Exception as e:
        return server_error_response(e)

@manager.route("/knowledgebases/<kb_id>/status", methods=["PUT"])  # noqa: F821
@superuser_required
@validate_request("status")
def update_knowledgebase_status(kb_id):
    """
    更新知识库状态 - 仅限超级用户
    ---
    tags:
      - System Management
    security:
      - ApiKeyAuth: []
    parameters:
      - in: path
        name: kb_id
        type: string
        required: true
        description: 知识库ID
      - in: body
        name: body
        description: 状态信息
        required: true
        schema:
          type: object
          properties:
            status:
              type: string
              enum: ['1', '0']
              description: 状态 (1: 激活, 0: 禁用)
    responses:
      200:
        description: 知识库状态更新成功
      400:
        description: 参数错误
      404:
        description: 知识库不存在
    """
    try:
        # 参数验证
        if not kb_id or not kb_id.strip():
            return get_json_result(code=settings.RetCode.ARGUMENT_ERROR, message="知识库ID不能为空")

        data = request.get_json()
        status = data.get("status")

        # 状态参数验证
        if status not in ["1", "0"]:
            return get_json_result(code=settings.RetCode.ARGUMENT_ERROR, message="状态参数必须为 '1' (激活) 或 '0' (禁用)")

        # 检查知识库是否存在
        kb_detail = KnowledgebaseService.get_knowledgebase_detail(kb_id.strip())
        if not kb_detail:
            return get_json_result(code=settings.RetCode.DATA_ERROR, message="知识库不存在")

        result = KnowledgebaseService.update_knowledgebase_status(kb_id.strip(), status)
        if result:
            status_text = "激活" if status == "1" else "禁用"
            return get_json_result(message=f"知识库状态已更新为{status_text}")
        else:
            return get_json_result(code=settings.RetCode.SERVER_ERROR, message="状态更新失败，请稍后重试")
    except Exception as e:
        return server_error_response(e)

@manager.route("/users/<user_id>/tenant-details", methods=["GET"])  # noqa: F821
@superuser_required
def get_user_tenant_details(user_id):
    """
    获取用户租户详情 - 仅限超级用户
    展示该用户对应租户团队下的租户信息
    ---
    tags:
      - System Management
    security:
      - ApiKeyAuth: []
    parameters:
      - in: path
        name: user_id
        type: string
        required: true
        description: 用户ID
      - in: query
        name: page
        type: integer
        default: 1
        description: 页码
      - in: query
        name: size
        type: integer
        default: 20
        description: 每页大小
    responses:
      200:
        description: 租户详情获取成功
        schema:
          type: object
          properties:
            data:
              type: object
              properties:
                tenants:
                  type: array
                  items:
                    type: object
                    properties:
                      id:
                        type: string
                        description: 主键ID
                      tenant_id:
                        type: string
                        description: 租户ID
                      nickname:
                        type: string
                        description: 用户名称
                      email:
                        type: string
                        description: 邮箱
                      status:
                        type: string
                        description: 状态
                      join_time:
                        type: integer
                        description: 加入时间戳
                total:
                  type: integer
                page:
                  type: integer
                size:
                  type: integer
      400:
        description: 参数错误
      404:
        description: 用户不存在
    """
    try:
        # 参数验证
        if not user_id or not user_id.strip():
            return get_json_result(code=settings.RetCode.ARGUMENT_ERROR, message="用户ID不能为空")

        # 参数验证和处理
        try:
            page = max(1, int(request.args.get("page", 1)))
            size = max(1, min(100, int(request.args.get("size", 20))))
        except ValueError:
            return get_json_result(code=settings.RetCode.ARGUMENT_ERROR, message="页码和每页大小必须为正整数")

        # 检查用户是否存在
        user = UserService.filter_by_id(user_id.strip())
        if not user:
            return get_json_result(code=settings.RetCode.DATA_ERROR, message="用户不存在")

        # 获取租户详情
        users, total = UserService.get_user_tenant_details(user_id.strip(), page, size)

        # 构建返回数据
        tenant_list = []
        for user in users:
            tenant_data = {
                "id": user.id,
                "tenant_id": user.usertenant.tenant_id,
                "nickname": user.nickname,
                "email": user.email,
                "status": user.usertenant.status,
                "join_time": user.usertenant.join_time,
            }
            tenant_list.append(tenant_data)

        return get_json_result(
            data={
                "tenants": tenant_list,
                "total": total,
                "page": page,
                "size": size,
                "total_pages": (total + size - 1) // size,
            }
        )
    except Exception as e:
        return server_error_response(e)

@manager.route("/users/<user_id>/team-details", methods=["GET"])  # noqa: F821
@superuser_required
def get_user_team_details(user_id):
    """
    获取用户团队详情 - 仅限超级用户
    展示用户加入的团队信息
    ---
    tags:
      - System Management
    security:
      - ApiKeyAuth: []
    parameters:
      - in: path
        name: user_id
        type: string
        required: true
        description: 用户ID
      - in: query
        name: page
        type: integer
        default: 1
        description: 页码
      - in: query
        name: size
        type: integer
        default: 20
        description: 每页大小
    responses:
      200:
        description: 团队详情获取成功
        schema:
          type: object
          properties:
            data:
              type: object
              properties:
                teams:
                  type: array
                  items:
                    type: object
                    properties:
                      team_name:
                        type: string
                        description: 团队名称
                      role:
                        type: string
                        description: 在该团队的角色
                      team_member_count:
                        type: integer
                        description: 团队总人数
                      team_create_time:
                        type: integer
                        description: 团队创建时间戳
                      join_time:
                        type: integer
                        description: 加入团队的时间戳
                total:
                  type: integer
                page:
                  type: integer
                size:
                  type: integer
      400:
        description: 参数错误
      404:
        description: 用户不存在
    """
    try:
        # 参数验证
        if not user_id or not user_id.strip():
            return get_json_result(code=settings.RetCode.ARGUMENT_ERROR, message="用户ID不能为空")

        # 参数验证和处理
        try:
            page = max(1, int(request.args.get("page", 1)))
            size = max(1, min(100, int(request.args.get("size", 20))))
        except ValueError:
            return get_json_result(code=settings.RetCode.ARGUMENT_ERROR, message="页码和每页大小必须为正整数")

        # 检查用户是否存在
        user = UserService.filter_by_id(user_id.strip())
        if not user:
            return get_json_result(code=settings.RetCode.DATA_ERROR, message="用户不存在")

        # 获取团队详情
        teams, total = UserService.get_user_team_details(user_id.strip(), page, size)

        return get_json_result(
            data={
                "teams": teams,
                "total": total,
                "page": page,
                "size": size,
                "total_pages": (total + size - 1) // size,
            }
        )
    except Exception as e:
        return server_error_response(e)
