{"components": {"begin": {"obj": {"component_name": "<PERSON><PERSON>", "params": {"prologue": "Hi there!"}}, "downstream": ["answer:0"], "upstream": []}, "answer:0": {"obj": {"component_name": "Answer", "params": {}}, "downstream": ["retrieval:0"], "upstream": ["begin", "generate:0", "switch:0"]}, "retrieval:0": {"obj": {"component_name": "Retrieval", "params": {"similarity_threshold": 0.2, "keywords_similarity_weight": 0.3, "top_n": 6, "top_k": 1024, "rerank_id": "BAAI/bge-reranker-v2-m3", "kb_ids": ["869a236818b811ef91dffa163e197198"], "empty_response": "Sorry, knowledge base has noting related information."}}, "downstream": ["relevant:0"], "upstream": ["answer:0"]}, "relevant:0": {"obj": {"component_name": "Relevant", "params": {"llm_id": "deepseek-chat", "temperature": 0.02, "yes": "generate:0", "no": "message:0"}}, "downstream": ["message:0", "generate:0"], "upstream": ["retrieval:0"]}, "generate:0": {"obj": {"component_name": "Generate", "params": {"llm_id": "deepseek-chat", "prompt": "You are an intelligent assistant. Please answer the question based on content of knowledge base. When all knowledge base content is irrelevant to the question, your answer must include the sentence \"The answer you are looking for is not found in the knowledge base!\". Answers need to consider chat history.\n      Knowledge base content is as following:\n      {input}\n      The above is the content of knowledge base.", "temperature": 0.2}}, "downstream": ["answer:0"], "upstream": ["relevant:0"]}, "message:0": {"obj": {"component_name": "Message", "params": {"messages": ["Sorry, I don't know. Please leave your contact, our experts will contact you later. What's your e-mail/phone/wechat?", "I'm an AI bot and not quite sure about this question. Please leave your contact, our experts will contact you later. What's your e-mail/phone/wechat?", "Can't find answer in my knowledge base. Please leave your contact, our experts will contact you later. What's your e-mail/phone/wechat?"]}}, "downstream": ["answer:0"], "upstream": ["relevant:0"]}}, "history": [], "path": [], "messages": [], "reference": {}, "answer": []}